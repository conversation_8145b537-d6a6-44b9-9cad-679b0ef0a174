# 介绍
这是一个 figma plugin 工程，用于选中设计稿，将设计稿中的元素，转换为私有组件库 ABC UI 的描述信息。转换后的信息，将用于给 LLM 生成代码。
该工程是一个 pnpm monorepo，包含如下子工程：
- @abc-figma/figma-ui-generator: 转换工具，位于 packages/figma-ui-generator
- @abc-figma/vue-playground: vue2.7 的演示项目，位于 packages/vue-playground

# 快速启动
```bash
pnpm install
pnpm ui-transfer // 启动 @abc-figma/ui-transfer 开发(热更新)
pnpm figma // 启动插件开发(热更新)
pnpm ui // 可选，一个 playground，集成了 @abc/ui-pc，可以验证生成的 prompt 的效果
pnpm doc-gen // 根据本地的 abc-fed-common 工程生成组件的 json 文件，使用前需要先 把根目录下的 `.env.example` 复制并重命名为 `.env`，修改其中的变量值ABC_FED_COMMON_DIR、ABC_PC_DIR为自己电脑的路径
```

# 调试
Figma 导入插件

1. 打开 Figma 客户端，顶部的文件菜单，选择 `Plugins` → `Development` → `Import plugin from manifest`

![Figma 导入插件](./docs/figma-import-plugin.png)

2. 选择工程目录下的 packages/figma-ui-generator/manifest.json

![Figma 选择 manifest 文件](./docs/figma-import-manifset.png)

3. 打开 ABC UI Generator 即可调试
