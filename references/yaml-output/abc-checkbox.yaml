name: AbcCheckbox
description: 多选框，用于在多个选项中选择
usage: "<template> <abc-flex vertical :gap=\"16\"> <abc-form is-excel item-no-margin> <abc-descriptions :column=\"2\" grid bordered :label-width=\"200\"> <abc-descriptions-item label=\"type\" content-padding=\"0\"> <abc-select v-model=\"type\"> <abc-option label=\"boolean\" value=\"boolean\"></abc-option> <abc-option label=\"number\" value=\"number\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"shape\" content-padding=\"0\"> <abc-select v-model=\"shape\"> <abc-option label=\"square\" value=\"square\"></abc-option> <abc-option label=\"round\" value=\"round\"></abc-option> <abc-option label=\"ring\" value=\"ring\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"disabled\"> <abc-switch v-model=\"disabled\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"control\"> <abc-switch v-model=\"control\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"indeterminate\"> <abc-switch v-model=\"indeterminate\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"noBorder\"> <abc-switch v-model=\"noBorder\"></abc-switch> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card padding-size=\"large\"> <abc-checkbox v-model=\"value\" :disabled=\"disabled\" :control=\"control\" :type=\"type\" :indeterminate=\"indeterminate\" :no-border=\"noBorder\" :shape=\"shape\" @click=\"handleClick\" ></abc-checkbox> </abc-card> </abc-flex> </template> <script> export default { data() { return { value: false, disabled: false, control: false, type: 'boolean', indeterminate: false, noBorder: false, shape: 'square', } }, methods: { handleClick() { if (this.control) { window.alert('通过click事件控制') this.value = !this.value } }, }, } </script>"
props:
  - name: value
    default: "false"
  - name: checked
    description: 当前的选中状态
  - name: disabled
    description: 是否禁用 默认为false
    default: "false"
  - name: control
    description: 是否受控制 默认为false 设置为true点击checkbox只会正常抛出点击事件
    default: "false"
  - name: type
    description: 绑定值的类型 boolean 或者 number
    default: "'boolean'"
  - name: customerStyle
    description: 自定义样式
    default: "{}"
  - name: label
  - name: indeterminate
    description: 是否使用半选状态
    default: "false"
  - name: noBorder
    description: 是否隐藏边框
    default: "false"
  - name: shape
    description: 形状 支持square和round和ring
    default: "'square'"
slots:
  - default
events:
  - click
  - input
  - change
