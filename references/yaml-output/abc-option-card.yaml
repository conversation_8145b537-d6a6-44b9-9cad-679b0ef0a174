name: AbcOptionCard
description: 选项卡组件，用于展示选项卡内容
usage: "<template> <abc-flex wrap=\"wrap\" :gap=\"16\"> <abc-option-card v-model=\"selected\" icon=\"n-xls-fill\" selectable=\"icon-outside\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" data-cy=\"storybook-test-option-card\" ></abc-option-card> <abc-option-card v-model=\"selected\" icon=\"n-xls-fill\" selectable=\"icon-outside\" title=\"这是标题\" theme=\"success\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <abc-option-card v-model=\"selected\" icon=\"n-xls-fill\" selectable=\"icon-inside\" theme=\"success\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <abc-option-card v-model=\"selected\" icon=\"n-xls-fill\" selectable=\"false\" theme=\"success\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <abc-option-card v-model=\"selected\" icon=\"n-xls-fill\" :selectable=\"false\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <abc-option-card v-model=\"selected\" disabled icon=\"n-xls-fill\" selectable=\"icon-outside\" theme=\"success\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <abc-option-card v-model=\"selected\" disabled icon=\"n-xls-fill\" selectable=\"icon-inside\" theme=\"success\" title=\"这是标题\" description=\"这是内容这是内容这是内容\" @change=\"change\" @click=\"click\" ></abc-option-card> <div>{{ selected }}</div> <div>事件名称: {{ eventName }}</div> </abc-flex> </template> <script> export default { data() { return { selected: true, eventName: '', } }, methods: { click() { this.eventName = 'click' }, change(e) { this.eventName = 'change' }, }, } </script>"
props:
  - name: value
    description: 控制是否是勾选状态
    default: "false"
  - name: icon
    description: 图标-同abc-icon图标
    default: "''"
  - name: showIcon
    description: 是否显示icon图标
    default: "true"
  - name: title
    description: 标题
    default: "''"
  - name: description
    description: 内容
    default: "''"
  - name: disabled
    description: 是否禁用
    default: "false"
  - name: theme
    description: 主题 primary 和 success 默认 primary
    default: "'primary'"
  - name: width
    description: 宽度 单位px
  - name: height
    description: 高度 单位px
  - name: selectable
    description: 勾选风格 'icon-inside', 'icon-outside', 'default', 'false'
    default: "'default'"
  - name: titleConfig
    description: 标题配置-同abcText
    default: |-
      {
          size: 'large',
      }
  - name: descriptionConfig
    description: 描述配置-同abcText
    default: |-
      {
          theme: 'gray-light',
          size: 'normal',
      }
slots:
  - name: icon
    description: icon插槽
  - name: title
    description: title插槽
  - name: description
    description: description插槽
events:
  - input
  - click
  - change
