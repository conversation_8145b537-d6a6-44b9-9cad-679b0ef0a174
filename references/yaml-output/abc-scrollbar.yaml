name: AbcScrollbar
description: 滚动条组件，用于包裹滚动区域，会默认右边留出来10px; 自带滚动条样式
usage: "<template> <div class=\"quick-list\" style=\"width: 800px; height: 400px; border: 1px solid #000\" > <abc-scrollbar> <div v-for=\"i in 10\" :key=\"i\" style=\"height: 100px\">{{ i }}</div> </abc-scrollbar> </div> </template> <script> export default { data() { return { value: 0, isBlock: false, isDisabled: false, isCancel: false, } }, } </script>"
props:
  - name: resident
    description: resident 是否常驻滚动条
    default: "false"
  - name: paddingSize
    description: "none 0 default 24px; small: 16px; tiny: 10px"
    default: "'default'"
slots:
  - default
