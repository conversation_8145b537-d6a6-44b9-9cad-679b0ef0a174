name: BizProjectMultipleSelect
description: ""
usage: "<template> <biz-project-multiple-select :fetch-fn=\"fetchData\" ></biz-project-multiple-select> </template> <script> export default { methods: { fetchData({ keyword }) { return new Promise((resolve) => { console.log('keyword', keyword) // eslint-disable-next-line abc/no-timer-id setTimeout(() => { const data = { list: [ { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff00000000348ba16bc84d0002', id: 'ffffffff00000000348ba16bc84d0002', isSell: 1, disable: 0, v2DisableStatus: 0, displayName: '检查打印测试', displaySpec: '部位', name: '检查打印测试', packageCostPrice: 45, packagePrice: 50, packageUnit: '部位', pieceNum: 1, shortId: '300000905085382', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 2, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 45, chainPackagePrice: 50, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff00000000348ba16bc84d0002', goodsType: 3, isDummy: 0, medicineNum: '300000905085382', name: '检查打印测试', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff00000000348cfb1a8866c000', id: 'ffffffff00000000348cfb1a8866c000', isSell: 1, disable: 0, extendInfo: { executeDepartments: [ { id: 'ffffffff0000000034db191ee2ce8000', name: 'Bubble-CT', departmentAddress: '', tagId: 'ffffffff0000000034db191fe2ce8000', type: 1, isDefault: 0, mobile: '', beds: null, customId: 'ZN0030', isClinical: 3, mainMedical: '', mainMedicalName: null, mainMedicalCode: null, status: 1, chainId: '6a869c22abee4ffbaef3e527bbb70aeb', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', secondMedical: '', secondMedicalName: null, secondMedicalCode: null, startDate: null, principal: '', created: '2024-10-21T08:59:35Z', lastModified: '2024-10-21T08:59:44Z', }, ], }, v2DisableStatus: 0, displayName: '检查无医保', displaySpec: '次', name: '检查无医保', packageCostPrice: 1, packagePrice: 12, packageUnit: '次', pieceNum: 1, shortId: '300000905085395', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, bizRelevantId: '3786771004760129536', deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 1, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 1, chainPackagePrice: 12, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, shebaoPayMode: 0, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff00000000348cfb1a8866c000', goodsType: 3, payMode: 0, isDummy: 0, medicineNum: '300000905085395', name: '检查无医保', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff000000003492625a48f48000', id: 'ffffffff000000003492625a48f48000', isSell: 1, disable: 0, v2DisableStatus: 0, displayName: '检查发疯', displaySpec: '次', name: '检查发疯', packageCostPrice: 0, packagePrice: 12, packageUnit: '次', pieceNum: 1, shortId: '300000905085421', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, bizRelevantId: '3786771004760129536', deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 1, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 0, chainPackagePrice: 12, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, shebaoPayMode: 0, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff000000003492625a48f48000', goodsType: 3, payMode: 0, isDummy: 0, medicineNum: '300000905085421', name: '检查发疯', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 3, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff00000000349d48e10a618000', id: 'ffffffff00000000349d48e10a618000', isSell: 1, disable: 0, v2DisableStatus: 0, displayName: '检查1012', displaySpec: '项', name: '检查1012', packageCostPrice: 0, packagePrice: 1, packageUnit: '项', pieceNum: 1, shortId: '300000905086105', subType: 2, type: 3, typeId: 21, customTypeId: '1029067', customTypeName: '检查二级分类', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 1, gender: 2, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 0, chainPackagePrice: 1, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, shebaoPayMode: 0, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff00000000349d48e10a618000', goodsType: 3, payMode: 0, isDummy: 0, medicineNum: '300000905086105', name: '检查1012', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff00000000349936f1c9db0000', id: 'ffffffff00000000349936f1c9db0000', isSell: 1, disable: 0, v2DisableStatus: 0, displayName: '检查啊', displaySpec: '项', name: '检查啊', packageCostPrice: 10, packagePrice: 10000, packageUnit: '项', pieceNum: 1, shortId: '300000905086043', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 2, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 10, chainPackagePrice: 10000, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff00000000349936f1c9db0000', goodsType: 3, isDummy: 0, medicineNum: '300000905086043', name: '检查啊', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff000000003499a1bf49f60000', id: 'ffffffff000000003499a1bf49f60000', isSell: 1, disable: 0, v2DisableStatus: 0, displayName: '打印测试检查', displaySpec: '次', name: '打印测试检查', packageCostPrice: 100, packagePrice: 110, packageUnit: '次', pieceNum: 1, shortId: '300000905086062', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, bizRelevantId: '3786771004760129536', deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 2, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 100, chainPackagePrice: 110, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, shebaoPayMode: 0, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 0, feeTypeId: '21', shebao: { goodsId: 'ffffffff000000003499a1bf49f60000', goodsType: 3, payMode: 0, isDummy: 0, medicineNum: '300000905086062', name: '打印测试检查', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, { goodsVersion: 0, extendSpec: '10', dismounting: 0, goodsId: 'ffffffff0000000034861167a7328000', id: 'ffffffff0000000034861167a7328000', isSell: 1, disable: 0, extendInfo: {}, v2DisableStatus: 0, displayName: '检查项目CT111', displaySpec: '次', name: '检查项目CT111', packageCostPrice: 1, packagePrice: 12, packageUnit: '次', pieceNum: 1, shortId: '300000905085266', subType: 2, type: 3, typeId: 21, customTypeId: '0', combineType: 0, bizExtensions: { itemCategory: '1', }, pharmacyType: 0, pharmacyNo: 0, deviceInnerFlag: 1, deviceType: 1, cloudSupplierFlag: 0, coopFlag: 0, inspectionSite: 0, status: 0, noStocks: true, organId: '6a869c22abee4ffbaef3e527bbb70aeb', chainPackageCostPrice: 0, chainPackagePrice: 12, medicalFeeGrade: 0, inorderConfig: 0, sellConfig: 0, disableSell: 0, needExecutive: 0, hospitalNeedExecutive: 0, sourceFlag: 1, shebaoPayMode: 0, pieceCount: null, packageCount: null, stockPieceCount: null, stockPackageCount: null, spuGoodsId: '0', feeComposeType: 20, feeTypeId: '0', shebao: { goodsId: 'ffffffff0000000034861167a7328000', goodsType: 3, payMode: 0, isDummy: 0, medicineNum: '300000905085266', name: '检查项目CT111', medicalFeeGrade: 0, }, priceType: 1, dangerIngredient: 0, isPreciousDevice: 0, cMSpec: '', }, ], } resolve(data) }, 300) }) }, }, } </script>"
