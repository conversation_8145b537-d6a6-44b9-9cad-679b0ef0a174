name: AbcBadge
description: 徽标，用于展示未读消息数量等
usage: "<template> <abc-flex vertical :gap=\"16\"> <abc-form is-excel item-no-margin> <abc-descriptions :column=\"2\" grid bordered :label-width=\"200\"> <abc-descriptions-item label=\"value\" content-padding=\"0\"> <abc-input v-model=\"value\" type=\"number\"></abc-input> </abc-descriptions-item> <abc-descriptions-item label=\"theme\" content-padding=\"0\"> <abc-select v-model=\"theme\"> <abc-option label=\"text\" value=\"text\"></abc-option> <abc-option label=\"danger\" value=\"danger\"></abc-option> <abc-option label=\"warn\" value=\"warn\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"variant\" content-padding=\"0\"> <abc-select v-model=\"variant\"> <abc-option label=\"text\" value=\"text\"></abc-option> <abc-option label=\"round\" value=\"round\"></abc-option> <abc-option label=\"dot\" value=\"dot\"></abc-option> <abc-option label=\"count\" value=\"count\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"maxNumber\" content-padding=\"0\"> <abc-input v-model=\"maxNumber\" type=\"number\"></abc-input> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card padding-size=\"small\"> <abc-badge :value=\"value\" :theme=\"theme\" :variant=\"variant\" :max-number=\"+maxNumber\" data-cy=\"storybook-test-default\" > 确定 </abc-badge> </abc-card> </abc-flex> </template> <script> export default { data() { return { value: 1, theme: 'danger', variant: 'round', maxNumber: 99, } }, } </script>"
props:
  - name: value
    default: "0"
  - name: variant
    description: |-
      类型 text 文字徽标 round 圆形徽标 dot 点状徽标 count文字汇总徽标
      text 不设置theme文本颜色默认继承父元素颜色
    default: "'text'"
  - name: theme
    description: |-
      主题 danger 危险; warn 警告; text 文本;
      属性为round和dot时 仅支持danger warn 属性 不传默认为danger
    default: "'text'"
  - name: maxNumber
    description: 是否需要限制最大位数 默认为99 超出设置范围展示xx+
    default: "99"
