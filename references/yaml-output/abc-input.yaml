name: AbcInput
description: 输入框组件，用于输入文本内容
usage: "<template> <div> <abc-flex> <abc-space> <span>disabled状态</span> <abc-switch v-model=\"isDisabled\" @change=\"handleChangeDisabled\" ></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>readonly状态</span> <abc-switch v-model=\"isReadonly\" @change=\"handleChangeReadonly\" ></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>支持清除</span> <abc-switch v-model=\"isClearable\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>只有下边框的输入框</span> <abc-switch v-model=\"isOnlyBottomBorder\" @change=\"handleChangeBorder\" ></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\">normal </abc-radio> <abc-radio label=\"medium\">medium </abc-radio> <abc-radio label=\"large\">large </abc-radio> </abc-radio-group> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>append 插槽</span> <abc-switch v-model=\"showAppendInput\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>appendInner 插槽</span> <abc-switch v-model=\"showAppendInner\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>appendLabel 插槽</span> <abc-switch v-model=\"showAppendLabel\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>cover 插槽</span> <abc-switch v-model=\"showCover\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>prepend 插槽</span> <abc-switch v-model=\"showPrepend\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>loading</span> <abc-switch v-model=\"loading\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>loading位置</span> <abc-radio-group v-model=\"loadingPosition\"> <abc-radio label=\"right\">right</abc-radio> <abc-radio label=\"left\">left</abc-radio> </abc-radio-group> </abc-space> </abc-flex> <div style=\"margin-top: 16px\"> <abc-input v-model=\"currentValue\" :disabled=\"isDisabled\" :readonly=\"isReadonly\" :loading=\"loading\" :loading-position=\"loadingPosition\" :width=\"140\" :only-bottom-border=\"isOnlyBottomBorder\" :size=\"size\" :clearable=\"isClearable\" :placeholder=\"placeholder\" > <span slot=\"append\" v-if=\"showAppendInput\">盒</span> <span slot=\"prepend\" v-if=\"showPrepend\"> <abc-icon icon=\"s-b-scan-line\"></abc-icon> </span> <span slot=\"appendInner\" v-if=\"showAppendInner\">单位</span> <span slot=\"appendLabel\" v-if=\"showAppendLabel\">次数</span> </abc-input> <abc-input v-if=\"showCover\" :size=\"size\"> <abc-flex slot=\"cover\" align=\"center\" justify=\"center\" style=\"height: 100%\" > <abc-text v-abc-title.ellipsis=\"'使用 cover 插槽实现自定义文本效果'\" ></abc-text> </abc-flex> </abc-input> </div> </div> </template> <script> export default { data() { return { currentValue: '', isDisabled: false, isReadonly: false, isClearable: false, isOnlyBottomBorder: false, isClear: false, showAppendLabel: false, showAppendInner: false, showCover: false, readonlyValue: '这是只读 Input', disableValue: '这是禁用 Input', placeholder: 'placeholder', size: '', showAppendInput: false, showPrepend: false, loading: false, loadingPosition: 'left', } }, methods: { handleChangeBorder(val) { if (val) { this.placeholder = '只有下边框的 input' } else { this.placeholder = '' } }, handleChangeReadonly(val) { if (val) { this.placeholder = '只读的 input' } else { this.placeholder = '' } }, handleChangeDisabled(val) { if (val) { this.placeholder = 'disabled的 input' } else { this.placeholder = '' } }, }, } </script>"
props:
  - name: size
    description: 表单尺寸大小
    values:
      - tiny/small/medium/large/huge
    default: "''"
  - name: adaptiveWidth
    description: 是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度
    default: undefined
  - name: value
  - name: type
    description: money/number/phone/number-en-char
    default: "'text'"
  - name: config
    description: 配置项
    default: |-
      {
          min: null, // 最少输入多少位，type 为 number/money 时生效
          max: null, // 最多输入多少位，type 为 number/money 时生效
          formatLength: 0, // 支持小数点后几位，type 为 number/money 时生效
          supportZero: false, // 是否支持零，type 为 number/money 时生效
          supportFraction: false, // 是否支持分数，type 为 number/money 时生效
          supportNegative: false, // 是否支持负数，type 为 number/money 时生效
      }
  - name: width
    description: 设置 input 的宽度，像素值，不带 px 单位
  - name: maxLength
    description: 最大可输入字符数，与原生 input 的 maxLength 一致
    default: "500"
  - name: disallowed
    description: 不允许输入的字符，在 input 事件中替换为''
    default: "null"
  - name: margin
    description: 设置 input 的margin
  - name: placeholder
    description: 占位文本
  - name: focusPlaceholder
    description: focus 时显示的 placeholder
    default: "''"
  - name: hoverPlaceholder
    description: hover 时显示的 placeholder
    default: "''"
  - name: hidePlaceholderWhenFocus
    description: focus 时隐藏 placeholder
    default: "false"
  - name: disabled
    description: 是否禁用
  - name: readonly
    description: 是否只读
  - name: icon
    description: 图标
  - name: inputCustomStyle
    description: 除开 width、margin 可以直接控制 input 样式外，可通过 inputCustomStyle 对 input 进行更多的定制
  - name: tabindex
    description: 与原生 input 一致
  - name: trim
    description: 字符串空格过滤
    default: "false"
  - name: showMaxLengthTips
    description: 是否显示可输入的剩余字符数
    default: "false"
  - name: ignoreComposing
    description: |-
      是否忽略 compositionstart 和 compositionend 之间触发的事件
      为 true 时，会阻止 IME 输入法的输入
    default: "false"
  - name: validateEvent
    description: 是否通知 form-item 进行校验，默认为 true，在自定义组件的场景，需要根据情况使用，例如 abc-date-picker
    default: "true"
  - name: distinguishHalfAngleLength
    description: 是否区分全交半角的长度，与 maxLength 搭配使用，半角占 1 位，全角占 2 位
    default: "false"
  - name: onlyBottomBorder
    description: input只有底部边框
    default: "false"
  - name: clearable
    description: 支持清除，注意指定该属性后，icon 将不会显示
    default: "false"
  - name: focusWhenClear
    description: 点击清除按钮时，是否聚焦，默认为 true
    default: "true"
  - name: loading
    default: "false"
  - name: loadingPosition
    default: "'left'"
slots:
  - prepend
  - appendInner
  - append
  - cover
  - appendLabel
events:
  - up
  - down
  - left
  - right
  - input
  - clear
  - blur
  - focus
  - change
  - enter
  - tab
  - icon-click
  - click
  - keyup
