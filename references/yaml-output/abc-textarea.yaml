name: AbcTextarea
description: 多行文本输入框
usage: "<template> <div> <abc-flex> 禁用状态：<abc-switch v-model=\"isDisabled\"></abc-switch> </abc-flex> <abc-flex style=\"margin-top: 16px\"> 只读状态：<abc-switch v-model=\"isReadonly\"></abc-switch> </abc-flex> <abc-textarea style=\"margin-top: 16px\" v-model=\"value\" show-max-length-tips :disabled=\"isDisabled\" data-cy=\"storybook-test-default\" :readonly=\"isReadonly\" :maxlength=\"10\" placeholder=\"输入模板内容\" ></abc-textarea> </div> </template> <script> export default { data() { return { value: '基础用法', isDisabled: false, isReadonly: false, } }, } </script>"
props:
  - name: size
    description: 表单尺寸大小
    values:
      - tiny/small/medium/large/huge
    default: "''"
  - name: adaptiveWidth
    description: 是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度
    default: undefined
  - name: value
    default: "''"
  - name: rows
    description: 文本区内的可见行数,与原生rows一致
  - name: width
    description: 设置的宽度，像素值，不带px单位，不设置默认100%
  - name: height
    description: 设置的高度，像素值，不带px单位，设置rows后无效
    default: "200"
  - name: maxlength
    description: 最大可输入字符数，与原生maxLength一致
    default: "500"
  - name: showMaxLengthTips
    description: 是否显示可输入的剩余字符数
    default: "false"
  - name: distinguishHalfAngleLength
    description: 是否区分全交半角的长度，与 maxLength 搭配使用，半角占 1 位，全角占 2 位
    default: "false"
  - name: placeholder
    description: 占位文本
    default: "'输入内容'"
  - name: disabled
    description: 是否禁用
  - name: readonly
    description: 是否只读
  - name: inputCustomStyle
    description: 自定义样式
  - name: trim
    description: 去除首尾空格
    default: "false"
events:
  - input
  - blur
  - focus
  - change
  - enter
