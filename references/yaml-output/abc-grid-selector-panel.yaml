name: AbcGridSelectorPanel
description: grid布局面板（展示/选择）组件，用于展示信息及选取信息，
usage: "<template> <div> <abc-space> <abc-checkbox v-model=\"isGroup\"> 分组 </abc-checkbox> <abc-checkbox v-model=\"editable\"> 可编辑 </abc-checkbox> <abc-checkbox v-model=\"showFooter\"> 展示footer </abc-checkbox> </abc-space> <br /> <abc-select-input style=\"margin-top: 12px\" v-model=\"selectInputValue\" :is-empty=\"false\" :visible-popper.sync=\"visibleMultiplePanel\" clearable :editable=\"multiple ? false : editable\" plainPopper :onlyBottomBorder=\"onlyBottomBorder\" @enter=\"handleKeyOperate('enter')\" @left=\"handleKeyOperate('left')\" @right=\"handleKeyOperate('right')\" @up=\"handleKeyOperate('up')\" @down=\"handleKeyOperate('down')\" @clear=\"clear\" > <AbcGridSelectorPanel :multiple=\"multiple\" :labelField=\"labelField\" :valueField=\"valueField\" v-if=\"visibleMultiplePanel\" @clearActive=\"active = ''\" :active=\"active\" v-model=\"selectedValue\" :width=\"500\" :columns=\"4\" :dataSource=\"isGroup ? groupedData : gridData\" @select=\"handleChange\" @close=\"visibleMultiplePanel = false\" > <template v-if=\"showFooter\" #fixed-footer> <abc-flex style=\"width: 100%\" justify=\"end\"> <abc-button size=\"small\" theme=\"default\" variant=\"text\" icon=\"s-b-settings-line\" ></abc-button> </abc-flex> </template> </AbcGridSelectorPanel> </abc-select-input> </div> </template> <script> export default { data() { return { onlyBottomBorder: false, // 是否只展示下边框 visibleMultiplePanel: false, selectedValue: [], selectInputValue: '', active: '', valueField: 'value', labelField: 'name', editable: false, multiple: false, isGroup: false, showFooter: false, gridData: [ { title: 'title 1', list: [ { name: '选项', value: '1', }, { name: '选项', value: '2', }, { name: '选项', value: '3', }, { name: '选项', value: '4', }, { name: '选项', value: '5', }, { name: '选项', value: '6', }, { name: '选项', value: '7', }, { name: '选项', value: '8', }, { name: '选项', value: '9', }, // ... more items ], }, ], groupedData: [ { list: [ { name: '选项1', value: '1', }, { name: '选项2', value: '2', }, ], }, { list: [ { name: '选项3', value: '3', }, { name: '选项4', value: '4', disabled: true, }, { name: '选项5', value: '5', }, { name: '选项6', value: '6', }, { name: '选项7', value: '7', }, { name: '选项8', value: '8', }, ], }, { list: [ { name: '选项9', value: '9', }, { name: '选项10', value: '10', }, ], }, ], } }, methods: { handleChange(value) { console.log('Selected:', value) if (this.multiple) { this.selectInputValue = value.join(' ') return } this.selectInputValue = value this.visibleMultiplePanel = false }, handleKeyOperate(key) { console.log(key) this.active = key }, clear() { if (this.multiple) { this.selectedValue = [] return } this.selectedValue = '' }, }, watch: { selectedValue: { handler(newVal) { console.log(newVal) }, }, }, } </script>"
props:
  - name: dataSource
    description: |-
      数据源
      eg. [{
          title: '',
          list: [
              {label: '测一', value: '测一'},
              {label: '测二', value: '测二'},
          ]
      }]
    default: "[]"
  - name: labelField
    description: 列表展示数据-label字段名
    default: "'label'"
  - name: valueField
    description: 列表展示数据-value字段名
    default: "'value'"
  - name: columns
    description: 列数-默认4列
    default: "4"
  - name: width
    description: 面板宽度-默认352
    default: "''"
  - name: value
    description: 绑定的value，单选模式[String, Number],多选模式[Array]
    default: "null"
  - name: active
    description: 外界动作指令
    default: "''"
  - name: multiple
    description: 多选模式
    default: () => false
slots:
  - fixed-footer
events:
  - clearActive
  - input
  - select
  - close
