name: AbcCard
description: 卡片，用于展示一些信息
usage: "<template> <abc-flex vertical :gap=\"16\"> <abc-form is-excel item-no-margin> <abc-descriptions :column=\"2\" grid bordered :label-width=\"200\"> <abc-descriptions-item label=\"radiusSize\" content-padding=\"0\"> <abc-select v-model=\"radiusSize\"> <abc-option label=\"mini(4px)\" value=\"mini\"></abc-option> <abc-option label=\"small(4px)\" value=\"small\"></abc-option> <abc-option label=\"large(12px)\" value=\"large\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"paddingSize\" content-padding=\"0\"> <abc-select v-model=\"paddingSize\"> <abc-option label=\"none\" value=\"none\"></abc-option> <abc-option label=\"mini(8px)\" value=\"mini\"></abc-option> <abc-option label=\"small(12px)\" value=\"small\"></abc-option> <abc-option label=\"medium(16px)\" value=\"medium\"></abc-option> <abc-option label=\"large(24px)\" value=\"large\"></abc-option> <abc-option label=\"huge(32px)\" value=\"huge\"></abc-option> <abc-option label=\"hugely(40px)\" value=\"hugely\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"background\" content-padding=\"0\"> <abc-select v-model=\"background\"> <abc-option label=\"white\" value=\"white\"></abc-option> <abc-option label=\"gray\" value=\"gray\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"border\"> <abc-switch v-model=\"border\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"shadow\"> <abc-switch v-model=\"shadow\"></abc-switch> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card :border=\"border\" :radius-size=\"radiusSize\" :padding-size=\"paddingSize\" :background=\"background\" :shadow=\"shadow\" data-cy=\"storybook-test-default\" > <abc-layout> <abc-layout-content> <abc-section> <abc-title level=\"1\">优惠券功能介绍</abc-title> <abc-title :bold=\"false\">为什么使用优惠券？</abc-title> <abc-p gray>发放优惠券，可提升顾客复购率</abc-p> <abc-p gray>设置免费领取的优惠券，可吸引新顾客</abc-p> <abc-p gray>设置优惠券使用门槛，可提升客单价</abc-p> </abc-section> <abc-section> <abc-title>优惠券如何发放？</abc-title> <abc-p >设置为可免费领取的优惠券，顾客可在 [微诊所] - [我的优惠券] 中自行领取</abc-p > <abc-p >可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</abc-p > <abc-p >可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</abc-p > </abc-section> <abc-section> <abc-title level=\"1\">为什么使用优惠券？</abc-title> <abc-p>发放优惠券，可提升顾客复购率</abc-p> </abc-section> <abc-section> <abc-title level=\"3\">为什么使用优惠券？</abc-title> <abc-p gray small>发放优惠券，可提升顾客复购率</abc-p> </abc-section> </abc-layout-content> </abc-layout> </abc-card> </abc-flex> </template> <script> export default { data() { return { radiusSize: 'large', paddingSize: 'none', background: 'white', border: true, shadow: false, } }, } </script>"
props:
  - name: radiusSize
    description: 卡片圆角，mini、small、large
    values:
      - mini
      - small
      - large
    default: "'large'"
  - name: paddingSize
    description: |-
      内置卡片padding，
      'none' 0;
      'mini' 8px;
      'small': 12;
      'medium': 16;
      'large': 24;
      'huge': 32;
      'hugely': 40;
    values:
      - none
      - mini
      - small
      - medium
      - large
      - huge
      - hugely
    default: "'none'"
  - name: background
    description: |-
      背景色默认白色，可以指定
      gray 灰底
    values:
      - white
      - gray
      - custom
    default: "'white'"
  - name: border
    description: card 是否有边框
    default: "true"
  - name: shadow
    description: card 是否有阴影
    default: "false"
slots:
  - title
  - default
