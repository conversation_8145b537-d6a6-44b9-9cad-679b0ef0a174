name: AbcQrCode
description: 二维码组件，用于展示二维码
usage: "<template> <div> <abc-input v-model=\"qrContent\" placeholder=\"输入要生成二维码的内容\" :width=\"200\" ></abc-input> <div><abc-qr-code :src=\"qrContent\"></abc-qr-code></div> </div> </template> <script> export default { data() { return { qrContent: '', } }, } </script>"
props:
  - name: loading
    description: 是否展示 Loading
  - name: needRefresh
    description: 是否显示刷新
  - name: src
    description: 需要生成二维码的内容
  - name: width
    description: 二维码宽度
    default: "200"
  - name: margin
    description: 二维码边距
    default: "4"
  - name: customClass
    description: 二维码自定义class
  - name: customTipsClass
    description: 二维码自定义内容区域class
slots:
  - innerText
events:
  - refresh
