name: BizMixedSelectionFilter
description: ""
usage: "<template> <abc-flex vertical :gap=\"20\"> <p>checkbox-button模式</p> <biz-mixed-selection-filter v-model=\"warningKeys\" type=\"checkbox-button\" :options=\"warningList\" @change=\"handleChange\" ></biz-mixed-selection-filter> <p>checkbox模式</p> <biz-mixed-selection-filter v-model=\"warningKeys\" type=\"checkbox\" :options=\"warningList\" @change=\"handleChange\" ></biz-mixed-selection-filter> <p>radio模式</p> <biz-mixed-selection-filter v-model=\"warningKey\" type=\"radio\" :options=\"warningList\" @change=\"handleChange\" ></biz-mixed-selection-filter> </abc-flex> </template> <script> export default { data() { return { warningKey: 'stock', warningKeys: ['profit'], warningList: [ { label: '库存预警', value: 'stock', statisticsNumber: 10, }, { label: '效期预警', value: 'expired', disabled: true, statisticsNumber: 5, }, { label: '毛利异常', value: 'profit', statisticsNumber: 0, }, ], } }, methods: { handleChange(val) { console.log('biz-mixed-selection-filter change', val) }, }, } </script>"
