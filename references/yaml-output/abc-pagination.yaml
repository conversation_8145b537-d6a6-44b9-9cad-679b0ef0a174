name: AbcPagination
description: 分页组件，用于分页展示数据，包含页码、上一页、下一页，指定分页条数等功能
usage: "<template> <div> <h5 style=\"margin: 24px 0 0\">size:normal</h5> <abc-pagination data-cy=\"storybook-test-base1\" :show-total-page=\"true\" show-size :pagination-params=\"pageParams\" :count=\"pageParams.count\" @current-change=\"handlePageChange\" > </abc-pagination> <abc-pagination :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" :is-background=\"false\" prev-text=\"\" next-text=\"\" @current-change=\"handlePageChange\" > </abc-pagination> <h5 style=\"margin: 24px 0\">size:small</h5> <abc-pagination size=\"small\" :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" @current-change=\"handlePageChange\" > </abc-pagination> <abc-pagination size=\"small\" :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" :is-background=\"false\" prev-text=\"\" next-text=\"\" @current-change=\"handlePageChange\" > </abc-pagination> </div> </template> <script> export default { data() { return { pageParams: { pageIndex: 0, pageSize: 10, count: 100, }, } }, methods: { handlePageChange(val) { console.log('val', val) }, }, } </script>"
props:
  - name: size
    description: |-
      分页组件大小
      支持 normal/small
    values:
      - small
      - normal
    default: "'normal'"
  - name: showPagination
    description: 是否显示分页
    default: "true"
  - name: count
    description: 数据总数
    default: "0"
  - name: paginationParams
    description: |-
      分页参数

      {number} pageIndex 当前页码，
      {number} pageSize 每页条数，如果开启了showSize，则作为分页器默认值

      {number} offset 后端的offset，
      {number} limit 每页条数，如果开启了showSize，则作为分页器默认值

      以上两组参数成对出现，同时存在，优先使用第一组
    default: "{}"
  - name: showTotalPage
    description: 是否展示总数
    default: "false"
  - name: totalPageAlignRight
    description: 总数是否右对齐，有tipsContent时无效
    default: "false"
  - name: hideLastPageCount
    description: 是否隐藏最后一页的页码
    default: "false"
  - name: prevText
    description: 上一页按钮文案
    default: "''"
  - name: nextText
    description: 下一页按钮文案
    default: "''"
  - name: isBackground
    description: 是否展示背景
    default: "true"
  - name: pagerCount
    description: 最大页码按钮数
    default: "7"
  - name: showSize
    description: 是否展示分页器
    default: "false"
  - name: pageSizes
    description: |-
      分页器每页个数选项，支持数字[10, 20, 30, 40, 50, 100]，对象[{value: 10, label: '推荐'}, {value: 20, label: ''}]
      以及组合[{value: 10, label: '推荐'}, 20, {value: 30, label: '热门'}]
    default: "[10, 20, 30, 40, 50, 100]"
  - name: pageSizesWidth
    description: 分页器宽度
    default: "106"
slots:
  - tipsContent
events:
  - current-change
  - size-change
