name: AbcTooltip
description: 提示组件，用于展示提示信息
usage: <template> <div class="box"> <div class="top"> <abc-tooltip placement="top-start" content="Top Start 提示文字"> <abc-button class="box-item" type="ghost">上左</abc-button> </abc-tooltip> <abc-tooltip placement="top" content="Top 提示文字"> <abc-button class="box-item" type="ghost">上中</abc-button> </abc-tooltip> <abc-tooltip placement="top-end" content="Top End 提示文字"> <abc-button class="box-item" type="ghost">上右</abc-button> </abc-tooltip> </div> <div class="left"> <abc-tooltip placement="left-start" content="Left Start 提示文字"> <abc-button class="box-item" type="ghost">左上</abc-button> </abc-tooltip> <abc-tooltip placement="left" content="Left 提示文字"> <abc-button class="box-item" type="ghost">左中</abc-button> </abc-tooltip> <abc-tooltip placement="left-end" content="Left End 提示文字"> <abc-button class="box-item" type="ghost">左下</abc-button> </abc-tooltip> </div> <div class="right"> <abc-tooltip placement="right-start" content="Right Start 提示文字"> <abc-button class="box-item" type="ghost">右上</abc-button> </abc-tooltip> <abc-tooltip placement="right" content="Right 提示文字"> <abc-button class="box-item" type="ghost">右中</abc-button> </abc-tooltip> <abc-tooltip placement="right-end" content="Right End 提示文字"> <abc-button class="box-item" type="ghost">右下</abc-button> </abc-tooltip> </div> <div class="bottom"> <abc-tooltip placement="bottom-start" content="Bottom Start 提示文字"> <abc-button class="box-item" type="ghost">下左</abc-button> </abc-tooltip> <abc-tooltip placement="bottom" content="Bottom 提示文字"> <abc-button class="box-item" type="ghost">下中</abc-button> </abc-tooltip> <abc-tooltip placement="bottom-end" content="Bottom End 提示文字"> <abc-button class="box-item" type="ghost">下右</abc-button> </abc-tooltip> </div> </div> </template> <script> export default {} </script>
props:
  - name: transformOrigin
    default: "true"
  - name: placement
    description: |-
      弹出位置
      top(-start, -end),
      right(-start, -end),
      bottom(-start, -end),
      left(-start, -end)
    values:
      - (top|bottom|left|right)(-start|-end)
    default: "'top-start'"
  - name: boundariesPadding
    description: 弹出位置边界的内边距
    default: "5"
  - name: reference
    description: popper 定位的引用元素
  - name: popper
  - name: offset
    description: 偏移量，控制 popper 相对于参考元素的偏移
    default: "0"
  - name: value
  - name: visibleArrow
    description: 箭头是否可见
    default: "true"
  - name: transition
  - name: arrowOffset
    description: 箭头偏移量
    default: "0"
  - name: appendToBody
    description: 是否将弹出层追加到 body 上
    default: "true"
  - name: popperOptions
    default: |-
      {
          // gpuAcceleration: false,
          eventsEnabled: false,
          boundariesElement: 'viewport',
      }
  - name: theme
    description: 主题, yellow/black
    default: "'yellow'"
  - name: size
    description: 尺寸, default/small
    default: "'default'"
  - name: trigger
    description: 触发方式，目前支持 hover
    default: "'hover'"
  - name: content
    description: 提示内容，仅支持文本，有自定义样式时，使用 slot="content"
    default: "''"
  - name: disabled
    description: 是否禁用，禁用时不展示 popper
    default: "false"
  - name: zIndex
    description: 控制层级，默认 1992
    default: "1992"
  - name: openDelay
    description: 打开延迟时间，单位 ms
    default: "0"
  - name: customPopperClass
    description: 自定义popper class name
    default: "''"
  - name: maxWidth
    description: 内容最大宽度
    default: "''"
  - name: showOnOverflow
    description: 是否在内容溢出时展示
    default: "false"
events:
  - input
  - created
