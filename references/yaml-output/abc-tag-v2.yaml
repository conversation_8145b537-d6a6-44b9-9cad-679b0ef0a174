name: AbcTagV2
description: 标签常用于标记、分类和选择。
usage: "<template> <abc-flex vertical gap=\"large\"> <abc-descriptions :column=\"1\" :label-width=\"120\" grid label-vertical-align=\"center\" > <abc-descriptions-item label=\"流程标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 theme=\"primary\">待审批</abc-tag-v2> <abc-tag-v2 theme=\"success\">已通过</abc-tag-v2> <abc-tag-v2 theme=\"danger\">已驳回</abc-tag-v2> <abc-tag-v2 theme=\"default\">已关闭</abc-tag-v2> </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"患者标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 variant=\"light-outline\" theme=\"success\" shape=\"round\" size=\"mini\" >高血压</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"primary\" shape=\"square\" size=\"mini\" use-first-letter >高血压</abc-tag-v2 > <abc-tag-v2 variant=\"dark\" theme=\"danger\" shape=\"round\" size=\"mini\" >欠</abc-tag-v2 > </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"单文字患者标签\"> <abc-flex gap=\"middle\" vertical> <abc-flex gap=\"middle\"> <abc-tag-v2 variant=\"light-outline\" theme=\"break-blue\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"purple\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"primary\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"success\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"danger\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"warning\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"gold\" size=\"mini\" use-first-letter >例</abc-tag-v2 > </abc-flex> <abc-flex gap=\"middle\"> <abc-tag-v2 variant=\"light-outline\" theme=\"dark-blue\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"dark-pink\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"cyan\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"lime\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"magenta\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"pink-purple\" size=\"mini\" use-first-letter >例</abc-tag-v2 > <abc-tag-v2 variant=\"light-outline\" theme=\"yellow\" size=\"mini\" use-first-letter >例</abc-tag-v2 > </abc-flex> </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"图标患者标签\"> <abc-flex gap=\"middle\" vertical> <abc-flex gap=\"middle\"> <abc-tag-v2 variant=\"light-outline\" theme=\"break-blue\" size=\"mini\" icon=\"s-heart-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"purple\" size=\"mini\" icon=\"s-virus-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"primary\" size=\"mini\" icon=\"s-currency-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"cyan\" size=\"mini\" icon=\"s-share-circle-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"danger\" size=\"mini\" icon=\"s-laughter-line\" ></abc-tag-v2> </abc-flex> <abc-flex gap=\"middle\"> <abc-tag-v2 variant=\"light-outline\" theme=\"dark-blue\" size=\"mini\" icon=\"s-disable-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"dark-pink\" size=\"mini\" icon=\"s-like-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"success\" size=\"mini\" icon=\"s-time-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"lime\" size=\"mini\" icon=\"s-happy-line\" ></abc-tag-v2> <abc-tag-v2 variant=\"light-outline\" theme=\"yellow\" size=\"mini\" icon=\"s-unhappy-line\" ></abc-tag-v2> </abc-flex> </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"营销标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 variant=\"dark\" theme=\"danger\" shape=\"round\" >会员卡</abc-tag-v2 > <abc-tag-v2 variant=\"outline\" theme=\"danger\" shape=\"round\" >折扣卡</abc-tag-v2 > </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"药品标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 variant=\"dark\" theme=\"success\" shape=\"round\" size=\"mini\" >OTC</abc-tag-v2 > <abc-tag-v2 variant=\"dark\" theme=\"warning\" shape=\"round\" size=\"mini\" >医保</abc-tag-v2 > </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"活动标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 variant=\"light-outline\" theme=\"warning\" shape=\"round\" size=\"mini\" >满100元立减20</abc-tag-v2 > </abc-flex> </abc-descriptions-item> <abc-descriptions-item label=\"实体标签\"> <abc-flex gap=\"large\"> <abc-tag-v2 variant=\"outline\" theme=\"default\" shape=\"square\" size=\"large\" icon=\"people\" >张三丰</abc-tag-v2 > <abc-tag-v2 variant=\"outline\" theme=\"default\" shape=\"square\" size=\"large\" icon=\"people\" icon-color=\"var(--abc-color-R3)\" >殷素素</abc-tag-v2 > <abc-tag-v2 variant=\"outline\" theme=\"default\" shape=\"square\" size=\"huge\" icon=\"department_2\" >发热门诊</abc-tag-v2 > <abc-tag-v2 variant=\"outline\" theme=\"default\" shape=\"square\" size=\"huge\" icon=\"city\" >ABC数字医疗云</abc-tag-v2 > </abc-flex> </abc-descriptions-item> </abc-descriptions> </abc-flex> </template> <script> export default { data() { return { selected: false, shape: 'square', closable: false, closePosition: 'top-right', } }, computed: { isOnlyIcon() { return this.icon && !this.tagText }, }, watch: { closable(val) { if (val) { this.closePosition = 'top-right' } }, }, methods: { handleClose() { console.log('handleClose') }, handleClick() { console.log('handleClick') this.selected = !this.selected }, }, } </script>"
props:
  - name: shape
    description: 形状：square/round/quadrangle
    default: "'square'"
  - name: size
    description: 尺寸：tiny/mini/small/medium/large/huge/hugely
    default: "'medium'"
  - name: theme
    description: 主题：default/primary/warning/danger/success/purple/cyan/magenta/lime/gold/pink-purple/yellow/break-blue/dark-pink/dark-blue
    default: "'default'"
  - name: variant
    description: 变体：light/dark/outline/light-outline/ghost
    default: "'light'"
  - name: closable
    description: 是否显示关闭按钮
    default: "false"
  - name: closePosition
    description: 关闭按钮的位置
    default: "'top-right'"
  - name: closeResident
    description: 关闭按钮是否常驻，针对 closePosition 为 top-right 有效
    default: "false"
  - name: icon
    description: 显示在 label 左侧的图标
    default: "''"
  - name: iconSize
  - name: iconColor
  - name: useFirstLetter
    description: 是否使用首字母作为标签
    default: "false"
  - name: disabled
    description: 是否禁用
    default: "false"
  - name: selectable
    description: 是否可选择
    default: "false"
  - name: selected
    description: 是否选中
    default: "false"
  - name: ellipsis
    description: 是否省略
    default: "false"
  - name: minWidth
    description: 最小宽度
    default: "''"
slots:
  - name: icon
    description: icon 自定义图标时可用，优先使用 props 中的 icon，而不是插槽
  - name: default
    description: 默认插槽，显示文本
events:
  - close
  - click
