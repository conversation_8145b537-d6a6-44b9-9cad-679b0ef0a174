name: AbcTable
description: 表格组件，用于展示数据，支持自定义表头、自定义列、自定义行、自定义单元格、自定义分页、自定义加载状态、自定义空状态、自定义选中状态、自定义排序、自定义筛选、自定义固定列、自定义虚拟列表、自定义树形表格、自定义滚动加载、自定义配置工具等。
usage: "<template> <abc-form item-no-margin> <abc-table data-cy=\"storybook-test-default\" show-order :render-config=\"renderConfig\" :data-list=\"dataList\" need-min-height :show-hover-tr-bg=\"false\" > <template #describe=\"{ trData }\"> <abc-table-cell :theme=\"trData.describe\" clickable> {{ trData.describe }} </abc-table-cell> </template> <template #unitPrice=\"{ trData }\"> <abc-table-cell> <abc-form-item> <abc-input v-model=\"trData.unitPrice\"></abc-input> </abc-form-item> </abc-table-cell> </template> <template #count=\"{ trData }\"> <abc-table-cell :title=\"title()\"> <abc-form-item> <abc-input v-model=\"trData.count\"></abc-input> </abc-form-item> </abc-table-cell> </template> <template #totalPrice=\"{ trData }\"> <abc-table-cell> <abc-form-item> <abc-input v-model=\"trData.totalPrice\"></abc-input> </abc-form-item> </abc-table-cell> </template> </abc-table> </abc-form> </template> <script> export default { data() { return { unitPrice: '10', unitCount: '1', totalPrice: '10', sex: '男', renderConfig: { hasInnerBorder: true, hasHeaderBorder: true, list: [ { key: 'name', label: '诊疗项目', testValue: '自动计费项目', headerAppendRender: (h, config) => { return <span style=\"color: red\">headerAppendRender</span> }, headerStyle: { textAlign: 'center', }, style: { flex: '1', paddingLeft: '', paddingRight: '', textAlign: 'left', }, }, { key: 'describe', label: '介绍', description: '提示', style: { flex: 'none', width: '86px', paddingLeft: '', paddingRight: '', textAlign: 'right', }, }, { key: 'unitPrice', label: '单价', colType: 'money', style: { flex: 'none', width: '86px', paddingLeft: '', paddingRight: '', textAlign: 'right', }, }, { key: 'count', label: '数量', style: { flex: 'none', width: '86px', textAlign: 'right', }, }, { key: 'totalPrice', label: '金额', colType: 'money', style: { flex: 'none', width: '86px', textAlign: 'right', }, }, ], }, dataList: [], } }, created() { setTimeout(() => { this.dataList = [ { keyId: '1', name: '自动计费项目', unitPrice: '10', count: '1', describe: 'success', totalPrice: '10', }, { keyId: '2', name: '诊疗项目', unitPrice: '10', count: '1', describe: 'warning', totalPrice: '10', }, { keyId: '3', name: '诊疗项目3', unitPrice: '30', unitCount: '3', describe: 'danger', totalPrice: '90', }, { keyId: '4', name: '诊疗项目4', unitPrice: '30', unitCount: '3', describe: 'primary', totalPrice: '90', }, ] }, 1000) }, methods: { title() { console.log(3333) }, }, } </script>"
props:
  - name: type
    description: |-
      表格预设风格:
      pro - 默认都是pro了，相关代码及定义在 table-pro-mixin.js
      excel - 请用 abc-form excel 实现
    default: "'normal'"
  - name: custom
    description: table内部完全自定义，满足一些列数很少的极端情况，正常情况下不要使用
    default: "false"
  - name: dataList
    description: |-
      custom为false的情况下，需要传 dataList
      非自定义customTrKey dataList 中的每一项必须包含 keyId 字段
      dataList 支持checkbox的话需要包含 checked 字段
    default: "[]"
  - name: headerSize
    description: 表格header height，目前有 small:26px, medium:36px, large:40px
    default: "'default'"
  - name: cellSize
    description: |-
      表格cell height，目前有
      default:40px,
      small:32px
      large:48px,
      xlarge:56px,
      xxlarge:64px,
      xxxlarge:72px,
      这里高度的变动会影响下面虚拟列表默认高度
    default: "'default'"
  - name: fixedTrHeight
    description: 默认情况下表格的行高度是根据根据cellSize固定的，但是有些情况下，需要动态高度
    default: "true"
  - name: cellPaddingSize
    description: 表格 table-cell padding，目前有 default:8px, large:12px
    default: "'default'"
  - name: theme
    description: |-
      表格主题
      white: 白色header footer
    default: "''"
  - name: topHeaderTheme
    description: 顶部top head主题，white 代表白底
    default: "''"
  - name: loading
    default: "false"
  - name: disabled
    description: 影响背景颜色
    default: "false"
  - name: supportDeleteTr
    description: 是否支持删除行， 抛出事件，使用方处理源数据
    default: "false"
  - name: deleteIconVariant
    description: 同 abc-delete-icon variant 属性
    default: "'outline-circle'"
  - name: needDeleteConfirm
    description: 是否支持删除行确认
    default: "false"
  - name: confirmPopoverPlacement
    description: 删除行确认popover placement
    default: "'right'"
  - name: bodyStyle
    description: 自定义table-body样式
    default: "{}"
  - name: fillHeight
    description: 自动撑满父级（fillReferenceEl）剩余高度
    default: "false"
  - name: autoHeight
    description: table 高度跟随内容，但是不会超过
    default: "false"
  - name: needMinHeight
    description: |-
      table 默认无最小高度
      不过 showContentEmpty 为true 且数据为空，还是会有最小高度
    default: "false"
  - name: tableMinHeight
    description: table 最小高度 默认 300, 只有在 needMinHeight 开启的情况下生效
    default: "300"
  - name: fillReferenceEl
    description: 自动计算高度所依赖的父级，不传默认 document.body
    default: "null"
  - name: fillBottomOffset
    description: 自动计算高度距离父级底部的距离
    default: "16"
  - name: draggable
    description: 支持拖拽排序，当传入了dataList会自动修改数据（虚拟列表暂不支持拖拽排序）
    default: "false"
  - name: enableVirtualList
    description: 是否开启虚拟列表，默认关闭的
    default: "false"
  - name: dragHandle
    description: 拖拽排序的手柄
    default: "'.drag-handle'"
  - name: dragFilter
    description: 拖拽排序排除的
    default: "'.is-drag-disabled'"
  - name: pagination
    description: |-
      展示分页
      pagination.showTotalPage: 是否展示总页数
      pagination.params: 分页参数
      pagination.count: 总条数
    default: "null"
  - name: showContentEmpty
    default: "true"
  - name: showHoverTrBg
    description: |-
      tr hover 是否展示背景色
      当设置 type === excel 的时候，该值无效，默认关闭
    default: "true"
  - name: emptyContent
    default: "'暂无数据'"
  - name: emptyShowIcon
    default: "true"
  - name: emptySize
    description: 空展位大小
    default: "'small'"
  - name: renderConfig
    default: "{}"
  - name: customTrKey
    default: |-
      () => {
          return '';
      }
  - name: customTrClass
    default: |-
      () => {
          return '';
      }
  - name: customTdClass
    default: |-
      () => {
          return '';
      }
  - name: customTrSupportDelete
    description: 自定义某行是否支持删除，前提条件table开启 supportDeleteTr
    default: |-
      () => {
          return true;
      }
  - name: summary
    default: "null"
  - name: summaryRenderKeys
    default: "[]"
  - name: showAllCheckbox
    description: 是否展示全选checkbox
    default: "true"
  - name: disabledCheckAll
    default: "false"
  - name: disabledItemFunc
    default: |-
      () => {
          return false;
      }
  - name: disabledItemDragFunc
    default: |-
      () => {
          return false;
      }
  - name: showChecked
    description: 需要展示checked状态
    default: "true"
  - name: trClickTriggerChecked
    description: 行点击是否触发checked
    default: "true"
  - name: needSelected
    description: 需要展示选中状态
    default: "false"
  - name: showOrder
    description: 需要展示序号
    default: "false"
  - name: customOrderRender
  - name: trClickable
    description: "每一行是否可以被点击，会出现 cursor: pointer 效果"
    default: "false"
  - name: childKey
    description: 指定 childKey，会展示dataList中的item的子项
    default: "''"
  - name: virtualListConfig
    description: 虚拟列表配置
    default: |-
      {
          bufferSize: 20,
          isFixedRowHeight: true, // 如果表格不是固定高度，就把这个变成false
          threshold: 60, // 阈值，数据量大于这个100就会默认开启
      }
  - name: scrollLoadConfig
    description: 滚动加载配置
    default: () => {}
  - name: hiddenTableHeader
    description: 隐藏表头
    default: "false"
  - name: showChildCheckbox
    description: 是否展示树形表格children的checkbox
    default: "true"
  - name: supportChildFold
    description: 树形表格是否支持折叠
    default: "false"
  - name: customTrDataCy
    description: 自动化测试用例使用
slots:
  - default
  - topHeader
  - name
  - table-content-empty
  - paginationTipsContent
  - footer
events:
  - pageSizeChange
  - pageChange
  - scroll
  - dragStart
  - dragOver
  - update:dataList
  - dragEnd
  - sortChange
  - changeAllChecked
  - changeChecked
  - handleClickTr
  - delete-tr
