name: BizGoodsInfoTagGroup
description: ""
usage: "<template> <abc-space direction=\"vertical\" align=\"start\"> <abc-space> <h2>限制标签:</h2> <biz-goods-info-tag-group :product-info=\"{ antibiotic: 0 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ antibiotic: 1 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ antibiotic: 2 }\" ></biz-goods-info-tag-group> </abc-space> <abc-space> <h2>成分标签:</h2> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 0 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 1 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 2 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 4 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 8 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 16 }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ dangerIngredient: 32 }\" ></biz-goods-info-tag-group> </abc-space> <abc-space> <h2>普通标签:</h2> <biz-goods-info-tag-group :product-info=\"{ goodsTagList: null }\" ></biz-goods-info-tag-group> <biz-goods-info-tag-group :product-info=\"{ goodsTagList: [ { name: '我是真' }, { name: '他是假' }, { name: '前面两个有一个是真的' }, ], }\" > </biz-goods-info-tag-group> </abc-space> <abc-space> <h2>收起标签:</h2> <div style=\" width: 50px; height: 40px; line-height: 40px; border-radius: 6px; border: 1px solid var(--abc-color-T3); padding-left: 4px; margin-left: 4px; \" > <biz-goods-info-tag-group :product-info=\"{ antibiotic: 2, dangerIngredient: 33, goodsTagList: [ { name: '我是真' }, { name: '他是假' }, { name: '前面两个有一个是真的' }, ], }\" :is-fold-tags=\"true\" > </biz-goods-info-tag-group> </div> </abc-space> </abc-space> </template> <script> export default {} </script>"
