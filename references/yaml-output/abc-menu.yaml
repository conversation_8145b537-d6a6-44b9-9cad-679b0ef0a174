name: AbcMenu
description: 菜单组件是一个功能强大的导航组件，用于网站导航、侧边栏等场景。它提供了基础导航、分组、主题定制等丰富功能，支持多级菜单、自定义图标、计数器等特性。
usage: "<template> <div style=\"width: 100%; padding: 20px 0\"> <div style=\"width: 260px; height: 400px; padding: 10px 10px\"> <abc-menu v-model=\"value\" @click=\"selectItem\"> <abc-menu-item icon=\"n-settings-line\" index=\"1\">菜单1</abc-menu-item> <abc-menu-item icon=\"n-settings-line\" index=\"2\">菜单2</abc-menu-item> <abc-menu-item icon=\"n-settings-line\" index=\"3\">菜单3</abc-menu-item> <abc-menu-item icon=\"n-settings-line\" index=\"4\" :count=\"13\" >菜单4</abc-menu-item > <abc-menu-item icon=\"n-settings-line\" index=\"5\">菜单5</abc-menu-item> <abc-menu-item icon=\"n-settings-line\" index=\"6\" :disabledItem=\"true\" >菜单6</abc-menu-item > <abc-sub-menu :index=\"7\" icon=\"n-settings-line\" value=\"菜单7\"> <abc-menu-item index=\"7-1\">菜单7-1</abc-menu-item> <abc-menu-item index=\"7-2\">菜单7-2</abc-menu-item> <abc-menu-item index=\"7-3\" :disabledItem=\"true\" >菜单7-3</abc-menu-item > </abc-sub-menu> <abc-menu-item icon=\"n-settings-line\" index=\"8\">菜单8</abc-menu-item> </abc-menu> </div> </div> </template> <script> export default { data() { return { value: '1', } }, methods: { selectItem(item) { console.log('item', item) }, }, } </script>"
