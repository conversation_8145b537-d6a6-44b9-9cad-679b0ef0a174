name: AbcProgress
description: 进度条组件
usage: "<template> <div> <abc-progress :percentage=\"percentage\"></abc-progress> <abc-progress :percentage=\"percentage\" variant=\"line\"> </abc-progress> <abc-progress :percentage=\"percentage\" variant=\"line\" size=\"large\"> </abc-progress> <abc-progress :percentage=\"percentage\" variant=\"circle\"> </abc-progress> <abc-progress :percentage=\"percentage\" variant=\"circle\" theme=\"green\"> </abc-progress> <abc-progress :percentage=\"percentage2\" :circle-total=\"percentage2\" variant=\"circle\" theme=\"green\" > </abc-progress> </div> </template> <script> export default { data() { return { percentage: 0, percentage2: 0, } }, created() { this.timer = setInterval(() => { if (this.percentage >= 100) { this.percentage = 0 } this.percentage += 4 if (this.percentage >= 100) { clearInterval(this.timer) } }, 200) setTimeout(() => { this.percentage2 = 1 }, 2000) this.$on('hook:beforeDestroy', () => { clearInterval(this.timer) }) }, } </script>"
props:
  - name: percentage
    description: 进度：0-100
    default: "0"
  - name: circleTotal
    description: 环形进度条进度：默认100 适用于 circle
    default: "100"
  - name: theme
    description: 主题 green blue
    default: "'blue'"
  - name: trackColor
    description: 轨道颜色 适用于 line square
    default: "''"
  - name: size
    description: 尺寸 default large  适用于 line
    default: "'default'"
  - name: strokeColor
    description: 进度条颜色 适用于 line square
    default: "''"
  - name: gradient
    description: 开启渐变 适用于 line square
    default: "false"
  - name: variant
    description: 形状 circle line square
    default: "'square'"
  - name: status
    description: 状态 progressing success error warning
    default: "'progressing'"
  - name: width
    description: 宽度 支持传字符串和数字 默认240px 例如 24px 24  适用于 line square
    default: "''"
  - name: customPercentage
    description: 自定义进度条展示的内容 如 50% 10-50
    default: "''"
