name: BizSelectTabs
description: ""
usage: "<template> <abc-form label-width=\"64\" label-position=\"left\" style=\"margin-top: 24px\"> <abc-form-item label=\"tiny\"> <biz-select-tabs v-model=\"value\" :options=\"tabOptions\" size=\"tiny\" ></biz-select-tabs> </abc-form-item> <abc-form-item label=\"small\"> <biz-select-tabs v-model=\"value\" :options=\"tabOptions\" size=\"small\" ></biz-select-tabs> </abc-form-item> <abc-form-item label=\"default\"> <biz-select-tabs v-model=\"value\" :options=\"tabOptions\"></biz-select-tabs> </abc-form-item> <abc-form-item label=\"medium\"> <biz-select-tabs v-model=\"value\" :options=\"tabOptions\" size=\"medium\" ></biz-select-tabs> </abc-form-item> <abc-form-item label=\"large\"> <biz-select-tabs v-model=\"value\" :options=\"tabOptions\" size=\"large\" ></biz-select-tabs> </abc-form-item> </abc-form> </template> <script> export default { data() { return { value: 1, tabOptions: [ { label: '选项1', value: 1, }, { label: '选项2', value: 2, icon: 's-b-scan-line', }, { label: '选项3', value: 3, }, ], } }, } </script>"
