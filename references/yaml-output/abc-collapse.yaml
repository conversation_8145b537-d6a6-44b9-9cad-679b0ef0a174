name: AbcCollapse
description: 折叠面板，用于展示折叠内容
usage: "<template> <div style=\"width: 500px\"> <abc-collapse v-model=\"list\" data-cy=\"storybook-test-default\"> <abc-collapse-item title=\"标题1\" value=\"0\" title-padding=\"0 6px 0 16px\" content-padding=\"10px 6px 16px 16px\" > <div style=\"text-align: justify\"> Operation feedback: enable the users to clearly perceive their operations by style updates and interactive effects; </div> <div style=\"text-align: justify\"> Visual feedback: reflect current state by updating or rearranging elements of the page. </div> </abc-collapse-item> <abc-collapse-item title=\"标题2\" value=\"1\" title-padding=\"0 6px 0 16px\" content-padding=\"10px 6px 16px 16px\" > <div style=\"text-align: justify\"> Operation feedback: enable the users to clearly perceive their operations by style updates and interactive effects; </div> <div style=\"text-align: justify\"> Visual feedback: reflect current state by updating or rearranging elements of the page. </div> </abc-collapse-item> <abc-collapse-item title=\"标题3\" value=\"2\" title-padding=\"0 6px 0 16px\" content-padding=\"10px 6px 16px 16px\" > <div style=\"text-align: justify\"> Operation feedback: enable the users to clearly perceive their operations by style updates and interactive effects; </div> <div style=\"text-align: justify\"> Visual feedback: reflect current state by updating or rearranging elements of the page. </div> </abc-collapse-item> </abc-collapse> </div> </template> <script> export default { data() { return { list: ['0'], } }, } </script>"
props:
  - name: accordion
    description: 手风琴
    default: "false"
  - name: v-model
    description: v-model 绑定的值
    default: "[]"
  - name: border
    description: 最外层是否带有边框
    default: "false"
  - name: size
    description: "size: default"
    default: "'default'"
  - name: panelTheme
    description: "theme: default / light (default - 灰色，light - 透明)"
    default: "'default'"
  - name: animation
    description: 是否需要动画
    default: () => true
slots:
  - default
events:
  - change
