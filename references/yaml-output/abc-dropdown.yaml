name: AbcDropdown
description: 下拉组件，用于展示下拉内容，与 AbcDropdownItem 配合使用
usage: "<template> <div> <abc-dropdown data-cy=\"storybook-test-default\" size=\"tiny\" @change=\"handleChange\" > <div slot=\"reference\"> <abc-button variant=\"ghost\"> {{ curVal }} </abc-button> </div> <abc-dropdown-item label=\"门诊单\" value=\"outpatient\"></abc-dropdown-item> <abc-dropdown-item label=\"收费单\" value=\"cashier\"></abc-dropdown-item> <abc-dropdown-item label=\"发药单\" value=\"dispense\"></abc-dropdown-item> </abc-dropdown> <abc-dropdown @change=\"handleChange\" size=\"small\"> <div slot=\"reference\"> <abc-button variant=\"ghost\"> {{ curVal }} </abc-button> </div> <abc-dropdown-item label=\"门诊单\" value=\"outpatient\"></abc-dropdown-item> <abc-dropdown-item label=\"收费单\" value=\"cashier\"></abc-dropdown-item> <abc-dropdown-item label=\"发药单\" value=\"dispense\"></abc-dropdown-item> </abc-dropdown> <abc-dropdown @change=\"handleChange\"> <div slot=\"reference\"> <abc-button variant=\"ghost\"> {{ curVal }} </abc-button> </div> <abc-dropdown-item label=\"门诊单\" value=\"outpatient\"></abc-dropdown-item> <abc-dropdown-item label=\"收费单\" value=\"cashier\"></abc-dropdown-item> <abc-dropdown-item label=\"发药单\" value=\"dispense\"></abc-dropdown-item> </abc-dropdown> <abc-dropdown @change=\"handleChange\" size=\"large\"> <div slot=\"reference\"> <abc-button variant=\"ghost\"> {{ curVal }} </abc-button> </div> <abc-dropdown-item label=\"门诊单\" value=\"outpatient\"></abc-dropdown-item> <abc-dropdown-item label=\"收费单\" value=\"cashier\"></abc-dropdown-item> <abc-dropdown-item label=\"发药单\" value=\"dispense\"></abc-dropdown-item> </abc-dropdown> </div> </template> <script> export default { data() { return { curVal: 'outpatient', } }, methods: { handleChange(val) { this.curVal = val }, }, } </script>"
props:
  - name: transformOrigin
    default: "true"
  - name: placement
    description: 自定义 弹层 方向
    values:
      - (top|bottom|left|right)(-start|-end)
    default: "'bottom'"
  - name: boundariesPadding
    description: 弹出位置边界的内边距
    default: "5"
  - name: reference
    description: popper 定位的引用元素
  - name: popper
  - name: offset
    description: popper 定位的偏移量
    default: "0"
  - name: value
  - name: visibleArrow
    description: 是否显示箭头
  - name: transition
  - name: arrowOffset
    description: 箭头的偏移量
    default: "35"
  - name: appendToBody
    description: 是否将弹出层追加到 body 上
    default: "true"
  - name: popperOptions
    default: |-
      {
          // gpuAcceleration: false,
          eventsEnabled: false,
          boundariesElement: 'viewport',
          // modifiersIgnored: ['preventOverflow'],
      }
  - name: customClass
    description: 自定义class
    default: "''"
  - name: size
    description: 尺寸
    values:
      - tiny
      - small
      - large
    default: "''"
  - name: marginTop
    description: 自定义 弹层 marginTop
  - name: disabled
    description: 禁用
  - name: minWidth
    description: 自定义 弹层 maxWidth
  - name: maxWidth
    description: 自定义 弹层 maxWidth
  - name: maxHeight
    description: 自定义 弹层 ul list 最大高度
  - name: scrollToSelected
    description: 是否滚动到选中位置
    default: "true"
  - name: control
    description: |-
      是否控制在面板展开时，reference 的 active 态，
      active 态需要组件自己实现：引入 mixin（popover-control）, 并且在组件中实现相应逻辑
    default: "true"
slots:
  - reference
  - default
events:
  - input
  - created
  - change
  - content-click
