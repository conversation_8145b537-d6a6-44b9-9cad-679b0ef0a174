name: AbcResult
description: 结果展示组件，用于展示操作结果，提供了成功、警告、信息三种状态
usage: "<template> <abc-flex gap=\"40\"> <abc-result title=\"提交成功\" content=\"审核后即可开通微商城（大概需要3到7个工作日）\" > <template #btn> <abc-flex> <abc-button>现在设置</abc-button> <abc-button variant=\"ghost\">稍后设置</abc-button> <abc-button variant=\"text\">我知道了</abc-button> </abc-flex> </template> </abc-result> <abc-result status=\"warning\" title=\"提交失败\" content=\"审核后即可开通微商城（大概需要3到7个工作日）\" > <template #btn> <abc-flex> <abc-button>现在设置</abc-button> <abc-button variant=\"ghost\">稍后设置</abc-button> <abc-button variant=\"text\">我知道了</abc-button> </abc-flex> </template> </abc-result> <abc-result status=\"info\" title=\"等待中\" content=\"审核后即可开通微商城（大概需要3到7个工作日）\" > <template #btn> <abc-flex> <abc-button>现在设置</abc-button> <abc-button variant=\"ghost\">稍后设置</abc-button> <abc-button variant=\"text\">我知道了</abc-button> </abc-flex> </template> </abc-result> </abc-flex> </template> <script> export default {} </script>"
props:
  - name: vertical
    description: |-
      排列方式 默认纵向排列
      横向排列, 仅支持Success/warning状态，且只有icon和文字
    default: "true"
  - name: status
    description: 状态 【success | warning | info 】
    default: "'success'"
  - name: customIcon
    description: 自定义icon（仅支持纵向排列）
    default: "''"
  - name: customIconColor
    description: 自定义icon color（仅支持纵向排列）
    default: "''"
  - name: title
    description: 标题
    default: "''"
  - name: content
    description: 内容
    default: "''"
  - name: showTitle
    description: 是否展示标题
    default: "true"
  - name: showContent
    description: 是否展示内容
    default: "true"
  - name: showBtn
    description: 是否展示按钮
    default: "true"
slots:
  - content
  - btn
