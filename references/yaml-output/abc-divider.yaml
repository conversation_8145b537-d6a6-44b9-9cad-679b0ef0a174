name: AbcDivider
description: 分割线是一个呈线状的轻量化组件，起到分隔、组织、细化的作用，用于有逻辑的组织元素内容和页面结构
usage: "<template> <abc-flex vertical gap=\"large\" style=\"padding: 16px\"> <abc-space> 主题 <abc-radio-group v-model=\"theme\"> <abc-radio-button label=\"light\" /> <abc-radio-button label=\"dark\" /> </abc-radio-group> </abc-space> <abc-space> 尺寸 <abc-radio-group v-model=\"size\"> <abc-radio-button label=\"normal\" /> <abc-radio-button label=\"large\" /> </abc-radio-group> </abc-space> <abc-space> 边距 <abc-radio-group v-model=\"margin\"> <abc-radio-button label=\"none\" /> <abc-radio-button label=\"mini\" /> <abc-radio-button label=\"small\" /> <abc-radio-button label=\"normal\" /> <abc-radio-button label=\"large\" /> </abc-radio-group> </abc-space> <abc-space> 布局 <abc-radio-group v-model=\"layout\"> <abc-radio-button label=\"horizontal\" /> <abc-radio-button label=\"vertical\" /> </abc-radio-group> </abc-space> <div> Item1 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" :layout=\"layout\" /> Item2 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"dashed\" :layout=\"layout\" /> Item3 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"dotted\" :layout=\"layout\" /> Item4 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"double\" :layout=\"layout\" /> </div> <div vertical style=\"background-color: #438ae2\"> Item1 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" :layout=\"layout\" /> Item2 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"dashed\" :layout=\"layout\" /> Item3 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"dotted\" :layout=\"layout\" /> Item4 <abc-divider :theme=\"theme\" :size=\"size\" :margin=\"margin\" type=\"double\" :layout=\"layout\" /> </div> </abc-flex> </template> <script> export default { data() { return { theme: 'light', size: 'normal', margin: 'normal', layout: 'horizontal', } }, } </script>"
props:
  - name: variant
    values:
      - solid
      - dashed
    default: "'solid'"
  - name: theme
    values:
      - light
      - dark
    default: "'light'"
  - name: size
    values:
      - normal
      - large
    default: "'normal'"
  - name: margin
    values:
      - none
      - mini
      - small
      - large
      - normal
    default: "'normal'"
  - name: layout
    values:
      - horizontal
      - vertical
    default: "'horizontal'"
