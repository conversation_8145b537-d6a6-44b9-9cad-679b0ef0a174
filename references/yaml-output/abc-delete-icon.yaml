name: AbcDeleteIcon
description: 简简单单，删除按钮，尺寸支持 large/huge/hugely
usage: "<template> <abc-flex vertical :style=\"containerStyle\"> <abc-flex> <abc-space> <span>theme：</span> <abc-radio-group v-model=\"theme\"> <abc-radio label=\"default\"></abc-radio> <abc-radio label=\"dark\"></abc-radio> <abc-radio label=\"light\"></abc-radio> </abc-radio-group> </abc-space> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon :theme=\"theme\" size=\"small\" data-cy=\"e2e-delete-icon\" @delete=\"handleDelete\" ></abc-delete-icon> <abc-delete-icon :theme=\"theme\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"large\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"huge\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"hugely\"></abc-delete-icon> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"small\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"large\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"huge\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"hugely\" ></abc-delete-icon> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon variant=\"fill\" size=\"small\"></abc-delete-icon> <abc-delete-icon variant=\"fill\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"large\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"huge\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"hugely\"></abc-delete-icon> </abc-flex> </abc-flex> </template> <script> export default { data() { return { theme: 'default', } }, computed: { containerStyle() { if (this.theme === 'light') { return { background: 'var(--abc-color-B2)', } } }, }, methods: { handleDelete() { alert('delete') }, }, } </script>"
props:
  - name: transformOrigin
    default: "true"
  - name: placement
    description: 弹出位置
    values:
      - (top|bottom|left|right)(-start|-end)
    default: "'right'"
  - name: boundariesPadding
    description: 弹出位置边界的内边距
    default: "5"
  - name: reference
    description: popper 定位的引用元素
  - name: popper
  - name: offset
    description: popper 定位的偏移量
    default: "0"
  - name: value
  - name: visibleArrow
    description: 是否显示箭头
    default: "false"
  - name: transition
  - name: arrowOffset
    description: 箭头的偏移量
    default: "35"
  - name: appendToBody
    description: 是否将弹出层追加到 body 上
    default: "true"
  - name: popperOptions
    default: |-
      {
          // gpuAcceleration: false,
          eventsEnabled: false,
          boundariesElement: 'viewport',
          // modifiersIgnored: ['preventOverflow'],
      }
  - name: variant
    default: "'outline-circle'"
  - name: theme
    default: "'default'"
  - name: size
    values:
      - small
      - medium
      - large
      - huge
      - hugely
    default: "''"
  - name: needConfirm
    default: "false"
  - name: customPopoverClass
    default: "''"
events:
  - input
  - created
  - delete
