name: AbcTransferV2
description: 穿梭框组件，用于在两栏之间进行数据穿梭
usage: "<template> <abc-transfer-v2 :data=\"data\" node-key=\"id\" :result-node-show-parent-name=\"true\" :confirm-button-disabled=\"false\" > </abc-transfer-v2> </template> <script> export default { data() { return { data: clone(data), } }, methods: {}, } </script>"
props:
  - name: data
    description: "树形结构数据: 参考props和nodeKey"
    default: "[]"
  - name: nodeKey
    description: 每个节点的唯一标识
    default: "'id'"
  - name: defaultCheckedKeys
    description: 默认选择的节点
  - name: defaultExpandedKeys
    description: 默认展开的节点
  - name: currentNodeKey
    description: 当前选中的节点
  - name: props
    description: |-
      指定节点属性
      {
        children: 'children', // 子节点
        label: 'label', // 展示名称
        subLabel: 'subLabel', // 展示副标题
        disabled: 'disabled', // 是否禁用
        additional: 'additional', // 针对label的一些附加信息展示
        isRemote: 'isRemote', // 该节点是否需要异步加载children
        isUserLeaf: 'isUserLeaf', // 该节点是否是用户自定义的叶子节点(场景：树形结构全是目录，需要将最后一级目录设置未自定义叶子节点， 此时真正的叶子节点会脱离树形结构展示)
        isLeaf: 'isLeaf', // 该节点是否为叶子节点
      }
    default: |-
      function() {
          return {
              children: 'children',
              label: 'label',
              disabled: 'disabled',
              additional: 'additional',
              isRemote: 'isRemote',
              subLabel: 'subLabel',
              isUserLeaf: 'isUserLeaf',
              isLeaf: 'isLeaf',
          };
      }
  - name: indent
    description: 相邻级节点间的水平缩进，单位为像素
    default: "16"
  - name: load
    description: 异步加载对应的function 配合节点设置了isRemote使用
  - name: lastNodeDetachTree
    description: 树形节点最后一级是否脱离树形结构单独展示, 仅在非tab模式下生效
    default: "false"
  - name: defaultCheckDisabled
    description: |-
      是否默认禁用所有节点的选择, 配合defaultCheckedKeys使用,
      且已选区域不会展示出已选的节点（需求：只能新增；若需要删除，则在组件外部删除，传入删除后的defaultCheckedKeys）
    default: "false"
  - name: leafIcon
    description: 叶子节点的icon
    default: "'s-commodity-color'"
  - name: icon
    description: 非叶子节点的icon
    default: "'s-folder-color'"
  - name: searchNodeMethod
    description: 全局搜索的function
  - name: searchPlaceholder
    description: 全局搜索的placeholder
    default: "'搜索'"
  - name: round
    description: 圆角展示
    default: "true"
  - name: showIcon
    description: 是否展示图标
    default: "true"
  - name: showCheckAll
    description: 是否展示全选
    default: "false"
  - name: checkStrictly
    description: 父子选择不做关联
    default: "false"
  - name: showSearch
    description: 是否展示搜索框
    default: "false"
  - name: resultTitle
    description: 选择结果的提示
    default: "'请选择'"
  - name: loading
    description: 是否展示loading
    default: "false"
  - name: isTabTransfer
    description: 是否是tab 模式的transfer
    default: "false"
  - name: transferTabOptions
    description: |-
      配合isTabTransfer使用，当isTabTransfer，不需要传data, transferTabOptions
      格式：
      [
              {
                  label: 'xx',
                  value: 'xx',
                  data: 'transfer的data'

                  以下可以不传
                  icon: 'xxx',
                  leafIcon: 'xxx',
                  showIcon: true,
              },
      ]
    default: "[]"
  - name: transferCurrentTab
    description: 当前选中的tab
    default: "''"
  - name: resultWidth
    description: 结果部分的宽度
    default: "320"
  - name: width
    description: 整个面板的宽度
    default: "0"
  - name: lite
    description: |-
      是否是lite版本, 需要参数resultList
      并且会抛出事件del,confirm,cancel
    default: "false"
  - name: resultList
    description: lite版本的结果list
    default: "[]"
  - name: size
    description: size, default -> 高600， large -> 高680
    default: () => 'default'
  - name: resultNodeShowParentName
    description: 选中是否要展示父级节点名字
    default: "false"
  - name: separation
    description: 选中是否要展示父级节点名字时的分隔符
    default: "'-'"
  - name: leafNodeCustom
    description: 叶子节点的决定根据业务数据data, data数据里面需要有字段isLeaf
    default: "false"
  - name: confirmButtonDisabled
    description: confirm确认按钮的禁止状态 -> Boolean
    default: undefined
  - name: v-model
    description: |-
      v-model 绑定选中的值；v-model的优先级 > defaultCheckedKeys
      约定：v-model和defaultCheckedKeys不能同时存在
slots:
  - default
  - lite-result-node-label
  - tree-node-label
  - search-node-label
  - empty
  - result-node-label
events:
  - update:transferCurrentTab
  - confirm
  - cancel
  - node-check-click
  - input
  - change
