name: AbcDatePickerBar
description: 日期选择栏，用于选择日期，当需要将日期选项外露进行快捷选择时，可使用此组件，注意使用内置选项时，需要手动导入：import { AbcDatePickerBar } from '@abc/ui-pc';const { DatePickerBarOptions } = AbcDatePickerBar;
usage: "<template> <AbcFlex :gap=\"24\" vertical> <abc-date-picker-bar v-model=\"label\" :options=\"options\" size=\"tiny\" value-format=\"YYYY-MM-DD\" data-cy=\"e2e-date-picker-bar-range\" @clear=\"handleClear\" @change=\"handleDateChange\" > </abc-date-picker-bar> <abc-date-picker-bar v-model=\"label\" :options=\"options\" size=\"small\" value-format=\"YYYY-MM-DD\" @clear=\"handleClear\" @change=\"handleDateChange\" > </abc-date-picker-bar> <abc-date-picker-bar v-model=\"label\" :options=\"options\" value-format=\"YYYY-MM-DD\" @clear=\"handleClear\" @change=\"handleDateChange\" > </abc-date-picker-bar> <abc-date-picker-bar v-model=\"label\" :options=\"options\" size=\"large\" value-format=\"YYYY-MM-DD\" @clear=\"handleClear\" @change=\"handleDateChange\" > </abc-date-picker-bar> <p>{{ date }}</p> </AbcFlex> </template> <script> export default { data() { return { label: DatePickerBarOptions.DAY.label, options: [ DatePickerBarOptions.DAY, DatePickerBarOptions.WEEK, DatePickerBarOptions.MONTH, DatePickerBarOptions.YEAR, { label: 'last_month', name: '上月', getValue() { return [getLastMonthStartDate(), getLastMonthEndDate()] }, }, ], date: '', } }, methods: { handleDateChange(value) { console.log('handleDateChange', value) this.date = value }, handleClear() { this.label = DatePickerBarOptions.DAY.label }, }, } </script>"
props:
  - name: size
    description: 表单尺寸大小
    values:
      - tiny/small/medium/large/huge
    default: "''"
  - name: adaptiveWidth
    description: 是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度
    default: undefined
  - name: value
    description: |-
      当前选中的 label，即传入的 options 中的 label<br/>
      特例：选中日期控件时，label 值为常量 `LABEL_DATE_PICKER`
    default: "''"
  - name: options
    description: |-
      选项值：<br/>
      Array<{<br/>
          label: string,<br/>
          name: string,<br/>
          getValue: () => Date | String | Array<Date|String>, // 返回日期或者字符串或者数组（表示范围时）<br/>
      }>
    default: "[]"
  - name: valueFormat
    description: 返回值格式，默认：'YYYY-MM-DD'
    default: "'YYYY-MM-DD'"
  - name: datePickerType
    description: "日期选择控件的 type: 'daterange' | 'date' | 'monthDayRange'"
    default: "'daterange'"
  - name: enableDatePicker
    description: 是否显示日期选择控件
    default: "true"
  - name: pickerOptions
    description: |-
      日期选择器特有的选项对象:<br/>
      shortcuts: Array<{text: string, onClick: (cb) => void}> 快捷选择日期方式<br/>
      disabledDate: () => Boolean <br/>
      yearRange: {begin: number, end: number} 年份选择范围，闭区间，默认：1990-2040
    default: "{}"
  - name: border
    description: 是否显示边框
    default: "true"
  - name: clearable
    description: 是否可清空
    default: "true"
slots:
  - label
events:
  - input
  - change
  - clear
