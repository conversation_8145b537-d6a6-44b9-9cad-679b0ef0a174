name: BizExpressAddressSelector
description: ""
usage: "<template> <div style=\"width: 400px\"> <biz-express-address-selector :value=\"data\" @create=\"handleCreate\" @edit=\"handleEdit\" @select=\"handleSelect\" :address-list=\"address\" > </biz-express-address-selector> </div> </template> <script> export default { data() { return { data: { id: '1512842890962935808', addressCityId: '110100', addressCityName: '北京市', addressDetail: 'asda', addressDistrictId: '110101', addressDistrictName: '东城区', addressProvinceId: '110000', addressProvinceName: '北京', deliveryName: '1231312', deliveryMobile: '123131', deliveryCompany: { id: '399689965841178624', name: 'HDHHDD', }, deliveryOrderNo: '', deliveryPayType: 1, chargeSheetId: 'ffffffff0000000034dddd1aa80d8004', deliveryFee: null, deliveryTraceData: null, patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '1512842890962935808', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: null, isAvailable: 0, }, address: [ { id: '1512842890962935808', patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '1512842890962935808', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: null, deliveryName: '1231312', deliveryMobile: '123131', addressCityId: '110100', addressCityName: '北京市', addressDetail: 'asda', addressDistrictId: '110101', addressDistrictName: '东城区', addressProvinceId: '110000', addressProvinceName: '北京', isAvailable: 0, }, { id: '1583713390412627968', patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '1583713390412627968', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: null, deliveryName: '把把', deliveryMobile: '18030976151', addressCityId: '120100', addressCityName: '天津市', addressDetail: '把把', addressDistrictId: '120103', addressDistrictName: '河西区', addressProvinceId: '120000', addressProvinceName: '天津市', isAvailable: 0, }, { id: '2127340011120852992', patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '2127340011120852992', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: '86', deliveryName: '苏晨', deliveryMobile: '18228592121', addressCityId: '370200', addressCityName: '青岛市', addressDetail: '趵突泉', addressDistrictId: '370211', addressDistrictName: '黄岛区', addressProvinceId: '370000', addressProvinceName: '山东', isAvailable: 0, }, { id: '2127378803265470464', patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '2127378803265470464', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: null, deliveryName: '李炜12', deliveryMobile: '18030976151', addressCityId: '370200', addressCityName: '青岛市', addressDetail: '区政务大厅', addressDistrictId: '370211', addressDistrictName: '黄岛区', addressProvinceId: '370000', addressProvinceName: '山东', isAvailable: 0, }, { id: '3810758660597792768', patientId: 'ffffffff00000000148859100c7e0000', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', deliveryAddressId: '3810758660597792768', deliveryPatientId: 'ffffffff00000000148859100c7e0000', deliveryCountryCode: '86', deliveryName: '李炜', deliveryMobile: '18030976151', addressCityId: '510100', addressCityName: '成都市', addressDetail: 'ssss', addressDistrictId: '510109', addressDistrictName: '高新区', addressProvinceId: '510000', addressProvinceName: '四川', isAvailable: 1, }, ], } }, methods: { handleCreate() { alert('新增') }, handleEdit() { alert('编辑') }, handleSelect() { alert('选中') }, }, } </script>"
