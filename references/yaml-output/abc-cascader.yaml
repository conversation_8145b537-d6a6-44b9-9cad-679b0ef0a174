name: AbcCascader
description: 级联选择器，用于多级数据选择
usage: "<template> <div> <div class=\"mdx-title\">✨ tiny</div> <abc-cascader v-model=\"value\" :options=\"options\" size=\"tiny\" :disabled=\"true\" :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"禁用\" :width=\"200\" > </abc-cascader> <div class=\"mdx-title\">✨ small</div> <abc-cascader v-model=\"value\" data-cy=\"storybook-test-default\" :options=\"options\" size=\"small\" :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"请输入\" :width=\"200\" no-icon clearable > </abc-cascader> <div class=\"mdx-title\">✨ default</div> <abc-cascader v-model=\"value1\" :options=\"[]\" :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"没有options\" :width=\"200\" > </abc-cascader> <div class=\"mdx-title\">✨ medium</div> <abc-cascader v-model=\"value\" :options=\"options\" size=\"medium\" :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"请输入\" :width=\"200\" no-icon :clearable=\"false\" :setting-config=\"{ visible: true }\" > </abc-cascader> <div class=\"mdx-title\">✨ large</div> <abc-cascader v-model=\"value\" :options=\"options\" size=\"large\" :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"请输入\" :width=\"200\" no-icon :clearable=\"false\" > </abc-cascader> <div class=\"mdx-title\">✨ 仅显示下边框</div> <abc-cascader v-model=\"value\" :options=\"options\" only-bottom-border :props=\"{ children: 'childs', label: 'name', value: 'id' }\" placeholder=\"show value\" :width=\"200\" showValue=\"医生推荐 医生推荐\" > </abc-cascader> <div class=\"mdx-title\">✨ 测试数据</div> <abc-cascader v-model=\"value2\" :options=\"options5\" :props=\"{ children: 'options', label: 'label', value: 'value' }\" placeholder=\"请输入\" :width=\"200\" > </abc-cascader> </div> </template> <script> export default { data() { return { options, options5, value: [], value1: [], value2: [], } }, } </script>"
props:
  - name: size
    description: 表单尺寸大小
    values:
      - tiny/small/medium/large/huge
    default: "''"
  - name: adaptiveWidth
    description: 是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度
    default: undefined
  - name: v-model
    description: v-model 值； 默认选中的值
    default: "[]"
  - name: options
    description: |-
      cascader的options,
      options item可提供search 和 searchFn 来支持搜索； 以及搜索框的placeholder
      可提供customRender来自定义渲染内容
      可提供footer 来自定义按钮
    default: "[]"
  - name: placeholder
    default: () => '请选择'
  - name: width
    description: cascader 宽度 - 这指的是结果wrapper的宽度，并非可选择区域panel的宽度
    default: "360"
  - name: clearable
    description: 当选中值后，是否展示可以删除值的icon
    default: () => true
  - name: separation
    description: 针对单选的 cascader： 对于所选值分割的处理，例如：a/b/c
    default: "'/'"
  - name: onlyShowLeafLabel
    description: "针对单选的 cascader: 所选值只展示叶子节点"
    default: () => false
  - name: disabled
    description: 是否禁用
    default: () => false
  - name: multiple
    description: 是否开启多选
    default: () => false
  - name: mutuallyExclusive
    description: 多选模式下，是否是互斥模式
    default: () => false
  - name: props
    description: options里面的label/value/child的别名
    default: |-
      {
          value: 'value',
          label: 'label',
          children: 'children',
      }
  - name: valueProps
    description: value里面的label/value的别名
    default: |-
      {
          _value: 'value',
          _label: 'label',
      }
  - name: confirmText
    description: 多选情况下，confirm文案
    default: "'筛选'"
  - name: cancelText
    description: 多选情况下，cancel文案
    default: "'清空'"
  - name: footerButtonProps
    description: |-
      多选情况下，自定义footer button 属性
      {
        cancel: {
          text: 'xxx' 这里的text会覆盖cancelText,
          xx: xx 这是是button的属性
        },
        confirm: {
          text: 'xxx' 这里的text会覆盖confirmText，
          xx: xx 这是是button的属性
        },
        footer: {
            xx: xx，
            xx: xx 这是是button的属性
        }
      }
    default: "{}"
  - name: collapse
    description: 多选-不互斥的情况下： 当子项全部选中的时候，结果是否只展示父级的结果
    default: "false"
  - name: referenceMode
    description: "cascader reference展示方式: default - 输入框展示； icon - 图标展示； text - 文字展示"
    default: () => 'default'
  - name: referenceIcon
    description: 默认的展示图标；配合referenceMode 使用
    default: () => 'filtrate'
  - name: referenceIconOptions
    description: 默认的展示图标的属性
    default: () => {}
  - name: referenceText
    description: 配合referenceMode 使用; 当referenceText有值的时候，文字展示内容是不会变的
    default: () => ''
  - name: referenceTextStyle
    description: 默认的展示文字的自定义style
    default: () => {}
  - name: referenceTextJustify
    description: 文字模式的对齐方式
    default: "'start'"
  - name: referenceActiveColor
    description: 激活态颜色，默认为 $B1，仅对文字模式和图标模式生效
    default: AbcTheme.$B1
  - name: panelMaxHeight
    description: 面板最大高度
    default: () => 200
  - name: customFooterStyle
    description: footer 自定义样式
    default: () => {}
  - name: panelWidth
    description: 自定义panel 宽度
  - name: onlyBottomBorder
    description: input只有底部边框
    default: "false"
  - name: settingConfig
    description: |-
      setting项配置信息
      {
          visible: xx,
          disabled: xx,
          disabledReason: xx.
      }
    default: "{}"
  - name: noIcon
    description: 不展示下拉箭头
    default: "false"
  - name: showValue
    description: 自定义展示的文字，有该字段，始终展示该字段,
  - name: filterable
    description: 全局搜索
    default: "false"
  - name: filterMethod
    description: |-
      全局搜索的方法
      注意：不支持api 远程搜索
  - name: filterPlaceholder
    description: 全局搜索的placeholder
    default: "'请输入搜索内容'"
slots:
  - reference
  - default
  - node
events:
  - panel-visible
  - reference-mouse-enter
  - reference-mouse-leave
  - change
  - enter
  - set
  - footer-click
