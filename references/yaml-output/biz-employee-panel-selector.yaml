name: BizEmployeePanelSelector
description: ""
usage: "<template> <div> <biz-employee-panel-selector v-model=\"selectedEmployees\" :employees=\"employees\" :is-deleting=\"isDeleteEmployee\" :default-checked-keys=\"defaultCheckedKeys\" @confirm=\"handleConfirm\" @update:is-deleting=\"isDeleteEmployee = $event\" /> <div style=\"margin-top: 16px\"> <p> 当前选中：{{ selectedEmployees.map((item) => item.name).join('、') }} </p> </div> </div> </template> <script> export default { data() { return { selectedEmployees: [], employees: [ { id: '1', name: '张三', namePy: 'zhangsan', namePyFirst: 'ZS', }, { id: '2', name: '李四', namePy: 'lisi', namePyFirst: 'LS', }, { id: '3', name: '王五', namePy: 'wangwu', namePyFirst: 'WW', }, { id: '4', name: '赵六', namePy: 'zhaoliu', namePyFirst: 'Z<PERSON>', }, ], isDeleteEmployee: false, } }, computed: { defaultCheckedKeys() { return this.selectedEmployees.map((item) => item.id) }, }, methods: { handleConfirm(employees) { this.selectedEmployees = employees console.log('已选择员工：', employees) }, }, } </script>"
