name: AbcPopover
description: 弹出层组件是其他弹窗类组件如气泡确认框实现的基础，当这些组件提供的能力不能满足定制需求时，可以在弹出层组件基础上封装。仅展示文本信息优先使用 AbcTooltip/AbcTooltipInfo
usage: "<template> <div> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"hover\" theme=\"yellow\" data-cy=\"storybook-test-base1\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">hover</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" control data-cy=\"storybook-test-base2\" > <abc-button variant=\"ghost\" slot=\"reference\" >click（reference 可提供active 状态）</abc-button > <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"focus\" theme=\"yellow\" data-cy=\"storybook-test-base3\" control > <span slot=\"reference\"> <abc-input placeholder=\"focus 激活\"></abc-input> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <h2>normal - manual</h2> <br /> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"manual\" theme=\"yellow\" v-model=\"visible\" data-cy=\"storybook-test-base-manual\" control > <span slot=\"reference\"> <abc-button variant=\"text\" data-cy=\"abc-mr-望闻切诊-脉象选择\" @click=\"visible = !visible\" > 脉象选择 </abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <h2>large - click</h2> <br /> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"large\" control > <span slot=\"reference\"> <abc-button icon=\"save\" variant=\"text\" theme=\"default\" size=\"small\"> </abc-button> </span> <div> <abc-button icon=\"save\" variant=\"text\" theme=\"default\" size=\"small\"> </abc-button> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <h2>huge - click</h2> <br /> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"huge\" control > <span slot=\"reference\"> <abc-button variant=\"text\" theme=\"default\" size=\"small\" icon=\"three_dot\" > </abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> </div> </template> <script> export default { data() { return { visible: false, } }, } </script>"
props:
  - name: transformOrigin
    default: "true"
  - name: placement
    description: 弹出位置
    values:
      - (top|bottom|left|right)(-start|-end)
    default: "'bottom-start'"
  - name: boundariesPadding
    description: 弹出位置边界的内边距
    default: "5"
  - name: reference
    description: popper 定位的引用元素
  - name: popper
  - name: offset
    description: popper 定位的偏移量
    default: "0"
  - name: value
  - name: visibleArrow
    description: 箭头是否可见
    default: "true"
  - name: transition
  - name: arrowOffset
    description: 箭头偏移量
    default: "0"
  - name: appendToBody
    description: 是否将弹出层追加到 body 上
    default: "true"
  - name: popperOptions
    default: |-
      {
          // gpuAcceleration: false,
          eventsEnabled: false,
          boundariesElement: 'viewport',
      }
  - name: theme
    description: |-
      提供 custom、yellow、white 三套主题
      默认为 custom，即需要自己定义样式
    values:
      - custom
      - yellow
      - white
    default: "'custom'"
  - name: trigger
    description: |-
      触发方式，支持 click、focus、hover、manual
      当为 manual 时，通过 v-model 控制显示
    values:
      - click
      - focus
      - hover
      - manual
    default: "'click'"
  - name: openDelay
    description: 打开延迟时间，单位 ms
    default: "0"
  - name: closeDelay
    description: 关闭延迟时间，单位 ms
    default: "200"
  - name: disabled
    description: 禁用弹层
    default: "false"
  - name: content
    description: 显示的内容，简单文本可用此属性传递，优先展示 slot 内容
  - name: popperClass
    description: 自定义 popper 的 class
    default: "''"
  - name: popperStyle
    description: 自定义 popper 的 style
  - name: width
    description: 自定义 popper 的宽度
  - name: zIndex
    description: 控制层级，默认 1992
    default: "1992"
  - name: tabindex
    description: 指定 reference 的原生 tabindex，用于 tab 控制
    default: "-1"
  - name: disabledClose
    description: 禁止关闭
  - name: residentPopover
    description: 是否常驻 popper，为 true 时，在 mounted 中会显示
    default: "false"
  - name: size
    description: popper 的大小，支持 small、large、huge
    values:
      - small
      - large
      - huge
    default: "'small'"
  - name: showOnOverflow
    description: 是否在内容溢出时显示
    default: "false"
  - name: control
    description: |-
      是否控制在面板展开时，reference 的 active 态，
      active 态需要组件自己实现：引入 mixin（popover-control）, 并且在组件中实现相应逻辑
    default: "true"
slots:
  - default
  - reference
events:
  - input
  - created
  - show
  - hide
