name: AbcContentEmpty
description: 空值状态展示
usage: "<template> <div style=\"margin: 40px auto; display: flex\"> <div style=\"flex: 1\"> <abc-content-empty size=\"mini\" value=\"这是mini\"></abc-content-empty> </div> <div style=\"flex: 1\"> <abc-content-empty size=\"small\" value=\"这是small\"></abc-content-empty> </div> <div style=\"flex: 1\"> <abc-content-empty data-cy=\"storybook-test-default\" value=\"暂无数据\" ></abc-content-empty> </div> <div style=\"flex: 1\"> <abc-content-empty size=\"large\" value=\"这是large\"></abc-content-empty> </div> </div> </template> <script> export default {} </script>"
props:
  - name: size
    description: abc icon 对应name
    values:
      - mini
      - small
      - normal
      - large
    default: "'normal'"
  - name: iconName
    description: abc icon 对应name
    default: "''"
  - name: iconSize
    description: abc icon 对应size
    default: "40"
  - name: iconColor
    description: abc icon 对应color, 默认P1
    default: "'#ced0da'"
  - name: top
    description: 自定义到顶位置top
    default: "''"
  - name: value
    description: 自定义文案，默认暂无数据
    default: "'暂无数据'"
  - name: showIcon
    description: 是否展示icon
    default: "true"
slots:
  - icon
  - default
