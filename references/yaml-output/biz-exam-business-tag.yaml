name: BizExamBusinessTag
description: ""
usage: "<template> <abc-flex :gap=\"16\" vertical> <abc-descriptions grid :column=\"2\" bordered :label-width=\"200\"> <abc-descriptions-item label=\"isCloudTag\"> <abc-switch v-model=\"isCloudTag\" @change=\"isOutSourcingTag = !isCloudTag\" ></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"isOutSourcingTag\"> <abc-switch v-model=\"isOutSourcingTag\" @change=\"isCloudTag = !isOutSourcingTag\" ></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"isText\"> <abc-switch v-model=\"isText\"></abc-switch> </abc-descriptions-item> </abc-descriptions> <abc-card padding-size=\"small\"> <biz-exam-business-tag :type=\"type\" :sub-type=\"subType\" :is-cloud-tag=\"isCloudTag\" :is-out-sourcing-tag=\"isOutSourcingTag\" :cloudSupplierFlag=\"1\" :is-text=\"isText\" :coop-flag=\"2\" ></biz-exam-business-tag> </abc-card> </abc-flex> </template> <script> export default { data() { return { isCloudTag: true, isOutSourcingTag: false, type: GoodsTypeEnum.EXAMINATION, subType: GoodsSubTypeEnum[GoodsTypeEnum.EXAMINATION].Test, isText: false, } }, } </script>"
