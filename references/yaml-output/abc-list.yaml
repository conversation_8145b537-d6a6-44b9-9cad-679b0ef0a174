name: AbcList
description: 列表组件，用于展示列表数据
usage: "<template> <div style=\" display: flex; flex-direction: column; justify-content: space-between; background-color: transparent; \" > <div style=\"padding: 20px\"> <abc-list data-cy=\"storybook-test-base1\" :data-list=\"dataList\" readonly show-icon @click-item=\"itemClick\" :need-selected=\"true\" :scrollable=\"false\" ></abc-list> </div> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" size=\"large\" :list-item-flex-config=\"{ align: 'flex-start' }\" > <template #prepend=\"{ isSelected }\"> <abc-icon :icon=\"isSelected ? 's-vip-color' : 's-wechat-color'\" style=\"margin-top: 4px\" ></abc-icon> </template> <template #append=\"{ isSelected }\"> <abc-badge :value=\"1\" :theme=\"isSelected ? 'warn' : 'danger'\" ></abc-badge> </template> </abc-list> </div> <label>显示分割线</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" size=\"large\" :custom-padding=\"[10, '20px']\" show-icon show-divider :no-top-border=\"false\" need-selected ></abc-list> </div> <label>显示分割线-hover时不隐藏</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" size=\"large\" :custom-padding=\"[10, '20px']\" show-icon show-divider :no-top-border=\"false\" :noHoverBorder=\"false\" need-selected ></abc-list> </div> <label>设置分割线属性</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" size=\"large\" show-icon show-divider need-selected :no-top-border=\"false\" :divider-config=\"{ variant: 'dashed', theme: 'light', margin: 'small' }\" > </abc-list> </div> <label>最后一条分割线不展示</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" size=\"large\" show-icon show-divider no-close-border need-selected :divider-config=\"{ variant: 'dashed', theme: 'light', margin: 'small' }\" > </abc-list> </div> <label>通过插槽设置 prepend 和 append</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" show-divider show-icon :custom-item-class=\"() => 'gnoscip'\" :hoverItemFunc=\"hoverItemFunc\" > <template #prepend> <abc-icon icon=\"s-wechat-color\"></abc-icon> </template> <template #append> <abc-badge :value=\"1\" theme=\"danger\"></abc-badge> </template> </abc-list> </div> <label @click=\"scrollIntoView\">插槽使用列表数据</label> <div style=\"padding: 20px\"> <abc-list ref=\"scrollList\" :create-key=\"createKey\" :data-list=\"dataList\" height=\"160px\" size=\"large\" show-icon need-selected :custom-item-class=\"'picsong'\" @click-item=\"itemClick\" > <template #prepend=\"{ item, index }\"> <abc-icon icon=\"s-wechat-color\"></abc-icon> {{ index }} </template> <template #default=\"{ item, index, isSelected }\"> <abc-flex flex=\"1\" :style=\"{ color: isSelected ? '#fff' : '#000' }\"> {{ item.title }}{{ index }} </abc-flex> </template> <template #append=\"{ item, index }\"> <abc-badge :value=\"1\" theme=\"danger\"></abc-badge> {{ index }} </template> </abc-list> </div> <label>插槽使用列表数据--readonly</label> <div style=\"padding: 20px\"> <abc-list :data-list=\"dataList\" :height=\"160\" size=\"large\" readonly show-icon :custom-item-class=\"'picsong'\" @click-item=\"itemClick\" > <template #prepend=\"{ item, index }\"> <abc-icon icon=\"s-wechat-color\"></abc-icon> {{ index }} </template> <template #default=\"{ item, index }\"> <abc-flex flex=\"1\"> {{ item.title }}{{ index }} </abc-flex> </template> <template #append=\"{ item, index }\"> <abc-badge :value=\"1\" theme=\"danger\"></abc-badge> {{ index }} </template> </abc-list> </div> </div> </template> <script> export default { data() { return { dataList: [ { id: 1, title: '湖南省郴州市北湖区下湄桥街道', }, { id: 2, title: '湖南省郴州市北湖区下湄桥街道', content: '湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道', checked: true, }, { id: 3, title: '湖南省郴州市北湖区下湄桥街道', content: '湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道', // checked: true, }, { id: 4, title: '湖南省郴州市北湖区下湄桥街道', content: '湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道', }, { id: 5, title: '湖南省郴州市北湖区下湄桥街道', content: '湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道', checked: true, }, { id: 6, title: '湖南省郴州市北湖区下湄桥街道', content: '湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道湖南省郴州市北湖区下湄桥街道', }, { id: 7, title: '成都市武侯区', content: '锦城大道666号', }, ], } }, methods: { itemClick(e, item) { console.log('itemClick') console.log(e, item) }, scrollIntoView() { console.log('scrollIntoView') this.$refs.scrollList.scrollToElement(3) this.$refs.scrollList.setSelectedKey(3) }, // 不允许第三个hover hoverItemFunc(item, index) { return index !== 2 }, createKey(item, i) { console.log('createKey', item, i) return item.id }, }, } </script>"
props:
  - name: height
    default: "'auto'"
  - name: dataList
    default: "[]"
  - name: needSelected
    default: "false"
  - name: showIcon
    description: 控制右侧箭头图标是否显示
    default: "false"
  - name: readonly
    default: "false"
  - name: size
    description: 尺寸：default/large
    values:
      - default
      - large
    default: "'default'"
  - name: showDivider
    default: "false"
  - name: noTopBorder
    default: "true"
  - name: noCloseBorder
    default: "false"
  - name: noHoverBorder
    default: "true"
  - name: dividerConfig
    description: |-
      divider 的配置属性 <br/>
      {
       variant: '',
       margin: '',
       theme: '',
       size: ''
      }
    default: "{}"
  - name: scrollConfig
    default: |-
      {
          // 0px
          paddingSize: 'none'
      }
  - name: listItemFlexConfig
  - name: createKey
    default: (item) => item.id
  - name: customItemClass
  - name: scrollable
    default: "true"
  - name: customPadding
    description: |-
      自定义item的padding,或者使用数组形式同时设置 [上下间距, 左右间距] eg: [8, 16] <br/>
      String: '8px' | '16px' | '24px' <br/>
      Number: 8 | 16 | 24
  - name: hoverItemFunc
    default: |-
      () => {
          return true;
      }
  - name: enableVirtualList
    default: "false"
  - name: virtualListConfig
    default: |-
      {
          scrollBarAlwaysShow: false
      }
slots:
  - name
events:
  - click-item
