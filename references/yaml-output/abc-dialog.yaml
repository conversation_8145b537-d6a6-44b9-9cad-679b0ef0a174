name: AbcDialog
description: 弹窗组件，用于展示弹窗内容
usage: "<template> <div style=\"min-height: 400px\"> 指定dialog size，决定dialog的宽度 <abc-select :width=\"120\" v-model=\"dialogSize\"> <abc-option v-for=\"it in dialogSizeOptions\" :label=\"it\" :value=\"it\" ></abc-option> </abc-select> <button @click=\"showDialog = true\">打开弹窗</button> <button @click=\"showTopDialog = true\">打开自定义top弹窗</button> <button @click=\"showResponsiveDialog = true\">打开响应式弹窗</button> <button @click=\"showDialog1 = true\">没header下边框</button> <button @click=\"showFullscreenDialog = true\">全屏弹窗</button> <AbcDialog v-model=\"showDialog\" ref=\"dialog\" title=\"费用预览\" append-to-body :auto-focus=\"false\" tabindex=\"-1\" size=\"large\" content-styles=\"padding: 0\" v-bind=\"$props\" @close-dialog=\"handleClose\" > <abc-button @click=\"handelClick\">toggle footer</abc-button> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div>this is content</div> <div v-if=\"showFooter\" slot=\"footer\" class=\"dialog-footer\"> <abc-button>确定</abc-button> <abc-button type=\"blank\" @click=\"showDialog = false\">取消</abc-button> </div> </AbcDialog> <AbcDialog :show-header-border-bottom=\"false\" v-model=\"showDialog1\" content-styles=\"width: 960px;max-height: 468px;\" :size=\"dialogSize\" append-to-body v-bind=\"$props\" @close-dialog=\"handleClose\" > <div>this is content</div> <div slot=\"footer\" class=\"dialog-footer\"> <abc-button>确定</abc-button> <abc-button type=\"blank\" @click=\"showDialog1 = false\">取消</abc-button> </div> </AbcDialog> <AbcDialog v-model=\"showTopDialog\" append-to-body :size=\"dialogSize\" custom-top=\"32px\" content-styles=\"width: 600px\" v-bind=\"$props\" @close-dialog=\"handleClose\" > <div>this is content</div> <div slot=\"footer\" class=\"dialog-footer\"> <abc-button>确定</abc-button> <abc-button type=\"blank\" @click=\"showTopDialog = false\" >取消</abc-button > </div> </AbcDialog> <AbcDialog v-model=\"showResponsiveDialog\" append-to-body responsive :size=\"dialogSize\" header-size=\"large\" v-bind=\"$props\" @close-dialog=\"handleClose\" > <div>this is content</div> <template #top-extend> <abc-tips-card-v2 theme=\"primary\"> <div style=\"width: 972px\"> <abc-flex justify=\"space-between\" align=\"center\"> <p> 包装包装无码商品在档案产品标识码设置无码无码商品在档案产品标识码设置无码 </p> </abc-flex> </div> </abc-tips-card-v2> </template> <div slot=\"footer\" class=\"dialog-footer\"> <abc-button>确定</abc-button> <abc-button type=\"blank\" @click=\"showResponsiveDialog = false\" >取消</abc-button > </div> </AbcDialog> <AbcDialog v-model=\"showFullscreenDialog\" append-to-body fullscreen :size=\"dialogSize\" v-bind=\"$props\" @close-dialog=\"handleClose\" > <div>这是个全屏弹窗</div> <div slot=\"footer\" class=\"dialog-footer\"> <abc-button>确定</abc-button> <abc-button type=\"blank\" @click=\"showFullscreenDialog = false\" >取消</abc-button > </div> </AbcDialog> </div> </template> <script> export default { data() { return { showDialog: false, showFullDialog: false, showTopDialog: false, showResponsiveDialog: false, showFullscreenDialog: false, showDialog1: false, showFooter: false, dialogSize: 'default', dialogSizeOptions: [ 'default', 'small', 'middle', 'large', 'huge', 'hugely', ], } }, methods: { handleClose() { p.value = false }, handelClick() { this.showFooter = !this.showFooter this.$nextTick(() => { this.$refs.dialog.updateDialogHeight() }) }, }, } </script>"
props:
  - name: value
    default: "false"
  - name: title
    description: 设置空字符串可以不展示title
    default: "''"
  - name: headerSize
    description: header大小 'auto', 'normal', 'large'
    values:
      - auto
      - normal
      - large
    default: "'auto'"
  - name: showHeaderBorderBottom
    description: 是否展示header下边框，为false的时候，内容区域padding-top会变成0
    default: "true"
  - name: type
    default: "'normal'"
  - name: closeOnClickModal
    description: 开启后点击蒙层也会关弹窗
    default: "false"
  - name: showClose
    description: 是否展示关闭按钮
    default: "true"
  - name: size
    description: |-
      dialog size 影响宽度，请取用设计figma标注
      default,
      small: 360px
      medium: 420px
      large: 640px
      xlarge: 780px
      huge: 960px
      hugely: 1200px
    values:
      - default
      - small
      - medium
      - large
      - xlarge
      - huge
      - hugely
    default: "'default'"
  - name: customClass
    description: 自定义class，这个会放到abc-dialog上
    default: "''"
  - name: customStyles
    description: 自定义class，这个会放到abc-dialog上
    default: "{}"
  - name: headerStyle
    description: header Style
    default: "''"
  - name: noAnimation
    description: 不需要打开动画
    default: "false"
  - name: beforeClose
    description: 关闭前回调
    default: "null"
  - name: contentStyles
    description: 会直接给 dialog-body 的样式
    default: "''"
  - name: otherHeight
    default: "0"
  - name: full
    description: 该属性废弃，设置了等价于 responsive
    default: "false"
  - name: fullscreen
    description: 是否全屏
    default: "false"
  - name: responsive
    description: |-
      是否响应式，响应式规则：
      width: 80vw，当 size 指定为 hugely 时，宽度固定为 1200px
      height: 90vh 当 size 指定为 hugely 时，宽度固定为 76vh
      min-width: 1200px
      弹窗中心点，距离顶部 50%
    default: "false"
  - name: customTop
    description: 少数情况下需要自定义dialog距离顶部的距离
    default: "''"
  - name: shadow
    default: "false"
  - name: autoFocus
    default: "true"
  - name: disabledKeyboard
    description: 禁用键盘操作 esc 关闭
    default: "false"
  - name: appendToBody
    description: |-
      是否加载到body下，这种情况需要在最外层设置样式
      因为历史原因这个值默认是false
      后面所有的dialog 请设置为true
    default: "false"
  - name: defaultTop
    default: "52"
  - name: showDialogCover
    description: 是否展示cover，默认true
    default: "true"
slots:
  - top-extend
  - title
  - title-append
  - search
  - default
  - left-extend
  - right-extend
  - bottom-extend
  - footer
  - shadow
  - cover-dialog
  - z-extend
events:
  - close
  - open
  - input
  - close-dialog
