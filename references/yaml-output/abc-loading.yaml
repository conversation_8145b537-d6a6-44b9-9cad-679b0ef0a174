name: AbcLoading
description: 加载中组件，用于展示加载中状态，请尽量使用 指令的形式调用 v-abc-loading 见：UI-PC/Directive/v-abc-loading
usage: "<template> <div> <abc-button data-cy=\"storybook-test-default\" @click=\"handleClick\" >{{ loading ? '关闭' : '开启' }}loading</abc-button > </div> </template> <script> export default { data() { return { loading: null, } }, methods: { handleClick() { if (this.loading) { this.loading.close() this.loading = null return } this.loading = this.$Loading({ text: 'loading~~~~', }) }, }, } </script>"
props:
  - name: type
    description: |-
      loading类型 page 有白色底  content 局部加载
      !逐渐废弃，应该使用variant
    default: VARIANT.EMPTY
  - name: text
    description: 图标下方文案
    default: "''"
  - name: customClass
    description: abc-loading-wrapper 上的自定义className
    default: "''"
  - name: noCover
    description: 不要背景遮罩 ！废弃，请使用 with-cover
    default: "false"
  - name: coverOpaque
    description: 背景不透明
    default: "false"
  - name: customTop
    description: 自定义 loading-spinner 距离父元素高度
    default: "''"
  - name: center
    description: 让 loading-spinner 在abc-loading-wrapper居中，center为true时 customTop 会失效
    default: "false"
  - name: large
    description: loading-spinner 大小 32px：用于全局和列表翻页加载，其中全局加载时有白色底， 不设置修饰默认就是large
    default: "false"
  - name: middle
    description: loading-spinner 大小 24px：用于通栏的页面加载，如右侧智能诊断
    default: "false"
  - name: small
    description: loading-spinner 大小 16px：用于局部加载，如左侧就诊历史
    default: "false"
  - name: blue
    description: loading-spinner 圈圈颜色 蓝色
    default: "false"
  - name: gray
    description: loading-spinner 圈圈颜色 灰色
    default: "false"
  - name: white
    description: loading-spinner 圈圈颜色 白色
    default: "false"
  - name: variant
    description: 变体，支持 page和content
    default: VARIANT.CONTENT
  - name: theme
    description: 主题，对应3种颜色
    default: THEME.BLUE
  - name: size
    description: 支持3种大小
    default: SIZE.NORMAL
