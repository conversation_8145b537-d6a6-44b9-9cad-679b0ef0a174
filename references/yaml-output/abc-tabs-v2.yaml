name: AbcTabsV2
description: 选项卡组件，用于切换不同内容，仅仅是选项卡，没有 tabs-pane 等配套组件，需要根据业务选用其他组件渲染内容
usage: "<template> <div> <div class=\"mdx-title\">✨ 统计值、提示值</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options2\" /> <div class=\"mdx-title\">✨ 禁用项</div> <abc-tabs-v2 v-model=\"currentTab1\" :option=\"options3\" /> <div class=\"mdx-title\">✨ 限制item最大字数</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options4\" :item-max-length=\"5\" /> <div class=\"mdx-title\">✨ 自定义间距</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" :custom-gap=\"60\" /> <div class=\"mdx-title\">✨ 居中展示</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" center /> <div class=\"mdx-title\">✨ 带icon</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options5\" /> <div class=\"mdx-title\">✨ 带tag</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options6\" /> <div class=\"mdx-title\">✨ 带红点</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options7\" /> <div class=\"mdx-title\">✨ 分割线</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options8\" /> <div class=\"mdx-title\">✨ 取消动画</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" :need-animation=\"false\" /> <div class=\"mdx-title\">✨ 切换需要确认</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" :beforeChange=\"changeFn\" /> <div class=\"mdx-title\">✨ 响应式 + node slot</div> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"\">默认</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"middle\"> middle</abc-radio> <abc-radio label=\"large\">large</abc-radio> <abc-radio label=\"huge\">huge</abc-radio> </abc-radio-group> </abc-space> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options9\" :adaptation=\"true\" :size=\"size\" > <template #node=\"{ node, active }\"> <abc-flex align=\"center\"> <abc-icon icon=\"search\" size=\"14\" :color=\"active ? '#459eff' : '#d4edfd'\" style=\"margin-right: 4px\" ></abc-icon ><span>{{ node.label }}</span> </abc-flex> </template> </abc-tabs-v2> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options9\" :adaptation=\"true\" type=\"border-card\" can-append :size=\"size\" > <template #node=\"{ node, active }\"> <abc-flex align=\"center\"> <abc-icon icon=\"search\" size=\"14\" :color=\"active ? '#459eff' : '#d4edfd'\" style=\"margin-right: 4px\" ></abc-icon ><span>{{ node.label }}</span> </abc-flex> </template> </abc-tabs-v2> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options9\" :adaptation=\"true\" type=\"outline\" :size=\"size\" > <template #node=\"{ node, active }\"> <abc-flex align=\"center\"> <abc-icon icon=\"search\" size=\"14\" :color=\"active ? '#459eff' : '#d4edfd'\" style=\"margin-right: 4px\" ></abc-icon ><span>{{ node.label }}</span> </abc-flex> </template> </abc-tabs-v2> <div class=\"mdx-title\">✨ indicator 偏移</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" :indicator-offset=\"4\" /> <div class=\"mdx-title\">✨ 纵向 default</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options6\" direction=\"vertical\" :width=\"300\" /> <div class=\"mdx-title\">✨ 纵向 card</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options6\" direction=\"vertical\" :width=\"300\" theme=\"card\" /> <div class=\"mdx-title\">✨ border-card 不封边</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options6\" type=\"border-card\" :no-close-border=\"true\" /> <div class=\"mdx-title\">✨ border-card 有分割线</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options8\" type=\"border-card\" /> <div class=\"mdx-title\">✨ border-card 可删除，可添加</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" type=\"border-card\" can-append can-del @del=\"handleDel\" @append=\"handleAppend\" /> <div class=\"mdx-title\">✨ outline 设置item-min-width</div> <abc-tabs-v2 v-model=\"currentTab\" :option=\"options1\" type=\"outline\" :item-min-width=\"150\" /> </div> </template> <script> export default { data() { return { currentTab: 0, currentTab1: 0, options1: [ { label: '门诊挂号', value: 0, }, { label: '理疗预约', value: 1, }, { label: '线下预约', value: 2, }, ], options2: [ { label: '门诊挂号', value: 0, statisticsNumber: 333, maxStatisticsNumber: 99, }, { label: '理疗预约', value: 1, noticeNumber: 333, maxNoticeNumber: 99, }, { label: '线下预约', value: 2, statisticsNumber: 0, }, ], options3: [ { label: '线上预约', value: 0, }, { label: '我是禁用项', value: 1, disabled: true, disabledTips: '这是被禁用的原因', }, { label: '线下预约', value: 2, }, ], options4: [ { label: '门诊挂号门诊挂号', value: 0, }, { label: '理疗预约理疗预约', value: 1, }, { label: '线下预约线下预约', value: 2, }, ], options5: [ { label: '线上预约', value: 0, iconOption: { size: '14px', icon: 'n-warning-circle-line', }, }, { label: '预约', value: 1, }, { label: '线下预约', value: 2, }, ], options6: [ { label: '线上预约', value: 0, }, { label: '线下预约', value: 1, tagOption: { variant: 'outline', theme: 'success', shape: 'square', size: 'mini', text: '待诊', }, }, { label: '线上理疗预约', value: 2, statisticsNumber: 22, maxStatisticsNumber: 99, }, { label: '线下理疗预约', value: 3, withRedDot: true, }, ], options7: [ { label: '线上预约', value: 0, }, { label: '线下预约', value: 1, withRedDot: true, }, { label: '阿斯达', value: 2, }, ], options8: [ { label: '线上预约', value: 0, }, { label: '从我后面出现分割线分组', value: 1, separation: true, }, { label: '线下预约', value: 2, }, ], options9: [ { label: '线上预约', value: 0, }, { label: '线下预约', value: 1, }, { label: '微信', value: 2, }, { label: '支付宝', value: 3, }, { label: '银联', value: 4, }, { label: '拼多多', value: 5, }, { label: '饿了么', value: 6, }, { label: '美团', value: 7, }, { label: '显示图标', value: 8, iconOption: { size: '14px', icon: 'form', }, }, { label: '显示红点', value: 9, withRedDot: true, }, { label: '显示 noticeNumber', value: 10, noticeNumber: 33, disabled: true, }, { label: '显示 statisticsNumber', value: 11, statisticsNumber: 44, }, { label: '这是一段很长的文本后面跟了一个 tag', value: 12, tagOption: { variant: 'outline', theme: 'success', shape: 'square', size: 'mini', text: '待诊', }, }, ], size: '', } }, methods: { changeFn({ value, done }) { if (value !== 1) done() else alert('当前项目不能切换') }, handleDel(index) { alert(`del ${index} 项`) }, handleAppend() { alert('append') }, }, } </script>"
props:
  - name: option
    description: |-
      tabs options: {
          value,
          label,
          noticeNumber, // 警告值，红色上标
          maxNoticeNumber, // 最大警告值，超出显示 +
          statisticsNumber, // 统计数字，括号中显示
          maxStatisticsNumber, // 统计数字最大值，超出会显示99+
          disabled,
          disabledTips, // 禁用时显示的提示
          separation, // 分割标志
          customClass,
          iconOption, // 透传给 abc-icon {size: '16px', icon: 'icon-name'}
          tagOption, //透传给 abc-tag-v2 {variant:'outline',theme:'success',shape:'square',size:'mini',text:'待诊'}
          withRedDot, // 是否显示红点
      }[]
  - name: direction
    values:
      - horizontal
      - vertical
    default: "'horizontal'"
  - name: width
    description: |-
      当设置为纵向时，可以指定tabs宽度
      (vertical 模式生效)
  - name: v-model
  - name: size
    description: small/middle/large/huge；不传会依据不同的type匹配不同的默认size
    values:
      - small
      - middle
      - large
      - huge
    default: "''"
  - name: disableIndicator
    description: 是否禁用下方滑块
    default: "false"
  - name: borderStyle
    description: abc-tabs border style
  - name: type
    description: |-
      default/outline/border-card；不传默认default
      注意：当 direction 为 vertical 时，只支持 default
    values:
      - default
      - outline
      - border-card
    default: "'default'"
  - name: useRouteMatch
    description: 是否优先依据路由匹配active
    default: "true"
  - name: canAppend
    description: 是否允许添加tab-item
    default: "false"
  - name: canDel
    description: 是否允许删除tab-item
    default: "false"
  - name: noCloseBorder
    description: border-card 情况下只有下border
    default: "false"
  - name: adaptation
    description: |-
      是否开启适配, 当wrapper展示不下，会折叠成更多
      （default模式生效, vertical模式不生效）
    default: "false"
  - name: center
    description: tab item 居中展示
    default: "false"
  - name: beforeChange
    description: tab 是否切换根据业务代码来决定
    default: "null"
  - name: customGap
    description: |-
      default 模式下，自定义间距
      （不建议使用）
  - name: itemMaxLength
    description: item 最大字数，超过此字数文字的内容将有渐隐效果
    default: "0"
  - name: itemMinWidth
    description: |-
      item 最小宽度, 如果设置了itemMinWidth，item将添加属性justify-content: center
      （非default模式生效）
    default: "0"
  - name: needAnimation
    description: |-
      是否需要动画
      （在有indicator 时生效）
    default: "true"
  - name: indicatorOffset
    description: |-
      indicator 需要偏移的距离
      （在有indicator 时生效， 只能向上偏移）
    default: "0"
  - name: border
    description: |-
      default 模式下是否需要border
      默认true
    default: "true"
  - name: theme
    description: |-
      direction 为 vertical 时，支持theme,默认default
      可选值 [ default, card ]
    default: "'default'"
slots:
  - node
  - border-card-append
events:
  - change
  - append
  - del
