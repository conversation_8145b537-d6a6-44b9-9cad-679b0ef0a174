name: AbcInputTag
description: 标签输入组件，用于将输入内容转化为标签进行展示
usage: "<template> <div style=\"display: flex; flex-direction: column; justify-content: space-around\" > <div> <div style=\"margin: 12px 0 8px\">size default</div> <AbcInputTag data-cy=\"storybook-test-default\" v-model=\"count1\" ></AbcInputTag> </div> <div> <div style=\"margin: 12px 0 8px\"> size medium，tag的size是huge，input是medium </div> <AbcInputTag v-model=\"count1\" size=\"medium\"></AbcInputTag> </div> <div> <div style=\"margin: 12px 0 8px\">size large</div> <AbcInputTag v-model=\"count1\" size=\"large\"></AbcInputTag> </div> <div> <div style=\"margin: 12px 0 8px\">disabled</div> <AbcInputTag v-model=\"count2\" disabled></AbcInputTag> </div> <div> <div style=\"margin: 12px 0 8px\">readonly</div> <AbcInputTag v-model=\"readonly\" readonly></AbcInputTag> </div> <div> <div style=\"margin: 12px 0 8px\">自定义格式化展示</div> <AbcInputTag v-model=\"count1\" data-cy=\"storybook-test-custom\" :dataFormatter=\"dataFormatter\" :input-width=\"100\" size=\"large\" ></AbcInputTag> </div> </div> </template> <script> export default { data() { return { count1: [], count2: ['disabled'], count3: [], readonly: ['readonly'], } }, methods: { dataFormatter(val) { return val.replace(/(.{5})/g, '$1 ') }, }, } </script>"
props:
  - name: size
    description: 表单尺寸大小
    values:
      - tiny/small/medium/large/huge
    default: "''"
  - name: adaptiveWidth
    description: 是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度
    default: undefined
  - name: value
  - name: width
  - name: placeholder
  - name: focusPlaceholder
  - name: disabled
  - name: readonly
  - name: tabindex
    description: 原生属性
  - name: max
    description: 最多几个标签
  - name: closable
    description: 是否支持 tag删除
    default: "false"
  - name: dataFormatter
  - name: inputWidth
    description: 输入input的最小宽度
  - name: allowRepeat
    description: 是否允许重复
    default: "true"
events:
  - input
  - click
  - blur
  - focus
  - enter
  - repeat-trigger
  - change
  - up
  - down
