// 自动生成的文件，请勿手动修改
// 从 JSON 文件转换为 YAML 格式的组件文档数据

import * as yaml from 'js-yaml';

// 预处理对象，修复正则表达式中的转义问题
function preprocessObject(obj: any): any {
  if (typeof obj === 'string') {
    // 对于包含正则表达式的字符串，将 \d, \w, \s 等转义序列转换为 \\d, \\w, \\s
    return obj.replace(/\\([dwsWDSbBnrtfv])/g, '\\\\$1');
  } else if (Array.isArray(obj)) {
    return obj.map(item => preprocessObject(item));
  } else if (obj && typeof obj === 'object') {
    const result: any = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = preprocessObject(value);
    }
    return result;
  }
  return obj;
}

// 使用 js-yaml 库进行 JSON 到 YAML 的转换
function jsonToYaml(obj: any): string {
  try {
    // 预处理对象，修复正则表达式转义问题
    const processedObj = preprocessObject(obj);
    
    // 使用 js-yaml 库进行转换，配置选项确保正确处理特殊字符
    return yaml.dump(processedObj, {
      indent: 2,
      lineWidth: -1, // 不限制行宽，避免长字符串被截断
      noRefs: true,  // 不使用引用，确保输出简洁
      quotingType: '"', // 使用双引号
      forceQuotes: false, // 只在必要时使用引号
      flowLevel: -1, // 使用块级样式而不是流式样式
    });
  } catch (error) {
    console.error('YAML 转换失败:', error);
    return '';
  }
}

// 将组件名从 kebab-case 转换为 PascalCase
function kebabToPascalCase(str: string): string {
  return str
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

// 动态导入所有 JSON 文件并转换为 YAML 格式
function loadComponentDocs() {
  const componentDocs: Record<string, any> = {};
  
  // 使用 require.context 动态导入所有 JSON 文件
  const requireContext = require.context('./abc-ui', false, /\.json$/);
  
  requireContext.keys().forEach((fileName) => {
    try {
      const jsonData = requireContext(fileName);
      const componentName = fileName.replace('./', '').replace('.json', '');
      const pascalName = kebabToPascalCase(componentName);
      
      // 转换为 YAML 格式
      const yamlContent = jsonToYaml(jsonData);
      
      componentDocs[pascalName] = {
        name: jsonData.name || pascalName,
        resolvedName: pascalName,
        kebabName: componentName,
        yamlContent: yamlContent, // 保存 YAML 格式内容
        jsonData: jsonData, // 同时保存原始 JSON 数据以便机器解析
        sourceFile: fileName.replace('./', '') + '.json',
        // 提取关键信息用于快速访问
        description: jsonData.description || '',
        usage: jsonData.usage || '',
        props: jsonData.props || [],
        events: jsonData.events || [],
        slots: jsonData.slots || []
      };
      
    } catch (error) {
      console.error(`加载组件文档失败: ${fileName}`, error);
    }
  });
  
  return componentDocs;
}

// 默认组件映射（保持与原有系统兼容）
const defaultComponentMapping: Record<string, string> = {
  'abc-address-selector': 'AbcAddressSelector',
  'abc-autocomplete': 'AbcAutocomplete',
  'abc-badge': 'AbcBadge',
  'abc-button-group': 'AbcButtonGroup',
  'abc-button-pagination': 'AbcButtonPagination',
  'abc-button': 'AbcButton',
  'abc-card': 'AbcCard',
  'abc-cascader': 'AbcCascader',
  'abc-checkbox-button': 'AbcCheckboxButton',
  'abc-checkbox': 'AbcCheckbox',
  'abc-collapse': 'AbcCollapse',
  'abc-content-empty': 'AbcContentEmpty',
  'abc-date-pagination': 'AbcDatePagination',
  'abc-date-picker-bar': 'AbcDatePickerBar',
  'abc-date-picker': 'AbcDatePicker',
  'abc-date-time-range-picker': 'AbcDateTimeRangePicker',
  'abc-delete-icon': 'AbcDeleteIcon',
  'abc-descriptions': 'AbcDescriptions',
  'abc-dialog': 'AbcDialog',
  'abc-divider': 'AbcDivider',
  'abc-dropdown': 'AbcDropdown',
  'abc-edit-div': 'AbcEditDiv',
  'abc-file-view-v2': 'AbcFileViewV2',
  'abc-flex': 'AbcFlex',
  'abc-form': 'AbcForm',
  'abc-grid-selector-panel': 'AbcGridSelectorPanel',
  'abc-grid': 'AbcGrid',
  'abc-icon': 'AbcIcon',
  'abc-image': 'AbcImage',
  'abc-input-mobile': 'AbcInputMobile',
  'abc-input-number': 'AbcInputNumber',
  'abc-input-password': 'AbcInputPassword',
  'abc-input-style': 'AbcInputStyle',
  'abc-input-tag': 'AbcInputTag',
  'abc-input': 'AbcInput',
  'abc-layout': 'AbcLayout',
  'abc-link': 'AbcLink',
  'abc-list': 'AbcList',
  'abc-loading': 'AbcLoading',
  'abc-menu': 'AbcMenu',
  'abc-modal': 'AbcModal',
  'abc-notice': 'AbcNotice',
  'abc-option-card': 'AbcOptionCard',
  'abc-oss-img': 'AbcOssImg',
  'abc-pagination': 'AbcPagination',
  'abc-popover': 'AbcPopover',
  'abc-preview': 'AbcPreview',
  'abc-progress': 'AbcProgress',
  'abc-qr-code': 'AbcQrCode',
  'abc-radio': 'AbcRadio',
  'abc-result': 'AbcResult',
  'abc-scrollbar': 'AbcScrollbar',
  'abc-search-icon': 'AbcSearchIcon',
  'abc-select-input': 'AbcSelectInput',
  'abc-select': 'AbcSelect',
  'abc-space': 'AbcSpace',
  'abc-statistic': 'AbcStatistic',
  'abc-step': 'AbcStep',
  'abc-steps': 'AbcSteps',
  'abc-suggestions-panel': 'AbcSuggestionsPanel',
  'abc-switch': 'AbcSwitch',
  'abc-table': 'AbcTable',
  'abc-tabs-v2': 'AbcTabsV2',
  'abc-tag-v2': 'AbcTagV2',
  'abc-text': 'AbcText',
  'abc-textarea': 'AbcTextarea',
  'abc-tips-card-v2': 'AbcTipsCardV2',
  'abc-tips': 'AbcTips',
  'abc-toast': 'AbcToast',
  'abc-tooltip-info': 'AbcTooltipInfo',
  'abc-tooltip': 'AbcTooltip',
  'abc-transfer-v2': 'AbcTransferV2',
  'abc-tree-v2': 'AbcTreeV2'
};

// 加载并导出组件文档数据
export const componentDocs = loadComponentDocs();
export { defaultComponentMapping };

console.log(`总共加载了 ${Object.keys(componentDocs).length} 个组件的文档数据`);
