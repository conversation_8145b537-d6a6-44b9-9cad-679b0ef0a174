<template>
  <AbcFlex :gap="24" vertical>
    <abc-date-picker-bar
      v-model="label"
      :options="options"
      size="tiny"
      value-format="YYYY-MM-DD"
      data-cy="e2e-date-picker-bar-range"
      @clear="handleClear"
      @change="handleDateChange"
    >
    </abc-date-picker-bar>
    <abc-date-picker-bar
      v-model="label"
      :options="options"
      size="small"
      value-format="YYYY-MM-DD"
      @clear="handleClear"
      @change="handleDateChange"
    >
    </abc-date-picker-bar>
    <abc-date-picker-bar
      v-model="label"
      :options="options"
      value-format="YYYY-MM-DD"
      @clear="handleClear"
      @change="handleDateChange"
    >
    </abc-date-picker-bar>
    <abc-date-picker-bar
      v-model="label"
      :options="options"
      size="large"
      value-format="YYYY-MM-DD"
      @clear="handleClear"
      @change="handleDateChange"
    >
    </abc-date-picker-bar>
    <p>{{ date }}</p>
  </AbcFlex>
</template>
<script>
export default {
  data() {
    return {
      label: DatePickerBarOptions.DAY.label,
      options: [
        DatePickerBarOptions.DAY,
        DatePickerBarOptions.WEEK,
        DatePickerBarOptions.MONTH,
        DatePickerBarOptions.YEAR,
        {
          label: 'last_month',
          name: '上月',
          getValue() {
            return [getLastMonthStartDate(), getLastMonthEndDate()]
          },
        },
      ],
      date: '',
    }
  },
  methods: {
    handleDateChange(value) {
      console.log('handleDateChange', value)
      this.date = value
    },
    handleClear() {
      this.label = DatePickerBarOptions.DAY.label
    },
  },
}
</script>
