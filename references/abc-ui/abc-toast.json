{"name": "AbcToast", "description": "Toast组件，提供全局展示提示信息的能力，支持多种主题，支持 $Toast() 函数式调用", "usage": "<template> <div class=\"toast-wrapper\"> <button @click=\"toast\">toast</button> <button @click=\"toastHandle\">直接调用toast函数, 并绑定referenceEl</button> </div> </template> <script> export default { methods: { toast() { this.$Toast({ message: '成功信息', type: 'success', }) }, toastHandle() { Toast({ message: '成功信息', type: 'success', referenceEl: document.querySelectorAll('.toast-wrapper')[0], }) }, }, } </script>", "events": ["click"]}