<template>
  <div>
    <div class="mdx-title">✨ tiny</div>
    <abc-cascader
      v-model="value"
      :options="options"
      size="tiny"
      :disabled="true"
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="禁用"
      :width="200"
    >
    </abc-cascader>
    <div class="mdx-title">✨ small</div>
    <abc-cascader
      v-model="value"
      data-cy="storybook-test-default"
      :options="options"
      size="small"
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="请输入"
      :width="200"
      no-icon
      clearable
    >
    </abc-cascader>
    <div class="mdx-title">✨ default</div>
    <abc-cascader
      v-model="value1"
      :options="[]"
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="没有options"
      :width="200"
    >
    </abc-cascader>
    <div class="mdx-title">✨ medium</div>
    <abc-cascader
      v-model="value"
      :options="options"
      size="medium"
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="请输入"
      :width="200"
      no-icon
      :clearable="false"
    >
    </abc-cascader>
    <div class="mdx-title">✨ large</div>
    <abc-cascader
      v-model="value"
      :options="options"
      size="large"
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="请输入"
      :width="200"
      no-icon
      :clearable="false"
    >
    </abc-cascader>
    <div class="mdx-title">✨ 仅显示下边框</div>
    <abc-cascader
      v-model="value"
      :options="options"
      only-bottom-border
      :props="{ children: 'childs', label: 'name', value: 'id' }"
      placeholder="show value"
      :width="200"
      showValue="医生推荐     医生推荐"
    >
    </abc-cascader>
    <div class="mdx-title">✨ 测试数据</div>
    <abc-cascader
      v-model="value2"
      :options="options5"
      :props="{ children: 'options', label: 'label', value: 'value' }"
      placeholder="请输入"
      :width="200"
    >
    </abc-cascader>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options,
      options5,
      value: [],
      value1: [],
      value2: [],
    }
  },
}
</script>
