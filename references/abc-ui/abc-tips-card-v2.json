{"name": "AbcTipsCardV2", "description": "提示卡片组件，用于展示定制提示信息", "usage": "<template> <abc-flex vertical gap=\"large\"> <AbcTipsCardV2 theme=\"primary\"> 这是primary内容 </AbcTipsCardV2> <AbcTipsCardV2 theme=\"warning\"> 这是warning内容 </AbcTipsCardV2> <AbcTipsCardV2 theme=\"primary\" :custom-icon=\"{ name: 'n-info-circle-fill', color: 'red' }\" > 自定义icon </AbcTipsCardV2> <AbcTipsCardV2 theme=\"warning\"> <AbcText theme=\"success-light\">使用AbcText自定义text</AbcText> </AbcTipsCardV2> <AbcTipsCardV2 theme=\"primary\"> <div> <p>多行文本</p> <p>多行文本</p> <p>多行文本</p> </div> </AbcTipsCardV2> </abc-flex> </template> <script> export default {} </script>", "props": [{"name": "theme", "description": "主题：primary/warning", "default": "'primary'"}, {"name": "variant", "description": "变体：outline/fill", "values": ["outline", "fill"], "default": "'outline'"}, {"name": "icon", "description": "是否显示icon", "default": "true"}, {"name": "customIcon", "description": "自定义icon", "default": "{\n    name: '',\n    color: ''\n}"}, {"name": "align", "description": "内容对齐方式，有title时，center不生效", "values": ["left", "center"], "default": "'left'"}, {"name": "borderRadius", "description": "tipsCard 是否有圆角，固定6px", "default": "true"}, {"name": "customClass", "description": "自定义 tipsCard 的 样式", "default": "''"}, {"name": "title", "description": "自定义 tipsCard 的 title", "default": "''"}, {"name": "width", "default": "'100%'"}, {"name": "operateOptions", "description": "operate默认配置，指定了<slot name=\"operate\"></slot>后，此配置不生效"}], "slots": ["operate", "default"]}