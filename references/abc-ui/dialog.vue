<template>
  <div style="min-height: 400px">
    指定dialog size，决定dialog的宽度
    <abc-select :width="120" v-model="dialogSize">
      <abc-option
        v-for="it in dialogSizeOptions"
        :label="it"
        :value="it"
      ></abc-option>
    </abc-select>
    <button @click="showDialog = true">打开弹窗</button>
    <button @click="showTopDialog = true">打开自定义top弹窗</button>
    <button @click="showResponsiveDialog = true">打开响应式弹窗</button>
    <button @click="showDialog1 = true">没header下边框</button>
    <button @click="showFullscreenDialog = true">全屏弹窗</button>
    <AbcDialog
      v-model="showDialog"
      ref="dialog"
      title="费用预览"
      append-to-body
      :auto-focus="false"
      tabindex="-1"
      size="large"
      content-styles="padding: 0"
      v-bind="$props"
      @close-dialog="handleClose"
    >
      <abc-button @click="handelClick">toggle footer</abc-button>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div>this is content</div>
      <div v-if="showFooter" slot="footer" class="dialog-footer">
        <abc-button>确定</abc-button>
        <abc-button type="blank" @click="showDialog = false">取消</abc-button>
      </div>
    </AbcDialog>
    <AbcDialog
      :show-header-border-bottom="false"
      v-model="showDialog1"
      content-styles="width: 960px;max-height: 468px;"
      :size="dialogSize"
      append-to-body
      v-bind="$props"
      @close-dialog="handleClose"
    >
      <div>this is content</div>
      <div slot="footer" class="dialog-footer">
        <abc-button>确定</abc-button>
        <abc-button type="blank" @click="showDialog1 = false">取消</abc-button>
      </div>
    </AbcDialog>
    <AbcDialog
      v-model="showTopDialog"
      append-to-body
      :size="dialogSize"
      custom-top="32px"
      content-styles="width: 600px"
      v-bind="$props"
      @close-dialog="handleClose"
    >
      <div>this is content</div>
      <div slot="footer" class="dialog-footer">
        <abc-button>确定</abc-button>
        <abc-button type="blank" @click="showTopDialog = false"
          >取消</abc-button
        >
      </div>
    </AbcDialog>
    <AbcDialog
      v-model="showResponsiveDialog"
      append-to-body
      responsive
      :size="dialogSize"
      header-size="large"
      v-bind="$props"
      @close-dialog="handleClose"
    >
      <div>this is content</div>
      <template #top-extend>
        <abc-tips-card-v2 theme="primary">
          <div style="width: 972px">
            <abc-flex justify="space-between" align="center">
              <p>
                包装包装无码商品在档案产品标识码设置无码无码商品在档案产品标识码设置无码
              </p>
            </abc-flex>
          </div>
        </abc-tips-card-v2>
      </template>
      <div slot="footer" class="dialog-footer">
        <abc-button>确定</abc-button>
        <abc-button type="blank" @click="showResponsiveDialog = false"
          >取消</abc-button
        >
      </div>
    </AbcDialog>
    <AbcDialog
      v-model="showFullscreenDialog"
      append-to-body
      fullscreen
      :size="dialogSize"
      v-bind="$props"
      @close-dialog="handleClose"
    >
      <div>这是个全屏弹窗</div>
      <div slot="footer" class="dialog-footer">
        <abc-button>确定</abc-button>
        <abc-button type="blank" @click="showFullscreenDialog = false"
          >取消</abc-button
        >
      </div>
    </AbcDialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      showDialog: false,
      showFullDialog: false,
      showTopDialog: false,
      showResponsiveDialog: false,
      showFullscreenDialog: false,
      showDialog1: false,
      showFooter: false,
      dialogSize: 'default',
      dialogSizeOptions: [
        'default',
        'small',
        'middle',
        'large',
        'huge',
        'hugely',
      ],
    }
  },
  methods: {
    handleClose() {
      p.value = false
    },
    handelClick() {
      this.showFooter = !this.showFooter
      this.$nextTick(() => {
        this.$refs.dialog.updateDialogHeight()
      })
    },
  },
}
</script>
