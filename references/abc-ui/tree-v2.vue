<template>
  <div style="width: 320px">
    <abc-tree-v2
      data-cy="storybook-test-tree-v2"
      :data="data"
      node-key="id"
      :props="{ label: 'name' }"
    >
    </abc-tree-v2>
  </div>
</template>
<script>
export default {
  data() {
    return {
      data: [
        {
          name: '西成药',
          id: '1',
          children: [
            {
              name: '抗感染药消化用药消化用药消化用药消化用药消化用药',
              id: '1-1',
            },
            {
              name: '抗感染药1',
              id: '1-2',
            },
          ],
        },
        {
          name: '呼吸用药',
          id: '2',
          children: [
            {
              name: '青霉素类',
              id: '2-1',
              children: [
                {
                  name: '抗感染药2',
                  id: '2-1-1',
                },
                {
                  name: '消化用药3',
                  id: '2-1-2',
                },
              ],
            },
          ],
        },
      ],
    }
  },
}
</script>
