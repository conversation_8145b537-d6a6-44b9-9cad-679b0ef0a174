{"name": "BizCustomizationOptions", "description": "", "usage": "<template> <div> <abc-button @click=\"visible = true\"> 打开设置弹窗 </abc-button> <abc-flex> 当前数据： </abc-flex> <ul> <li v-for=\"item in dataList\" :key=\"item.id\"> <span>排序：{{ item.sort }}</span> <span style=\"margin-left: 8px\">内容：{{ item.content }}</span> </li> </ul> <biz-customization-options v-if=\"visible\" v-model=\"visible\" :data-list=\"dataList\" title=\"设置弹窗\" show-icon :on-submit=\"onSubmit\" > </biz-customization-options> </div> </template> <script> export default { data() { return { visible: false, dataList: [ { id: 11111, content: '选项1', sort: 0, }, { id: 22222, content: '选项2', sort: 1, }, { id: 33333, content: '选项3', sort: 2, }, ], } }, methods: { onSubmit(dataList, deleteDataList) { console.log( '%c dataList\\n', 'background: green; padding: 0 5px', dataList, ) console.log( '%c deleteDataList\\n', 'background: green; padding: 0 5px', deleteDataList, ) this.dataList = dataList }, }, } </script>", "props": [{"name": "value", "description": "弹窗是否展示", "default": "false"}, {"name": "title", "description": "标题，设置空字符串可以不展示，参考dialog组件", "default": "''"}, {"name": "showIcon", "description": "是否展示icon", "default": "false"}, {"name": "defaultIcon", "description": "默认icon图标", "default": "'s-user-color'"}, {"name": "closeOnClickModal", "description": "开启后点击蒙层也会关弹窗，参考dialog组件", "default": "false"}, {"name": "size", "description": "dialog size 影响宽度，参考dialog组件\ndefault,\nsmall: 360px\nmedium: 420px\nlarge: 640px\nxlarge: 780px\nhuge: 960px\nhugely: 1200px", "values": ["default", "small", "medium", "large", "xlarge", "huge", "hugely"], "default": "'large'"}, {"name": "responsive", "description": "是否响应式，参考dialog组件，响应式规则：\nwidth: 80vw，当 size 指定为 hugely 时，宽度固定为 1200px\nheight: 90vh 当 size 指定为 hugely 时，宽度固定为 76vh\nmin-width: 1200px\n弹窗中心点，距离顶部 50%", "default": "false"}, {"name": "sortable", "description": "是否支持排序", "default": "true"}, {"name": "disabledDelete", "description": "是否禁用删除", "default": "false"}, {"name": "dataList", "description": "数据源，格式如下：\n{\n    id: string,\n    content: string,\n    sort: number,\n    icon?: string,\n    disabled?: boolean,\n    disableDelete?: boolean,\n}\nid: 唯一标识\ncontent: 内容\nsort: 排序序号，从0开始\nicon: 图标，不支持排序时可展示该图标\ndisabled: 是否禁用，禁用后不能编辑不能删除\ndisableDelete: 是否禁用删除", "default": "[]"}, {"name": "singleRowMaxLength", "description": "编辑/新增时可输入的最大字数", "default": "500"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "最大条数", "default": "20"}, {"name": "onClose", "description": "关闭弹窗的回调", "default": "() => {}"}, {"name": "onSubmit", "description": "确定的回调，入参有2个，第1个是调整后的数据源，第2个是被删除的数据源"}, {"name": "addNewItemFunc", "description": "新增item的回调\n需要返回一个JSON对象，可选属性参考dataList\n如果未传递改回调，则默认新增的格式是 { content: string, id: string, sort: number }", "default": "null"}], "slots": ["editor", "content"], "events": ["input"]}