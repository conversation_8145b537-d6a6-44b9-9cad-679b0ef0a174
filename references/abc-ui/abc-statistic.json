{"name": "AbcStatistic", "description": "统计组件，用于突出展示某个或某组数字、带描述的统计类数据", "usage": "<template> <abc-flex vertical gap=\"8\"> <abc-row :gutter=\"10\"> <abc-col :span=\"6\"> <abc-statistic title=\"864000.00\" content=\"总收入\" content-position=\"bottom\" ></abc-statistic> </abc-col> <abc-col :span=\"6\"> <abc-statistic theme=\"warning\" title=\"864000.00\" content=\"总收入\" content-position=\"bottom\" > </abc-statistic> </abc-col> <abc-col :span=\"6\"> <abc-statistic theme=\"primary\" title=\"864000.00\" content=\"总收入\"> </abc-statistic> </abc-col> <abc-col :span=\"6\"> <abc-statistic theme=\"primary\" title=\"864000.00\" content=\"总收入\"> </abc-statistic> </abc-col> </abc-row> <abc-divider></abc-divider> <abc-row :gutter=\"16\"> <abc-col :span=\"8\"> <abc-statistic data-cy=\"abc-statistic-test-default\" variant=\"outline\" top-title=\"实收\" top-content=\"2023-12-21 ~ 2024-12-32\" title=\"209999.00\" content=\"日均 2020.50\" > <template #labelTips> ogT1C01WTAksnYVBe3s7VXjRj-4U </template> </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic theme=\"warning\" variant=\"outline\" top-title=\"实收\" top-content=\"2023-12-21 ~ 2024-12-32\" title=\"209999.00\" content=\"日均 2020.50\" ></abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"2023-12-21 ~ 2024-12-32\" title=\"209999.00\" content=\"日均 2020.50\" ></abc-statistic> </abc-col> </abc-row> <abc-divider></abc-divider> <abc-row :gutter=\"16\"> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" content=\"日均 2020.50\" content-position=\"bottom\" > <template #labelTips>提示文字</template> </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" content=\"日均 2020.50\" top-content-position=\"right\" content-position=\"bottom\" > <template #labelTips>提示文字</template> </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" top-content-position=\"right\" content-position=\"bottom\" > <template #content> <abc-space> <span>环比近30天非会员日</span> <span style=\"color: var(--abc-color-Y2)\"> <abc-icon icon=\"n-price-trend-line-medium\"></abc-icon>32.50% </span> </abc-space> </template> </abc-statistic> </abc-col> </abc-row> <abc-row :gutter=\"16\"> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" top-content-position=\"right\" content=\"日均 2020.50\" bottom-content=\"本金4444.00 赠金4444.00\" fill-height > </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" top-content-position=\"bottom\" content=\"日均 2020.50\" bottom-content=\"本金4444.00 赠金4444.00\" content-position=\"bottom\" > </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"outline\" top-title=\"实收\" top-content=\"(近3次会员日)\" title=\"209999.00\" top-content-position=\"right\" bottom-content=\"本金4444.00 赠金4444.00\" fill-height > <template #content> <span>环比近30天非会员日</span> </template> <template #bottomContent> <div style=\"margin-top: 12px\"> 自定义样式与文案-自定义样式与文案 </div> </template> </abc-statistic> </abc-col> </abc-row> <abc-divider></abc-divider> <label>variant:colorful，支持theme:C1~C7及icon</label> <label>使用场景: 收费看板</label> <abc-row :gutter=\"16\"> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C1\" title=\"300\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C2\" title=\"300\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C3\" title=\"300\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C4\" title=\"300000000000000000000000000\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C4\" title=\"30000\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"4\"> <abc-statistic variant=\"colorful\" theme=\"C4\" title=\"3000000.00\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> </abc-row> <abc-row :gutter=\"16\"> <abc-col :span=\"8\"> <abc-statistic variant=\"colorful\" theme=\"C5\" title=\"300\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"colorful\" theme=\"C6\" title=\"300\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> <abc-col :span=\"8\"> <abc-statistic variant=\"colorful\" theme=\"C7\" title=\"300000000000000000000000000\" content=\"门诊人次\" icon=\"n-stethoscope-line\" > </abc-statistic> </abc-col> </abc-row> </abc-flex> </template> <script> export default {} </script>", "props": [{"name": "title", "description": "标题", "default": "''"}, {"name": "content", "description": "内容", "default": "''"}, {"name": "topTitle", "description": "outline模式顶部标题", "default": "''"}, {"name": "topContent", "description": "outline模式顶部内容", "default": "''"}, {"name": "topContentPosition", "description": "顶部内容显示位置: bottom/right", "default": "'bottom'"}, {"name": "variant", "description": "变体：fill/outline/colorful", "default": "'fill'"}, {"name": "theme", "description": "主题：default/warning/primary/C1-C7(仅variant为colorful)", "default": "'default'"}, {"name": "contentPosition", "description": "内容显示位置: right/bottom", "default": "'right'"}, {"name": "bottomContent", "description": "组件最底下的插槽默认内容，也可使用同名slot自定义展示", "default": "''"}, {"name": "fillHeight", "description": "是否充满父级高度", "default": "false"}, {"name": "icon", "description": "(仅variant为colorful)", "default": "''"}], "slots": [{"name": "labelTips", "description": "title 旁边显示 tooltip"}, {"name": "content", "description": "自定义 content"}, "bottomContent"]}