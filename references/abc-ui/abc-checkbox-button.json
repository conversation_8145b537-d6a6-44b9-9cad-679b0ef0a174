{"name": "AbcCheckboxButton", "description": "多选框的按钮形态，用于选择多个选项", "usage": "<template> <abc-flex vertical :gap=\"16\"> <abc-form is-excel item-no-margin> <abc-descriptions :column=\"2\" grid bordered :label-width=\"200\"> <abc-descriptions-item label=\"variant\" content-padding=\"0\"> <abc-select v-model=\"variant\" clearable> <abc-option label=\"plain\" value=\"plain\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"theme\" content-padding=\"0\"> <abc-select v-model=\"theme\" clearable> <abc-option label=\"dark\" value=\"dark\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"statisticsNumber\" content-padding=\"0\"> <abc-input v-model=\"statisticsNumber\" type=\"number\"></abc-input> </abc-descriptions-item> <abc-descriptions-item label=\"size\" content-padding=\"0\"> <abc-select v-model=\"size\" clearable> <abc-option label=\"mini\" value=\"mini\"></abc-option> <abc-option label=\"normal\" value=\"normal\"></abc-option> <abc-option label=\"small\" value=\"small\"></abc-option> <abc-option label=\"large\" value=\"large\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"disabled\"> <abc-switch v-model=\"disabled\"></abc-switch> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card padding-size=\"large\"> <abc-checkbox-button v-model=\"value\" :variant=\"variant\" :theme=\"theme\" :size=\"size\" :disabled=\"disabled\" :statistics-number=\"+statisticsNumber\" ></abc-checkbox-button> </abc-card> </abc-flex> </template> <script> export default { data() { return { value: false, variant: 'plain', theme: 'dark', statisticsNumber: 5, disabled: false, size: 'normal', } }, } </script>", "props": [{"name": "value", "default": "false"}, {"name": "checked", "description": "当前的选中状态"}, {"name": "disabled", "default": "false"}, {"name": "control", "description": "是否受控，受控时，只能通过父组件的value属性控制，自身点击不会更改状态", "default": "false"}, {"name": "type", "description": "绑定值的类型 boolean 或者 number", "default": "'boolean'"}, {"name": "customerStyle", "description": "自定义样式", "default": "{}"}, {"name": "label"}, {"name": "variant", "description": "变体，支持 plain，选中图标无边框", "values": ["plain"], "default": "''"}, {"name": "theme", "description": "主题，支持默认和 dark", "values": ["dark"], "default": "''"}, {"name": "size", "description": "支持normal和small两种大小", "values": ["mini", "small", "normal", "large"], "default": "'normal'"}, {"name": "statisticsNumber", "description": "统计数字", "default": "''"}], "slots": ["default"], "events": ["click", "input", "change"]}