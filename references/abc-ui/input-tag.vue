<template>
  <div
    style="display: flex; flex-direction: column; justify-content: space-around"
  >
    <div>
      <div style="margin: 12px 0 8px">size default</div>
      <AbcInputTag
        data-cy="storybook-test-default"
        v-model="count1"
      ></AbcInputTag>
    </div>
    <div>
      <div style="margin: 12px 0 8px">
        size medium，tag的size是huge，input是medium
      </div>
      <AbcInputTag v-model="count1" size="medium"></AbcInputTag>
    </div>
    <div>
      <div style="margin: 12px 0 8px">size large</div>
      <AbcInputTag v-model="count1" size="large"></AbcInputTag>
    </div>
    <div>
      <div style="margin: 12px 0 8px">disabled</div>
      <AbcInputTag v-model="count2" disabled></AbcInputTag>
    </div>
    <div>
      <div style="margin: 12px 0 8px">readonly</div>
      <AbcInputTag v-model="readonly" readonly></AbcInputTag>
    </div>
    <div>
      <div style="margin: 12px 0 8px">自定义格式化展示</div>
      <AbcInputTag
        v-model="count1"
        data-cy="storybook-test-custom"
        :dataFormatter="dataFormatter"
        :input-width="100"
        size="large"
      ></AbcInputTag>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      count1: [],
      count2: ['disabled'],
      count3: [],
      readonly: ['readonly'],
    }
  },
  methods: {
    dataFormatter(val) {
      return val.replace(/(.{5})/g, '$1 ')
    },
  },
}
</script>
