{"name": "AbcImage", "description": "图片组件，用于展示图片内容", "usage": "<template> <div> <abc-image :width=\"152\" :height=\"114\" :src=\"src\" data-cy=\"storybook-test-default\" fit=\"cover\" oss-process=\"image/resize,m_fill,w_104,h_104\" draggable=\"false\" enableDownloadHighQualityPic ></abc-image> <abc-image :width=\"100\" :height=\"100\" oss-style-name=\"AvatorCompress\" src=\"https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000197c4ff80fe84000/doctor/xpdGHJjEBQlicGP4QBLW8HEFvNmLnGgq_1638939309532.jpeg\" ></abc-image> <abc-image :width=\"100\" :height=\"100\" :src=\"src\"></abc-image> <abc-image :width=\"50\" :height=\"50\" :src=\"src\"></abc-image> <abc-image :width=\"38\" :height=\"32\" :src=\"src\"></abc-image> <abc-image :width=\"24\" :height=\"24\" :src=\"src\"></abc-image> <abc-image :width=\"16\" :height=\"16\" :src=\"src\"></abc-image> <abc-image :width=\"8\" :height=\"6\" :src=\"src\"></abc-image> <div> <abc-image :src=\"errSrc\" width=\"200\" height=\"200\" data-cy=\"storybook-test-error-default\" ></abc-image> </div> <div><abc-image :src=\"src\" width=\"300\" height=\"300\"></abc-image></div> </div> </template> <script> export default { data() { return { errSrc: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/ffffffff00000000197c4ff80fe84000/doctor/xpdGHJjEBQlicGP4QBLW8HEFvNmLnGgq_1638939309532-12213.jpeg', src: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/medical-record/v2-8795c0d9b4874e4d1f4c3861095339b4_r_4YMA3DboULl9.jpg?a=1', } }, } </script>", "props": [{"name": "src", "description": "img src属性"}, {"name": "alt", "description": "img元素属性 alt", "default": "''"}, {"name": "ossStyleName", "description": "oss img 压缩模式", "values": ["SignCompress", "licenseCompress", "reportCompress", "AvatorCompress"]}, {"name": "ossProcess", "description": "自定义 oss 处理方式", "default": "''"}, {"name": "defaultImg", "description": "src为空时加载默认图片", "default": "''"}, {"name": "fit", "description": "object-fit属性", "default": "''"}, {"name": "width", "description": "图片宽度\n支持number string类型[px % auto] 不传默认为auto"}, {"name": "height", "description": "图片高度\n支持number string类型 不传高宽默认为auto"}, {"name": "enableDownloadHighQualityPic", "description": "是否启用下载高清图", "default": "false"}], "slots": ["error"], "events": ["loader", "error", "click"]}