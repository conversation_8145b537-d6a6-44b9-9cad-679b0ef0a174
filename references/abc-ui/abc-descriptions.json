{"name": "AbcDescriptions", "description": "详情描述组件，用于展示一组信息", "usage": "<template> <abc-descriptions :column=\"4\" :label-width=\"56\" :bordered=\"false\" title=\"住院信息\" data-cy=\"e2e-descriptions\" > <abc-descriptions-item label=\"科室\">消化内科</abc-descriptions-item> <abc-descriptions-item label=\"科室\">消化内科</abc-descriptions-item> <abc-descriptions-item label=\"床位\">09</abc-descriptions-item> <abc-descriptions-item label=\"入院时间\">2022-09-09</abc-descriptions-item> <abc-descriptions-item label=\"入院诊断\" >社区获得性肺炎</abc-descriptions-item > <abc-descriptions-item label=\"住院医生\">陈志强</abc-descriptions-item> <abc-descriptions-item label=\"费别\">自费</abc-descriptions-item> <abc-descriptions-item label=\"费别2\" :label-width=\"44\" >医保</abc-descriptions-item > <abc-descriptions-item label=\"出院时间\">2022-09-09</abc-descriptions-item> <abc-descriptions-item label=\"档案号\">3303202304852</abc-descriptions-item> <abc-descriptions-item label=\"住院号\">234055555583</abc-descriptions-item> <abc-descriptions-item label=\"主管医师\">程思思</abc-descriptions-item> <abc-descriptions-item label=\"住院次数\">2</abc-descriptions-item> <abc-descriptions-item label=\"联系人关系\" :label-width=\"70\" >丈夫</abc-descriptions-item > </abc-descriptions> </template> <script> export default {} </script>", "props": [{"name": "title", "description": "标题", "default": "''"}, {"name": "column", "description": "总列数", "default": "3"}, {"name": "labelMargin", "description": "label 边距", "default": "8"}, {"name": "labelWidth", "description": "label 宽度", "default": "-1"}, {"name": "labelAlign", "description": "label 对齐方式：left | center | right | justify", "default": "'left'"}, {"name": "labelVerticalAlign", "description": "label 垂直对齐方式: top | center | bottom", "default": "''"}, {"name": "bordered", "description": "边框", "default": "true"}, {"name": "borderStyle", "description": "外部边框样式（包含标题底部border）", "default": "'solid'"}, {"name": "grid", "description": "网格", "default": "false"}, {"name": "hiddenLabelBorder", "description": "网格布局是否隐藏label右边框", "default": "false"}, {"name": "innerBorder", "description": "网格布局 内部边框样式（除标题底部border）", "default": "'solid'"}, {"name": "size", "description": "网格模式支持大小展示，default 32px; large: 40px\n非网格模式，small: 字号 12px", "default": "''"}, {"name": "contentPadding", "description": "abc-descriptions__view内容区域的padding，默认 ''", "default": "''"}, {"name": "ellipsis", "description": "是否显示省略号", "default": "false"}, {"name": "customTitleStyle", "description": "自定义头部样式", "default": "{}"}, {"name": "background", "description": "内容区域是否有背景色yellow\n颜色用于grid布局中", "default": "false"}, {"name": "disabled", "description": "是否展示disabled态\n用于grid布局中", "default": "false"}, {"name": "stretchLastItem", "description": "最后一个descriptions-item是否自适应撑满", "default": "false"}, {"name": "needInputStyle", "description": "是否需要input-style", "default": "true"}], "slots": ["default"]}