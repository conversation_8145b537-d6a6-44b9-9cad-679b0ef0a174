<template>
  <div>
    <abc-space>
      <abc-badge :value="value" theme="danger"></abc-badge>
      <abc-badge :value="value" theme="danger" variant="dot"></abc-badge>
      <abc-badge :value="value" theme="danger" variant="round"></abc-badge>
      <abc-badge
        data-cy="storybook-test-default"
        :value="value"
        theme="danger"
        variant="count"
      ></abc-badge>
    </abc-space>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 1,
    }
  },
}
</script>
