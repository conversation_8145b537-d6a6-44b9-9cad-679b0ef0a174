{"name": "AbcInputPassword", "description": "密码输入组件，用于输入密码内容，支持隐藏密码", "usage": "<template> <div> <abc-space style=\"margin-bottom: 16px\"> 支持loading： <abc-switch v-model=\"loading\"></abc-switch> </abc-space> <abc-input-password data-cy=\"storybook-test-default\" v-model=\"password\" :loading=\"loading\" ></abc-input-password> <div>password: {{ password }}</div> </div> </template> <script> export default { data() { return { password: '', loading: false, } }, } </script>", "props": [{"name": "allowExceedLength", "description": "是否允许输入密码超长", "default": "false"}, {"name": "value", "default": "''"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "最大长度，设置允许密码超长后，该属性无效", "default": "6"}, {"name": "freeInput", "description": "自由输入，开启后，不会展示分割格子，通过 maxLength 控制输入密码长度", "default": "false"}, {"name": "only<PERSON><PERSON><PERSON>", "description": "仅支持数字密码", "default": "true"}, {"name": "loading", "description": "loading 状态", "default": "false"}], "events": ["input", "clear", "enter"]}