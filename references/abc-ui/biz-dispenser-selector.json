{"name": "BizDispenserSelector", "description": "", "usage": "<template> <div style=\"padding: 20px\"> <h3>基础用法</h3> <biz-dispenser-selector v-model=\"selectedIds\" :dispensed-by-employee=\"defaultDispensers\" :status=\"status\" :employees=\"employees\" @change=\"handleChange\" /> <h3 style=\"margin-top: 20px\">禁用状态</h3> <biz-dispenser-selector v-model=\"selectedIds\" :dispensed-by-employee=\"defaultDispensers\" :status=\"status\" :employees=\"employees\" disabled /> <h3 style=\"margin-top: 20px\">默认发药员可修改</h3> <biz-dispenser-selector v-model=\"selectedIds\" :dispensed-by-employee=\"defaultDispensers\" :status=\"status\" :employees=\"employees\" :is-default-dispensed-by-employee-modified=\"true\" /> </div> </template> <script> export default { data() { return { selectedIds: [], defaultDispensers: [ { id: '1', name: '张三', }, ], employees: [ { employeeId: '1', employeeName: '张三', employeeNamePy: 'zhang<PERSON>', employeeNamePyFirst: 'ZS', status: 1, }, { employeeId: '2', employeeName: '李四', employeeNamePy: 'lisi', employeeNamePyFirst: 'LS', status: 1, }, { employeeId: '3', employeeName: '王五', employeeNamePy: 'wangwu', employeeNamePyFirst: 'WW', status: 1, }, ], status: 1, } }, methods: { handleChange(value) { console.log('选中的发药员:', value) }, }, } </script>"}