<template>
  <div style="width: 500px">
    <abc-collapse v-model="list" data-cy="storybook-test-default">
      <abc-collapse-item title="标题1" value="0">
        <div>
          Operation feedback: enable the users to clearly perceive their
          operations by style updates and interactive effects;
        </div>
        <div>
          Visual feedback: reflect current state by updating or rearranging
          elements of the page.
        </div>
      </abc-collapse-item>
      <abc-collapse-item title="标题2" value="1">
        <div>
          Operation feedback: enable the users to clearly perceive their
          operations by style updates and interactive effects;
        </div>
        <div>
          Visual feedback: reflect current state by updating or rearranging
          elements of the page.
        </div>
      </abc-collapse-item>
      <abc-collapse-item title="标题3" value="2">
        <div>
          Operation feedback: enable the users to clearly perceive their
          operations by style updates and interactive effects;
        </div>
        <div>
          Visual feedback: reflect current state by updating or rearranging
          elements of the page.
        </div>
      </abc-collapse-item>
    </abc-collapse>
  </div>
</template>
<script>
export default {
  data() {
    return {
      list: ['0'],
    }
  },
}
</script>
