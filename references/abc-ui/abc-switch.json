{"name": "AbcSwitch", "description": "开关组件，用于在两种状态之间切换", "usage": "<template> <div> <abc-switch data-cy=\"storybook-test-default\" v-model=\"value\"></abc-switch> </div> </template> <script> export default { data() { return { value: false, } }, } </script>", "props": [{"name": "value", "default": "false"}, {"name": "activeColor", "description": "激活状态颜色", "default": "''"}, {"name": "inactiveColor", "description": "非激活状态颜色", "default": "''"}, {"name": "disabled", "description": "禁用状态", "default": "false"}, {"name": "name", "description": "input 的 name", "default": "''"}, {"name": "width", "description": "数值: 36，或者字符串: 36px", "default": "''"}, {"name": "type", "description": "类型 指定 value的类型 支持传入boolean或者number", "default": "'boolean'"}, {"name": "theme", "description": "主题 fill text", "values": ["fill", "text"], "default": "'fill'"}], "events": ["input", "change"]}