{"name": "AbcInputMobile", "description": "手机号码输入组件，用于输入手机号码，支持国际区号", "usage": "<template> <div> <abc-input-mobile v-model=\"mobile\" data-cy=\"storybook-test-default\" :country-code.sync=\"countryCode\" :width=\"380\" ></abc-input-mobile> </div> </template> <script> export default { data() { return { mobile: '13883544239', countryCode: '86', } }, } </script>", "props": [{"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "value", "default": "''"}, {"name": "placeholder", "default": "''"}, {"name": "countryCode", "description": "区号代码", "default": "''"}, {"name": "countryCodeWidth", "description": "区号代码宽度， 默认61", "default": "61"}, {"name": "customSpaceBorderStyle", "description": "区号代码右边框， 默认solid", "values": ["solid", "dashed", "dotted"], "default": "'solid'"}, {"name": "width", "description": "整体宽度", "default": "230"}, {"name": "autoWidth", "description": "是否自动调整宽度", "default": "false"}, {"name": "showPrependIcon", "description": "是否显示图标", "default": "false"}, {"name": "iconColor", "description": "图标颜色", "default": "'#aab4bf'"}, {"name": "iconLeftPadding", "description": "图标左侧padding", "default": "12"}, {"name": "isDisabledCountryCode", "description": "是否禁用选区", "default": "false"}, {"name": "disabled", "description": "是否禁用"}, {"name": "readonly", "description": "是否只读"}, {"name": "isShowCountryCode", "description": "是否显示选区", "default": "true"}, {"name": "selectValidateEvent", "description": "下拉框是否通知 form-item 进行校验，默认为 false", "default": "false"}, {"name": "inputValidateEvent", "description": "输入框是否通知 form-item 进行校验，默认为 false", "default": "true"}, {"name": "clearable", "description": "支持清除，注意指定该属性后，icon 将不会显示", "default": "false"}, {"name": "autoFocus", "description": "自动聚焦到input", "default": "false"}], "slots": ["prepend"], "events": ["input", "update:countryCode", "input-enter", "input-change", "select-enter", "select-change"]}