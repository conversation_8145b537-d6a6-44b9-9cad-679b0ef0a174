<template>
  <div>
    <abc-button data-cy="storybook-test-default" @click="handleClick"
      >{{ loading ? '关闭' : '开启' }}loading</abc-button
    >
  </div>
</template>
<script>
export default {
  data() {
    return {
      loading: null,
    }
  },
  methods: {
    handleClick() {
      if (this.loading) {
        this.loading.close()
        this.loading = null
        return
      }
      this.loading = this.$Loading({
        text: 'loading~~~~',
      })
    },
  },
}
</script>
