{"name": "AbcLink", "description": "链接组件，用于跳转到指定链接", "usage": "<template> <abc-flex vertical gap=\"large\"> <abc-text size=\"large\" theme=\"gray\" bold> 主题 theme: </abc-text> <abc-space :size=\"60\"> <abc-space> <abc-link size=\"small\" href=\"https://fed.abczs.cn/storybook/?path=/docs/ui-pc-components-abctable--docs\" >链接按钮</abc-link > <abc-link href=\"https://fed.abczs.cn/storybook/?path=/docs/ui-pc-components-abctable--docs\" >链接按钮</abc-link > <abc-link size=\"large\" href=\"https://fed.abczs.cn/storybook/?path=/docs/ui-pc-components-abctable--docs\" >链接按钮</abc-link > </abc-space> <abc-space> <abc-link theme=\"warning\" size=\"small\">链接按钮</abc-link> <abc-link theme=\"warning\">链接按钮</abc-link> <abc-link theme=\"warning\" size=\"large\">链接按钮</abc-link> </abc-space> <abc-space> <abc-link theme=\"danger\" size=\"small\">链接按钮</abc-link> <abc-link theme=\"danger\">链接按钮</abc-link> <abc-link theme=\"danger\" size=\"large\">链接按钮</abc-link> </abc-space> <abc-space> <abc-link data-cy=\"storybook-test-default\" theme=\"default\" size=\"small\" >链接按钮</abc-link > <abc-link theme=\"default\">链接按钮</abc-link> <abc-link theme=\"default\" size=\"large\">链接按钮</abc-link> </abc-space> </abc-space> <abc-text size=\"large\" theme=\"gray\" bold> 禁用状态 disabled: </abc-text> <abc-space> <abc-link disabled>链接按钮</abc-link> <abc-link theme=\"warning\" disabled>链接按钮</abc-link> <abc-link theme=\"danger\" disabled>链接按钮</abc-link> </abc-space> <abc-text size=\"large\" theme=\"gray\" bold> 自定义插槽: </abc-text> <abc-space :size=\"60\"> <abc-space> <abc-link size=\"small\" ><abc-icon slot=\"prepend\" size=\"14\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > <abc-link ><abc-icon slot=\"prepend\" size=\"16\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > <abc-link size=\"large\" ><abc-icon slot=\"prepend\" size=\"16\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > </abc-space> <abc-space> <abc-link size=\"small\" ><abc-icon slot=\"append\" size=\"14\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > <abc-link ><abc-icon slot=\"append\" size=\"16\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > <abc-link size=\"large\" ><abc-icon slot=\"append\" size=\"16\" icon=\"n-download-line\"></abc-icon >链接按钮</abc-link > </abc-space> <abc-space> <abc-link> <abc-icon slot=\"prepend\" size=\"16\" icon=\"n-download-line\"></abc-icon> 链接按钮 <abc-icon slot=\"append\" size=\"16\" icon=\"n-right-line-medium\" ></abc-icon> </abc-link> </abc-space> </abc-space> </abc-flex> </template> <script> export default {} </script>", "props": [{"name": "size", "description": "尺寸：small/normal/large", "default": "'normal'"}, {"name": "theme", "description": "主题：primary/warning/danger/default", "default": "'primary'"}, {"name": "disabled", "description": "禁用状态"}, {"name": "href", "description": "链接URL"}, {"name": "target", "default": "'_blank'"}], "slots": ["prepend", "default", "append"], "events": ["click"]}