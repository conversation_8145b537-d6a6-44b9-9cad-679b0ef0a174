<template>
  <div>
    <abc-space>
      <abc-checkbox
        data-cy="storybook-test-number"
        v-model="value"
        type="number"
      >
        checkbox组件类型为number
      </abc-checkbox>
      <abc-checkbox data-cy="storybook-test-default" v-model="value2">
        checkbox组件
      </abc-checkbox>
      <abc-checkbox v-model="value3" shape="round">
        checkbox圆形组件
      </abc-checkbox>
      <abc-checkbox v-model="value5" shape="ring">
        checkbox圆形组件[仅图标显示颜色]
      </abc-checkbox>
      <abc-checkbox v-model="value4" :no-border="true">
        checkbox圆形组件
      </abc-checkbox>
    </abc-space>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 0,
      value2: true,
      value3: true,
      value4: false,
      value5: true,
      checked: 1,
    }
  },
  watch: {
    value(val) {
      console.log(val)
    },
    value2(val) {
      console.log(val)
    },
  },
}
</script>
