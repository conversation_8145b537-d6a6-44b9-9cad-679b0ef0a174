<template>
  <abc-flex :gap="24" vertical>
    <abc-space>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
        size="tiny"
        @change-date-pivot="handleDatePivotChange"
        data-cy="e2e-date-picker"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
        format="YYYY-MM-DD"
        size="small"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
        size="large"
      >
      </abc-date-picker>
    </abc-space>
    <abc-space>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
        size="tiny"
        :show-icon="false"
        @change-date-pivot="handleDatePivotChange"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :pickerOptions="pickerOptions"
        format="YYYY-MM-DD"
        :show-icon="false"
        size="small"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :show-icon="false"
        :pickerOptions="pickerOptions"
        shortcutName="abc"
      >
      </abc-date-picker>
      <abc-date-picker
        v-model="selectDate"
        :describe-list="describeList"
        :show-icon="false"
        :pickerOptions="pickerOptions"
        size="large"
        shortcutName="abc"
      >
      </abc-date-picker>
    </abc-space>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      selectDate: '2023-10-17',
      describeList: [
        {
          date: '2023-10-13',
          describe: '预10',
        },
        {
          date: '2023-10-19',
          describe: '预20',
        },
      ],
      pickerOptions: {
        disabledDate(date) {
          return date > new Date() || date <= new Date('2023-10-07')
        },
        shortcuts: [
          {
            text: '今天',
            onClick(cb) {
              const start = new Date()
              cb(start)
            },
          },
          {
            text: '昨天',
            onClick(cb) {
              const start = new Date()
              start.setTime(start.getTime() - 24 * 60 * 60 * 1000)
              cb(start)
            },
          },
        ],
        yearRange: {
          begin: 1930,
          end: 2040,
        },
      },
    }
  },
  methods: {
    handleDatePivotChange(args) {
      console.log('handleDatePivotChange', args)
    },
  },
}
</script>
