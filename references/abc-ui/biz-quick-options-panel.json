{"name": "BizQuickOptionsPanel", "description": "主要用于病历、医嘱事项等场景。", "usage": "<template> <abc-flex :gap=\"8\" vertical> <abc-flex> <abc-text>内容方向：</abc-text> <abc-radio-group v-model=\"vertical\"> <abc-radio :label=\"0\">横向</abc-radio> <abc-radio :label=\"1\">纵向</abc-radio> </abc-radio-group> </abc-flex> <biz-quick-options-panel :list=\"contentList\" :vertical=\"vertical\" @select=\"handleSelect\" @close=\"handleClose\" > </biz-quick-options-panel> </abc-flex> </template> <script> export default { data() { return { vertical: 0, contentList: [ { label: '', list: [ { label: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发健身房八九分不开', value: '抗卡积分和娜姐开发三级开发拿手机妇女节发几款三分看见啊是妇女节开发你撒娇咖啡撒放你家吧净空法师你分看见啊不能放假卡收费和娜姐说开发健身房八九分不开', }, { label: '高血压', value: '高血压', }, { label: '高血脂', value: '高血脂', }, { label: '心脏病', value: '心脏病', }, { label: '糖尿病', value: '糖尿病', }, { label: '精神疾病', value: '精神疾病', }, { label: '脑梗死', value: '脑梗死', }, { label: '肝炎', value: '肝炎', }, { label: '胃炎', value: '胃炎', }, { label: '肺结核', value: '肺结核', }, { label: '哮喘', value: '哮喘', }, { label: '鼻炎', value: '鼻炎', }, { label: '甲亢', value: '甲亢', }, { label: '高血压', value: '高血压', }, { label: '高血脂', value: '高血脂', }, { label: '心脏病', value: '心脏病', }, { label: '糖尿病', value: '糖尿病', }, { label: '精神疾病', value: '精神疾病', }, { label: '脑梗死', value: '脑梗死', }, { label: '肝炎', value: '肝炎', }, { label: '胃炎', value: '胃炎', }, { label: '肺结核', value: '肺结核', }, { label: '哮喘', value: '哮喘', }, { label: '鼻炎', value: '鼻炎', }, { label: '甲亢', value: '甲亢', }, ], }, ], } }, methods: { handleClose() { console.log('closed') }, handleSelect(val) { console.log('selected:', val) }, }, } </script>"}