<template>
  <div>
    <abc-space>
      <abc-checkbox v-model="isBlock">纵向排版</abc-checkbox>
      <abc-checkbox v-model="isDisabled">禁用</abc-checkbox>
      <abc-checkbox v-model="isCancel">支持反选</abc-checkbox>
    </abc-space>
    <abc-radio-group
      data-cy="storybook-test-default"
      v-model="value"
      :item-block="isBlock"
      style="margin-top: 16px"
    >
      <AbcRadio label="男" :disabled="isDisabled" :enable-cancel="isCancel"
        >男</AbcRadio
      >
      <AbcRadio
        label="女"
        :disabled="isDisabled"
        :enable-cancel="isCancel"
      ></AbcRadio>
    </abc-radio-group>
  </div>
</template>
<script>
export default {
  data() {
    return {
      value: 0,
      isBlock: false,
      isDisabled: false,
      isCancel: false,
    }
  },
}
</script>
