{"name": "AbcAddressSelector", "description": "", "usage": "<template> <abc-flex vertical :gap=\"16\"> <abc-form item-no-margin is-excel> <abc-descriptions :column=\"2\" grid :label-width=\"120\"> <abc-descriptions-item label=\"size\" content-padding=\"0\"> <abc-select v-model=\"size\"> <abc-option label=\"tiny\" value=\"tiny\">tiny</abc-option> <abc-option label=\"small\" value=\"small\">small</abc-option> <abc-option label=\"\" value=\"\"> 默认 </abc-option> <abc-option label=\"medium\" value=\"medium\"> medium </abc-option> <abc-option label=\"large\" value=\"large\"> large </abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"disabled\"> <abc-switch v-model=\"disabled\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"adaptiveWidth\"> <abc-switch v-model=\"adaptiveWidth\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"width\" content-padding=\"0\"> <abc-form-item> <abc-input-number size=\"small\" v-model=\"width\"></abc-input-number> </abc-form-item> </abc-descriptions-item> <abc-descriptions-item label=\"clearable\"> <abc-switch v-model=\"clearable\"></abc-switch> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card padding-size=\"small\"> <abc-address-selector v-model=\"address\" data-cy=\"storybook-test-default\" :size=\"size\" :width=\"width\" :clearable=\"clearable\" :disabled=\"disabled\" :adaptive-width=\"adaptiveWidth\" :placeholder=\"placeholder\" @change=\"handleChange\" > <template #extend> <div>自定义内容</div> </template> </abc-address-selector> <div style=\" margin-top: 16px; background-color: #f5f5f5; padding: 16px; border-radius: 4px; \" > address: {{ address }} </div> </abc-card> </abc-flex> </template> <script> export default { data() { return { address: { addressCityId: '510100', addressCityName: '成都市', addressProvinceId: '510000', addressProvinceName: '四川', addressDistrictId: '510108', addressDistrictName: '成华区', }, size: '', disabled: false, adaptiveWidth: false, width: 300, clearable: false, placeholder: '省/市/区', } }, methods: { handleChange(newVal, oldVal) { console.log('address change', newVal, oldVal) }, }, } </script>", "props": [{"name": "transform<PERSON><PERSON>in", "default": "true"}, {"name": "placement", "description": "弹出位置", "values": ["(top|bottom|left|right)(-start|-end)"], "default": "'bottom-start'"}, {"name": "boundariesPadding", "description": "弹出位置边界的内边距", "default": "5"}, {"name": "reference", "description": "popper 定位的引用元素"}, {"name": "popper"}, {"name": "offset", "description": "popper 定位的偏移量", "default": "0"}, {"name": "value"}, {"name": "visibleArrow", "description": "是否显示箭头"}, {"name": "transition"}, {"name": "arrowOffset", "description": "箭头的偏移量", "default": "35"}, {"name": "appendToBody", "description": "是否将弹出层追加到 body 上", "default": "true"}, {"name": "popperOptions", "default": "{\n    // gpuAcceleration: false,\n    eventsEnabled: false,\n    boundariesElement: 'viewport',\n    // modifiersIgnored: ['preventOverflow'],\n}"}, {"name": "innerCustomerStyle", "description": "弹出面板的自定义样式"}, {"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "placeholder", "description": "占位文本", "default": "'省／市／区'"}, {"name": "width", "description": "输入框宽度", "default": "240"}, {"name": "disabled", "description": "是否禁用"}, {"name": "clearable", "description": "是否显示清除图标", "default": "false"}], "slots": [{"name": "extend", "description": "面板的扩展内容"}], "events": ["input", "created", "change", "enter"]}