{"name": "AbcText", "description": "文本组件，用于展示文本内容", "usage": "<template> <abc-flex vertical gap=\"large\"> <abc-radio-group v-model=\"size\"> <abc-radio v-for=\"option in propsSize\" :label=\"option.size\" >{{ option.size }} - {{ option.desc }} </abc-radio> </abc-radio-group> <abc-space>加粗 <abc-switch v-model=\"bold\"></abc-switch> </abc-space> <abc-flex vertical style=\"background: rgb(248, 248, 248)\"> <abc-text v-for=\"theme in propsTheme\" :theme=\"theme\" :size=\"size\" :bold=\"bold\" > 主题 - {{ theme }} </abc-text> </abc-flex> </abc-flex> </template> <script> export default { data() { return { size: '', bold: false, } }, computed: { propsTheme() { return PropsTheme.filter(Boolean) }, propsSize() { return [ { size: 'tiny', desc: '10px', }, { size: 'mini', desc: '12px', }, { size: 'small', desc: '13px', }, { size: 'normal', desc: '14px', }, { size: 'large', desc: '16px', }, { size: 'xlarge', desc: '24px', }, { size: 'xxlarge', desc: '28px', }, { size: 'xxxlarge', desc: '32px', }, ] }, }, } </script>", "props": [{"name": "size", "description": "尺寸，控制字号，行高，不指定的情况下，继承自父组件", "values": ["tiny", "mini", "small", "normal", "large", "xlarge", "xxlarge", "xxxlarge"], "default": "''"}, {"name": "theme", "description": "主题", "values": ["black", "white", "gray", "gray-light", "blue", "primary", "primary-light", "warning", "warning-light", "danger", "danger-light", "success", "success-light"], "default": "''"}, {"name": "bold", "description": "是否加粗", "default": "false"}, {"name": "tag", "description": "指定组件的标签，默认是 span", "default": "'span'"}], "slots": ["default"]}