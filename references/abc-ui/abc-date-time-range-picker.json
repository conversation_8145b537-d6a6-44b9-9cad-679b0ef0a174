{"name": "AbcDateTimeRangePicker", "description": "日期选择器，用于选择日期、时间、日期范围、时间范围", "usage": "<template> <div> <abc-date-time-range-picker v-model=\"selectDate\" @change=\"handleChange\" size=\"tiny\" > </abc-date-time-range-picker> <div style=\"height: 8px\"></div> <abc-date-time-range-picker v-model=\"selectDate\" @change=\"handleChange\" size=\"small\" > </abc-date-time-range-picker> <div style=\"height: 8px\"></div> <abc-date-time-range-picker v-model=\"selectDate\" @change=\"handleChange\"> </abc-date-time-range-picker> <div style=\"height: 8px\"></div> <abc-date-time-range-picker v-model=\"selectDate\" @change=\"handleChange\" size=\"large\" > </abc-date-time-range-picker> </div> </template> <script> export default { data() { return { selectDate: [], } }, methods: { handleChange(args) { console.log('handleChange', args) }, }, } </script>", "props": [{"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "value", "description": "数组，有 2 个元素，类型可以为 Date 或者 String\nArray<Date|String>", "default": "null"}, {"name": "startDatePlaceholder", "description": "开始日期的占位符", "default": "'开始日期'"}, {"name": "endDatePlaceholder", "description": "结束日期的占位符", "default": "'结束日期'"}, {"name": "pickerOptions", "description": "日期选择器特有的选项对象:<br/>\nshortcuts: Array<{text: string, onClick: (cb) => void}> 快捷选择日期方式<br/>\ndisabledDate: (date: Date) => Boolean <br/>\nyearRange: {begin: number, end: number} 年份选择范围，闭区间，默认：1990-2040 <br/>\nonPick: ({minDate: Date, maxDate: Date}) => void 选中日期后会执行的回调，`daterange` 特有<br/>\ntodayText: string 指定今天的文字，默认显示的日期 <br/>\npivotByEndDate: boolean 是否以结束日期为基准显示面板，默认为 false，`daterange` 特有", "default": "{}"}, {"name": "describeList", "description": "日期描述列表 <br/>\n文字和图标二选一，图标优先展示\nArray<{date: Date, describe: string, describeIcon: string, describeClass: string}>", "default": "null"}, {"name": "clearable", "description": "是否显示清除按钮", "default": "true"}, {"name": "disabled", "description": "是否禁用时间选择器", "default": "false"}, {"name": "width", "description": "input 的宽度"}, {"name": "showWeeks", "description": "是否显示星期", "default": "true"}, {"name": "showIcon", "description": "是否展示日期 icon & delete icon", "default": "true"}, {"name": "dateIcon", "description": "删除 icon", "default": "'n-calendar-line'"}, {"name": "dateIconColor", "description": "删除 icon 的颜色", "default": "''"}, {"name": "placement", "description": "弹出框的位置", "default": "'bottom-start'"}, {"name": "defaultShowTimeCheck", "description": "是否默认展示时间选择器", "default": "true"}, {"name": "showTimeCheckOption", "description": "是否展示时分开关", "default": "true"}], "events": ["change-date-pivot", "input", "change", "clear"]}