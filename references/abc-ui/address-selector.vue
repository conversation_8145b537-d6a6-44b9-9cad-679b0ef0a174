<template>
  <abc-flex vertical :gap="16">
    <abc-form item-no-margin is-excel>
      <abc-descriptions :column="2" grid :label-width="120">
        <abc-descriptions-item :span="2" label="size">
          <abc-radio-group v-model="size">
            <abc-radio label="tiny">tiny</abc-radio>
            <abc-radio label="small">small</abc-radio>
            <abc-radio label=""> 默认 </abc-radio>
            <abc-radio label="medium"> medium </abc-radio>
            <abc-radio label="large"> large </abc-radio>
          </abc-radio-group>
        </abc-descriptions-item>
        <abc-descriptions-item label="disabled">
          <abc-switch v-model="disabled"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="adaptiveWidth">
          <abc-switch v-model="adaptiveWidth"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="width" content-padding="0">
          <abc-form-item>
            <abc-input-number size="small" v-model="width"></abc-input-number>
          </abc-form-item>
        </abc-descriptions-item>
        <abc-descriptions-item label="clearable">
          <abc-switch v-model="clearable"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="placeholder">
          {{ placeholder }}
        </abc-descriptions-item>
      </abc-descriptions>
    </abc-form>
    <abc-address-selector
      data-cy="storybook-test-default"
      :size="size"
      v-model="address"
      :width="width"
      :clearable="clearable"
      :disabled="disabled"
      :adaptive-width="adaptiveWidth"
      :placeholder="placeholder"
      @change="handleChange"
    ></abc-address-selector>
    <div>address: {{ address }}</div>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      address: {
        addressCityId: '510100',
        addressCityName: '成都市',
        addressProvinceId: '510000',
        addressProvinceName: '四川',
        addressDistrictId: '510108',
        addressDistrictName: '成华区',
      },
      size: '',
      disabled: false,
      adaptiveWidth: false,
      width: 300,
      clearable: false,
      placeholder: '省/市/区',
    }
  },
  methods: {
    handleChange(newVal, oldVal) {
      console.log('address change', newVal, oldVal)
    },
  },
}
</script>
