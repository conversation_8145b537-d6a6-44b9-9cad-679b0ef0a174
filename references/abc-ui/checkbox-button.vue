<template>
  <abc-descriptions :column="1">
    <abc-descriptions-item label="variant">
      <abc-space>
        <abc-checkbox-button v-model="variantValue" type="number">
          默认
        </abc-checkbox-button>
        <abc-checkbox-button
          data-cy="storybook-test-default"
          v-model="variantValue"
          type="number"
          statistics-number="2"
        >
          勿动该例子,用于测试
        </abc-checkbox-button>
        <abc-checkbox-button
          v-model="variantValue"
          type="number"
          statistics-number="0"
        >
          默认
        </abc-checkbox-button>
        <abc-checkbox-button
          v-model="variantValue2"
          type="number"
          variant="plain"
        >
          加工
        </abc-checkbox-button>
      </abc-space>
    </abc-descriptions-item>
    <abc-descriptions-item label="theme">
      <abc-space>
        <abc-checkbox-button v-model="themeValue" type="number">
          默认
        </abc-checkbox-button>
        <abc-checkbox-button
          v-model="themeValue"
          type="number"
          statistics-number="2"
        >
          默认
        </abc-checkbox-button>
        <abc-checkbox-button
          theme="dark"
          v-model="themeValue2"
          type="number"
          statistics-number="2"
        >
          dark
        </abc-checkbox-button>
      </abc-space>
    </abc-descriptions-item>
  </abc-descriptions>
</template>
<script>
export default {
  data() {
    return {
      variantValue: 0,
      variantValue2: 0,
      themeValue: 0,
      themeValue2: 0,
    }
  },
}
</script>
