{"name": "AbcTreeV2", "description": "树形组件，用于展示树形结构数据", "usage": "<template> <div style=\"width: 320px\"> <abc-tree-v2 data-cy=\"storybook-test-tree-v2\" :data=\"data\" node-key=\"id\" :props=\"{ label: 'name' }\" > </abc-tree-v2> </div> </template> <script> export default { data() { return { data: [ { name: '西成药', id: '1', children: [ { name: '抗感染药消化用药消化用药消化用药消化用药消化用药', id: '1-1', }, { name: '抗感染药1', id: '1-2', }, ], }, { name: '呼吸用药', id: '2', children: [ { name: '青霉素类', id: '2-1', children: [ { name: '抗感染药2', id: '2-1-1', }, { name: '消化用药3', id: '2-1-2', }, ], }, ], }, ], } }, } </script>", "props": [{"name": "data", "description": "树形结构数据: 参考props和nodeKey", "default": "[]"}, {"name": "nodeKey", "description": "数据唯一标识, 默认id", "default": "() => 'id'"}, {"name": "defaultExpandedKeys", "description": "默认展开的节点的 key 的数组"}, {"name": "renderContent", "description": "自定义渲染内容"}, {"name": "draggable", "description": "是否可拖拽", "default": "false"}, {"name": "allowDrag", "description": "判断节点是否可拖拽"}, {"name": "allowDrop", "description": "拖拽时判断目标节点是否能被放置"}, {"name": "props", "description": "指定节点属性\n{\n  children: 'children', // 子节点\n  label: 'name', // 展示名称\n  disabled: 'disabled', // 是否禁用\n}", "default": "function() {\n    return {\n        children: 'children',\n        label: 'name',\n        disabled: 'disabled',\n    };\n}"}, {"name": "indent", "description": "缩进", "default": "18"}, {"name": "icon", "description": "展示图标对应的 iconfont icon", "default": "'s-folder-color'"}, {"name": "showIcon", "description": "是否展示图标", "default": "true"}, {"name": "defaultCheckedKeys", "description": "默认选择的节点", "default": "[]"}, {"name": "paddingLeft", "description": "默认左边边距", "default": "8"}, {"name": "defaultExpandAll", "description": "默认全部展开", "default": "false"}], "events": ["node-drag-start", "node-drag-leave", "node-drag-enter", "node-drag-over", "node-drag-end", "node-drop", "node-expand"]}