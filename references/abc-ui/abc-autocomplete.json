{"name": "AbcAutocomplete", "description": "自动完成组件，用于输入框的自动完成，需要输入建议/辅助提示时使用。", "usage": "<template> <abc-flex vertical :gap=\"16\"> <abc-form item-no-margin is-excel> <abc-descriptions :column=\"2\" grid :label-width=\"300\"> <abc-descriptions-item label=\"尺寸（size）\" content-padding=\"0\"> <abc-select v-model=\"size\"> <abc-option label=\"tiny\" value=\"tiny\"></abc-option> <abc-option label=\"small\" value=\"small\"></abc-option> <abc-option label=\"normal\" value=\"normal\"></abc-option> <abc-option label=\"medium\" value=\"medium\"></abc-option> <abc-option label=\"large\" value=\"large\"></abc-option> </abc-select> </abc-descriptions-item> <abc-descriptions-item label=\"自适应宽度（adaptiveWidth）\"> <abc-switch v-model=\"adaptiveWidth\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"聚焦时显示（focusShow）\"> <abc-switch v-model=\"focusShow\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"可清空（clearable）\"> <abc-switch v-model=\"clearable\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"聚焦时占位文本（focusPlaceholder）\" content-padding=\"0\" > <abc-input v-model=\"focusPlaceholder\"></abc-input> </abc-descriptions-item> <abc-descriptions-item label=\"禁用（disabled）\"> <abc-switch v-model=\"disabled\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"只读（readonly）\"> <abc-switch v-model=\"readonly\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"获取建议时自动聚焦第一个（autoFocusFirst）\" > <abc-switch v-model=\"autoFocusFirst\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"常驻搜索建议（residentSugguestions）\"> <abc-switch v-model=\"residentSugguestions\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"只显示底部边框（onlyBottomBorder）\"> <abc-switch v-model=\"onlyBottomBorder\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"面板最大高度（maxHeight）\" content-padding=\"0\" > <abc-input v-model=\"maxHeight\"></abc-input> </abc-descriptions-item> <abc-descriptions-item label=\"是否显示空状态（showEmpty）\"> <abc-switch v-model=\"showEmpty\"></abc-switch> </abc-descriptions-item> </abc-descriptions> </abc-form> <abc-card padding-size=\"small\"> <abc-autocomplete v-model=\"value\" :size=\"size\" :adaptive-width=\"adaptiveWidth\" :focus-show=\"focusShow\" :clearable=\"clearable\" :focus-placeholder=\"focusPlaceholder\" :disabled=\"disabled\" :readonly=\"readonly\" :auto-focus-first=\"autoFocusFirst\" :resident-sugguestions=\"residentSugguestions\" :only-bottom-border=\"onlyBottomBorder\" :max-height=\"maxHeight\" :show-empty=\"showEmpty\" :async-fetch=\"true\" :fetch-suggestions=\"fetchData\" @enterEvent=\"handleSelect\" data-cy=\"storybook-test-default\" > <template slot=\"suggestion-header\"> <abc-space> <abc-select :width=\"91\"></abc-select> <abc-autocomplete :width=\"91\"></abc-autocomplete> </abc-space> <div class=\"suggestion-title\" style=\"display: flex; align-items: center; padding: 0 18px 0 8px\" > <div style=\"flex: 1\">姓名</div> <div style=\"width: 100px\">年龄</div> </div> </template> <template slot=\"suggestions\" slot-scope=\"props\"> <dt class=\"suggestions-item\" :class=\"{ selected: props.currentIndex == props.index }\" @click=\"handleSelect(props.suggestion)\" > <div style=\"flex: 1\">{{ props.suggestion.name }}</div> <div style=\"width: 100px\">{{ props.suggestion.age }}</div> </dt> </template> </abc-autocomplete> </abc-card> </abc-flex> </template> <script> export default { data() { return { size: 'normal', adaptiveWidth: false, focusShow: false, clearable: false, focusPlaceholder: '', disabled: false, readonly: false, autoFocusFirst: false, residentSugguestions: false, onlyBottomBorder: false, maxHeight: 0, showEmpty: false, options: [ { name: 'bubble', age: 10, }, { name: '刘喜喜喜喜喜喜喜喜', age: 12, }, { name: '王富民民民民民', age: 13, disabled: true, }, { name: '王二小', age: 14, }, { name: 'aaaaaaaaaaaaaaaaaaaaaaaaa', age: 15, }, { name: 'aaaaaaabbbbbbbbbb', age: 16, }, { name: 'acccccccccc', age: 16, }, { name: '张三三三', age: 22, disabled: true, }, { name: '李四五六七八', age: 22, disabled: true, }, ], value: '', } }, methods: { fetchData(key, callback) { return callback(this.options.filter((item) => item.name.includes(key))) }, handleSelect(data) { this.value = data.name }, }, } </script>", "props": [{"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "value", "description": "value"}, {"name": "fetchSuggestions", "description": "获取建议项，接受 callback 参数,callback 返回建议项列表", "default": "function() {\n    return () => {\n    };\n}"}, {"name": "filterSuggestions", "description": "筛选建议项，接受 key、callback 参数, callback 返回筛选后的建议项", "default": "function() {\n    return () => {\n    };\n}"}, {"name": "asyncFetch", "description": "是否开启异步获取建议项，配合 fetchSuggestions 使用", "default": "false"}, {"name": "focusShow", "description": "聚焦时是否展示 建议项", "default": "false"}, {"name": "focusWhenAppendClick", "description": "点击 prepend 的icon需要focus input", "default": "false"}, {"name": "type", "description": "输入框类型", "values": ["text、number"], "default": "'text'"}, {"name": "width", "description": "输入框宽度"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "可输入的最大长度", "default": "30"}, {"name": "innerWidth", "description": "建议项弹窗宽度"}, {"name": "customClass", "description": "建议项弹窗自定义Class"}, {"name": "clearable", "description": "是否支持清除搜索"}, {"name": "placeholder", "description": "输入框占位符"}, {"name": "focusPlaceholder", "description": "focus 时的占位符", "default": "''"}, {"name": "hidePlaceholder<PERSON>henFocus", "description": "focus 时隐藏 placeholder", "default": "false"}, {"name": "placement", "description": "建议项弹出位置"}, {"name": "disabled", "description": "是否禁用"}, {"name": "readonly", "description": "是否只读"}, {"name": "index"}, {"name": "tabindex"}, {"name": "delayTime", "description": "输入框延迟搜索时间", "default": "200"}, {"name": "keyboardEvent", "description": "阻止默认事件，触发方法，特殊按键直接响应"}, {"name": "autoFocusFirst", "description": "默认选中第一个suggestion", "default": "true"}, {"name": "popperOptions", "description": "popperOptions", "default": "{}"}, {"name": "residentSugguestions", "description": "常驻搜索建议", "default": "false"}, {"name": "onlyBottomBorder", "description": "input只有底部边框", "default": "false"}, {"name": "closeOnClickOutside", "description": "clickOutside 事件触发是否关闭建议项，返回 true 关闭，false 不关闭，默认为 true", "default": "() => true"}, {"name": "maxHeight", "description": "弹出面板滚动区域的最大高度，不包含 header 和 footer 的高度，作用在 class: abc-scrollbar-wrapper", "default": "''"}, {"name": "distinguishHalfAngleLength", "description": "是否区分全交半角的长度，与 maxLength 搭配使用，半角占 1 位，全角占 2 位", "default": "false"}, {"name": "inputCustomStyle", "description": "自定义input样式"}, {"name": "onlySelect", "description": "支持选中取值", "default": "false"}, {"name": "showEmpty", "description": "固定面板，建议项为空时，展示空状态", "default": "false"}], "slots": [{"name": "prepend", "description": "可以在input前跟单位等, 插入class"}, {"name": "suggestion-header", "description": "自定义搜索建议的header"}, {"name": "suggestion-item", "description": "自定义搜索建议的item"}, "suggestions", {"name": "suggestion-footer", "description": "自定义搜索建议的footer"}, {"name": "suggestion-fixed-footer", "description": "固定搜索建议的footer"}, {"name": "empty", "description": "这个 slot 不能控制面板的空状态，勿用"}, {"name": "append", "description": "可以在input后跟单位等, 插入class"}, {"name": "appendInner", "description": "可以在input后跟单位等, 插入select"}], "events": ["input", "change", "blur", "blurEvent", "focus", "enterEvent", "select", "clear", "right", "left"]}