{"name": "BizGoodsTypeCascader", "description": "", "usage": "<template> <div> <biz-goods-type-cascader ref=\"goodsTypeCascader\" v-model=\"selectedTypes1\" :goods-type-options=\"allTypes\" @change=\"handleChangeType1\" /> <biz-goods-type-cascader ref=\"goodsTypeCascader\" v-model=\"selectedTypes2\" :goods-type-options=\"allTypes\" :is-adapter-data=\"false\" @change=\"handleChangeType2\" /> </div> </template> <script> export default { setup() { const { allTypes, fetchAllGoodsTypes } = useGoodsType() // import { useGoodsType } from '@/components-composite/biz-goods-type-cascader/index'; return { allTypes, fetchAllGoodsTypes, } }, data() { return { selectedTypes1: [], selectedTypes2: [], goodsTypeOptions: [ { id: '12', parentId: '1', name: '西药', goodsType: 1, goodsSubType: 1, isLeaf: 1, sort: 2, children: [], }, { id: '14', parentId: '13', name: '中药饮片', goodsType: 1, goodsSubType: 2, goodsCMSpec: '中药饮片', isLeaf: 1, sort: 3, customTypes: [ { id: '1029255', typeId: 14, sort: 0, name: 'bbb', innerFlag: 0, }, { id: -14, name: '未指定', sort: 999, typeId: 14, }, ], children: [ { id: '1029255', typeId: 14, sort: 0, name: 'bbb', innerFlag: 0, }, { id: -14, name: '未指定', sort: 999, typeId: 14, }, ], }, { id: '16', parentId: '1', name: '中成药', goodsType: 1, goodsSubType: 3, isLeaf: 1, sort: 5, children: [], }, { id: '17', parentId: '2', name: '医疗器械', goodsType: 2, goodsSubType: 1, isLeaf: 1, sort: 6, customTypes: [ { id: '1029118', typeId: 17, sort: 0, name: '123123', innerFlag: 0, }, { id: -17, name: '未指定', sort: 999, typeId: 17, }, ], children: [ { id: '1029118', typeId: 17, sort: 0, name: '123123', innerFlag: 0, }, { id: -17, name: '未指定', sort: 999, typeId: 17, }, ], }, { id: '26', parentId: '7', name: '保健药品', goodsType: 7, goodsSubType: 2, isLeaf: 1, sort: 15, customTypes: [ { id: '1029239', typeId: 26, sort: 0, name: '123', innerFlag: 0, }, { id: -26, name: '未指定', sort: 999, typeId: 26, }, ], children: [ { id: '1029239', typeId: 26, sort: 0, name: '123', innerFlag: 0, }, { id: -26, name: '未指定', sort: 999, typeId: 26, }, ], }, { id: '27', parentId: '7', name: '保健食品', goodsType: 7, goodsSubType: 3, isLeaf: 1, sort: 16, children: [], }, { id: '28', parentId: '7', name: '其他商品', goodsType: 7, goodsSubType: 4, isLeaf: 1, sort: 17, children: [], }, ], } }, created() { this.fetchAllGoodsTypes(() => { // 模拟接口返回 // import GoodsAPI from 'api/goods'; // return GoodsAPI.fetchGoodsClassificationV3({ // queryType: 1, // needCustomType: 1, // }); return { data: { list: this.goodsTypeOptions, }, } }) }, methods: { handleChangeType1(a, b) { console.log('selectedTypes1', this.selectedTypes1) console.log('handleChangeType1', a, b) }, handleChangeType2(a, b) { console.log('selectedTypes2', this.selectedTypes2) console.log('handleChangeType2', a, b) }, }, } </script>"}