<template>
  <abc-flex vertical>
    <abc-descriptions :column="1" :bordered="false">
      <abc-descriptions-item label="variant">
        <abc-radio-group v-model="variant">
          <abc-radio label="fill"></abc-radio>
          <abc-radio label="outline"></abc-radio>
          <abc-radio label="ghost"></abc-radio>
          <abc-radio label="text"></abc-radio>
        </abc-radio-group>
      </abc-descriptions-item>
      <abc-descriptions-item label="theme">
        <abc-radio-group v-model="theme">
          <abc-radio label="default"></abc-radio>
          <abc-radio label="primary"></abc-radio>
          <abc-radio label="success"></abc-radio>
          <abc-radio label="warning"></abc-radio>
          <abc-radio label="danger"></abc-radio>
        </abc-radio-group>
      </abc-descriptions-item>
      <abc-descriptions-item label="disabled">
        <abc-switch v-model="disabled"></abc-switch>
      </abc-descriptions-item>
      <abc-descriptions-item label="loading">
        <abc-switch v-model="loading"></abc-switch>
      </abc-descriptions-item>
      <abc-descriptions-item label="图标">
        <abc-switch v-model="icon"></abc-switch>
      </abc-descriptions-item>
      <abc-descriptions-item label="图标位置">
        <abc-radio-group v-model="iconPosition">
          <abc-radio label="left"></abc-radio>
          <abc-radio label="right"></abc-radio>
        </abc-radio-group>
      </abc-descriptions-item>
      <abc-descriptions-item label="size">
        <abc-radio-group v-model="size">
          <abc-radio label="small"></abc-radio>
          <abc-radio label="normal"></abc-radio>
          <abc-radio label="large"></abc-radio>
        </abc-radio-group>
      </abc-descriptions-item>
      <abc-descriptions-item label="shape">
        <abc-radio-group v-model="shape">
          <abc-radio label="square"></abc-radio>
          <abc-radio label="round"></abc-radio>
        </abc-radio-group>
      </abc-descriptions-item>
      <abc-descriptions-item label="noBorderRadius">
        <abc-switch v-model="noBorderRadius"></abc-switch>
      </abc-descriptions-item>
      <abc-descriptions-item label="count">
        <abc-input-number
          size="small"
          v-model="count"
          :width="100"
        ></abc-input-number>
      </abc-descriptions-item>
      <abc-descriptions-item label="width">
        <abc-input-number
          size="small"
          v-model="width"
          :width="100"
        ></abc-input-number>
      </abc-descriptions-item>
    </abc-descriptions>
    <div style="padding-left: 12px">
      <abc-button
        :icon-position="iconPosition"
        :icon="iconName"
        :size="size"
        :shape="shape"
        :disabled="disabled"
        :loading="loading"
        :variant="variant"
        :theme="theme"
        :no-border-radius="noBorderRadius"
        :count="count"
        :width="width"
      >
        {{ variant }} - {{ theme }}
      </abc-button>
    </div>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      icon: false,
      size: 'normal',
      shape: 'square',
      disabled: false,
      loading: false,
      iconPosition: 'left',
      variant: 'fill',
      theme: 'default',
      noBorderRadius: false,
      count: 5,
      width: 120,
    }
  },
  computed: {
    iconName() {
      return this.icon ? 'n-setting-line' : ''
    },
  },
}
</script>
