<template>
  <abc-flex wrap="wrap" :gap="16">
    <abc-option-card
      v-model="selected"
      icon="n-xls-fill"
      selectable="icon-outside"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
      data-cy="storybook-test-option-card"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      icon="n-xls-fill"
      selectable="icon-outside"
      title="这是标题"
      theme="success"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      icon="n-xls-fill"
      selectable="icon-inside"
      theme="success"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      icon="n-xls-fill"
      selectable="false"
      theme="success"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      icon="n-xls-fill"
      :selectable="false"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      disabled
      icon="n-xls-fill"
      selectable="icon-outside"
      theme="success"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <abc-option-card
      v-model="selected"
      disabled
      icon="n-xls-fill"
      selectable="icon-inside"
      theme="success"
      title="这是标题"
      description="这是内容这是内容这是内容"
      @change="change"
      @click="click"
    ></abc-option-card>
    <div>{{ selected }}</div>
    <div>事件名称: {{ eventName }}</div>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      selected: true,
      eventName: '',
    }
  },
  methods: {
    click() {
      this.eventName = 'click'
    },
    change(e) {
      this.eventName = 'change'
    },
  },
}
</script>
