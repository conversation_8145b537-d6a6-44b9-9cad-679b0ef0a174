{"name": "AbcSelect", "description": "选择器组件", "usage": "<template> <div> <div class=\"mdx-title\">✨ tiny</div> <p> <abc-select data-cy=\"storybook-test-base1\" v-model=\"patientSex\" placeholder=\"no icon\" size=\"tiny\" no-icon clearable > <abc-option value=\"男\" label=\"男\" disabled></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <br /> <div class=\"mdx-title\">✨ small</div> <p> <abc-select data-cy=\"storybook-test-base6\" v-model=\"patientSex2\" placeholder=\"聚焦展示选项\" size=\"small\" focusShowOptions width=\"60\" inner-width=\"200\" > <abc-option value=\"初诊\" label=\"初诊\"></abc-option> <abc-option value=\"自费门诊自费门诊\" label=\"自费门诊自费门诊\" ></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ default</div> <p> <abc-select data-cy=\"storybook-test-base7\" v-model=\"patientSex1\" placeholder=\"没有option\" show-empty > </abc-select> <br /> <br /> <abc-select v-model=\"patientSex\" placeholder=\"请选择性别\" readonly> <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ medium</div> <p> <abc-select data-cy=\"storybook-test-base8\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"medium\" > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ large</div> <p> <abc-select data-cy=\"storybook-test-base9\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"large\" > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ onlyBottomBorder</div> <p> <abc-select data-cy=\"storybook-test-base10\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"large\" only-bottom-border > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\"> ✨ 给定value，但是没匹配上option, 展示空。默认选中值：不男不女 </div> <p> <abc-select data-cy=\"storybook-test-base11\" v-model=\"patientSex4\" placeholder=\"没有匹配上\" show-empty > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ 面板最小宽度38</div> <p> <abc-select data-cy=\"storybook-test-base12\" v-model=\"patientSex\" width=\"30\" no-icon > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ reference slot</div> <p> <abc-select data-cy=\"storybook-test-base12\" v-model=\"patientSex\"> <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> <span slot=\"reference\" >啦啦啦 <abc-tooltip-info placement=\"top-start\" content=\"Top Start 提示文字\"> </abc-tooltip-info> </span> </abc-select> </p> <div class=\"mdx-title\">✨ prepend + 换 icon</div> <div> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\">normal </abc-radio> <abc-radio label=\"medium\">medium </abc-radio> <abc-radio label=\"large\">large </abc-radio> </abc-radio-group> </abc-space> </div> <br /> <p> <abc-select data-cy=\"storybook-test-base12\" v-model=\"patientSex\" :size=\"size\" trigger-icon=\"s-ai-fill\" > <abc-icon icon=\"s-b-scan-line\" slot=\"prepend\"></abc-icon> <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> </div> </template> <script> export default { data() { return { patientSex: '', patientSex1: '', patientSex2: '', patientSex4: '不男不女', size: '', } }, } </script>", "props": [{"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "showValue", "description": "自定义展示的文字，有该字段，始终展示该字段"}, {"name": "value"}, {"name": "label"}, {"name": "async"}, {"name": "customClass"}, {"name": "type", "default": "'text'"}, {"name": "validateEvent", "default": "true"}, {"name": "width", "default": "360"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "下拉选项最大宽度"}, {"name": "maxHeight", "description": "下拉选项最大高度", "default": "308"}, {"name": "innerWidth"}, {"name": "inputStyle"}, {"name": "index"}, {"name": "noIcon", "description": "不展示下拉箭头", "default": "false"}, {"name": "placeholder"}, {"name": "disabled", "description": "是否禁用"}, {"name": "tabindex"}, {"name": "clearable", "description": "是否可删除", "default": "false"}, {"name": "withSearch", "description": "是否带搜索", "default": "false"}, {"name": "fetchSuggestions", "description": "获取建议项，在该方法内改变 options"}, {"name": "placement", "default": "'bottom-start'"}, {"name": "wordBreak", "description": "item:  break-word + ellipsis + break-all", "default": "false"}, {"name": "focusShowOptions", "description": "聚焦自动展开options", "default": "false"}, {"name": "supportInverse", "description": "是否支持反选", "default": "false"}, {"name": "defaultValue", "description": "支持反选的情况下，反选后恢复默认值", "default": "''"}, {"name": "multiple", "description": "是否支持多选", "default": "() => false"}, {"name": "referenceMode", "description": "select reference展示方式: default - 输入框展示； icon - 图标展示； text - 文字展示\n文字模式下： referenceText > value > placeholder", "default": "() => 'default'"}, {"name": "referenceIcon", "description": "默认的展示图标；配合referenceMode 使用", "default": "() => 'filtrate'"}, {"name": "referenceIconOptions", "description": "默认的展示图标的属性", "default": "() => {}"}, {"name": "referenceText", "description": "默认的展示文字；配合referenceMode 使用", "default": "''"}, {"name": "referenceTextStyle", "description": "默认的展示文字的自定义style", "default": "() => {}"}, {"name": "referenceTextJustify", "description": "文字模式的对齐方式", "default": "'start'"}, {"name": "referenceActiveColor", "description": "激活态颜色，默认为 $S1，仅对文字模式和图标模式生效", "default": "AbcTheme.S1"}, {"name": "multiLabelMode", "description": "多选的label展示方式： tag；text", "default": "() => 'tag'"}, {"name": "maxTag", "description": "multiLabelMode 为 tag 时，最后一个tag展示为+n; 如果为 text，展示xx/xx/xx 等几项\nps: 历史原因：当为text时都展示为xx等几项，最初只考虑了tag, 所以该prop取名有歧义", "default": "() => 1000"}, {"name": "changeRequireConfirm", "description": "多选模式下，change事件需要筛选和清除来操作", "default": "() => false"}, {"name": "multipleLimit", "description": "多选模式下，最多可以选中多少个", "default": "() => -1"}, {"name": "disableOperateOptionIds", "description": "多选模式下，给定默认值，默认值可以包含不准删除的项目，（第一：该项的tag不能有删除icon，第二，该项要禁止掉）", "default": "[]"}, {"name": "separation", "description": "多选模式下分割", "default": "() => '/'"}, {"name": "onlyBottomBorder", "description": "select只有底部边框", "default": "false"}, {"name": "theme", "description": "颜色模式，目前只支持dark", "default": "''"}, {"name": "allowCreate", "description": "通过输入框创建新项目", "default": "false"}, {"name": "createMaxLength", "description": "输入框创建最大允许输入值的长度", "default": "100"}, {"name": "setting", "description": "是否拥有setting footer", "default": "false"}, {"name": "settingText", "description": "setting footer 文字", "default": "''"}, {"name": "showEmpty", "description": "组件内部根据options的数量展示空状态与否？", "default": "true"}, {"name": "emptyText", "description": "没有options时，展示的文字", "default": "'暂无数据'"}, {"name": "tagMaxWidth", "description": "多选tag限制宽度", "default": "0"}, {"name": "defaultValueMap", "description": "用于value在options里面找不到的情况", "default": "{}"}, {"name": "beforeChange", "description": "select 是否选中根据业务代码来决定 （注意： 只支持单选）", "default": "null"}, {"name": "settingIcon", "description": "setting icon, default: n-settings-line", "default": "'n-settings-line'"}, {"name": "disabledInputEnter", "description": "input 回车不做事情", "default": "() => false"}, {"name": "textModePlus", "description": "text mode plus: 有hover; 可删除", "default": "() => false"}, {"name": "correctMultipleClearValue", "description": "是否修正多选情况下clear的返回值\nps: 历史原因：clear的返回值都统一处理成了空字符串", "default": "() => false"}, {"name": "checkAllProps", "description": "是否开启全选，默认不开启\n只在多选并且不限制multipleLimit时，allow-create为false, withSearch为false 生效\n{\n    visible: false,\n    text: '全选',\n}", "default": "{\n    visible: false,\n    text: '全选'\n}"}, {"name": "triggerIcon", "description": "支持更换trigger的 icon\nps: 历史原因：icon请和设计确认，需要偏左", "default": "'n-select-fill'"}, {"name": "scrollToValue", "description": "面板打开，滚动到对应value的 dom\nps: 有选中内容，选中的 dom的优先级大于该属性", "default": "undefined"}], "slots": ["prepend", "reference", "description", "default", "setting-slot", "bottom-fixed"], "events": ["close", "set", "input", "change", "focus", "open", "enter", "blur", "clear"]}