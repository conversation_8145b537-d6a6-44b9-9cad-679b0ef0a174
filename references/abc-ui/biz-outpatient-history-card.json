{"name": "BizOutpatientHistoryCard", "description": "", "usage": "<template> <biz-outpatient-history-card :detail=\"detail\" hasPermission hasCopy hasCopyMr canViewHistoryPr showTotalPrice :customStyle=\"{ width: '370px', background: 'var(--abc-color-cp-grey2)' }\" ></biz-outpatient-history-card> </template> <script> export default { data() { return { detail: { totalPrice: 186.0, sourceTotalPrice: 186.0, displayTotalPrice: 186, promotionPrice: 0.0, adjustmentPrice: 0.0, unitAdjustmentFee: 0, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: 186.0, needPayFee: 186.0, owedFee: 0, receivedPrice: 0.0, receivedIgnoreOwePrice: 0.0, printTotalPrice: 186.0, id: 'ffffffff0000000034fd2a9fc4afc000', patientOrderId: 'ffffffff0000000034fd2aac223a4000', hospitalPatientOrderId: null, chainId: '6a869c22abee4ffbaef3e527bbb70aeb', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', departmentId: '59140f8ecdeb4553ab570f710274e0ab', departmentName: '小儿外科诊室', clinicName: '高新大原店', doctorId: '6e45706922a74966ab51e4ed1e604641', doctorName: '丁柱112', nationalDoctorCode: null, doctorSignImgUrl: 'https://cis-images-dev.oss-cn-shanghai.aliyuncs.com/prescription-sign/hSD6cP7pcvCuM6xBfsK6Pj87ZfeAyBLE_1737015062815', organLogo: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/basic/pexels-nui-malama-169330637-30129937_WceMDShbJ8H4.jpg', copywriterName: null, patientOrderNo: 31038, created: '2025-05-16T02:35:14Z', diagnosedDate: '2025-05-16T02:35:14Z', orderByDate: '2025-05-16T02:35:00Z', reserveDate: '2025-05-16T02:35:00Z', adjustmentFee: 0, isPriceChanged: 0, chargeStatus: 0, totalFee: null, status: 1, statusName: '已诊', subStatus: 0, executeStatus: 0, source: 2, outpatientSource: 0, dataSignature: null, registrationInfo: { totalPrice: null, sourceTotalPrice: 0, displayTotalPrice: 0, promotionPrice: null, adjustmentPrice: null, unitAdjustmentFee: null, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: null, needPayFee: null, owedFee: null, receivedPrice: null, receivedIgnoreOwePrice: 0, printTotalPrice: 0, chargeStatus: 0, registrationSheetId: 'ffffffff0000000034fd2aac22ce8000', reserveDate: '2025-05-16', reserveStart: '10:35', reserveEnd: '12:00', visitRemark: null, visitSourceId: null, visitSourceFrom: null, visitSourceFromName: null, departmentId: '59140f8ecdeb4553ab570f710274e0ab', departmentName: '小儿外科诊室', registrationProducts: [], }, registrationFee: 6.0, registrationCategory: 0, registrationFeeStatus: 0, isReserved: 0, referralFlag: 0, referralSource: null, isOnline: 0, medicalRecord: { id: 'ffffffff0000000034fd2a9fc4afc001', patientOrderId: 'ffffffff0000000034fd2aac223a4000', outpatientSheetId: 'ffffffff0000000034fd2a9fc4afc000', patientId: '958e99beb2fc4340b1cf4fd4418d530e', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', chainId: '6a869c22abee4ffbaef3e527bbb70aeb', departmentId: '', doctorId: '', type: 2, chiefComplaint: '前牙疼痛', pastHistory: '既往体健，否认药物过敏史，否认备孕 q，既往有高血脂', allergicHistory: ' 否认药物过敏史', familyHistory: '否认家族遗传病史', personalHistory: '否认疫区旅居史', presentHistory: '现病史', physicalExamination: '生命体征平稳，未闻及异常呼吸音及干湿啰音，浅表淋巴结未扪及肿大，未闻及心包摩擦音，各瓣膜区未闻及明显杂音，心音有力心律齐，无腹肌紧张，各瓣膜区未闻及明显杂音，各瓣膜区未闻及明显杂音，双肾区无叩痛，肠鸣音活跃，全腹部无压痛，全腹部无压痛，心前区无异常搏动，体温 38.6 ℃，体温 40.6 ℃，体温 40.5 ℃，体温 37.5 ℃', doctorAdvice: null, syndrome: null, syndromeTreatment: null, therapy: null, chineseExamination: null, birthHistory: null, oralExamination: null, epidemiologicalHistory: '{\"patientChecked\":true,\"attendantChecked\":false,\"intervalDays\":14,\"symptomList\":[{\"label\":\"\",\"isSuspicious\":true},{\"label\":\"14天内是否有发热、干咳、乏力、鼻塞、流涕、咽痛、肌痛、腹泻、结膜炎、嗅觉和味觉减退等症状（有任一症状即选择“是”）\",\"value\":\"是\",\"selectedOptions\":[\"发热\"]},{\"label\":\"14天内是否有高/中风险地区，境外或其他有病例报告社区的旅行史或居住史\",\"value\":\"是\",\"selectedOptions\":[\"国内病例社区\"]},{\"label\":\"14天内是否接触过来自高/中风险地区，或来自有病例报告社区的疑似症状患者\",\"value\":\"是\"},{\"label\":\"14天内是否有与新冠病毒感染者（核酸检测阳性）的接触史\",\"value\":\"是\"}],\"suspiciousList\":[]}', obstetricalHistory: '[{\"type\":\"pregnant\",\"birthCount\":1,\"pregnantCount\":1},{\"type\":\"menstruation\",\"menophaniaAge\":13,\"menstruationDays\":[3,5],\"menstrualCycle\":[28,30],\"menopauseTab\":1,\"menopauseDate\":\"2025-05-15\",\"menopauseAge\":\"\"},\"经色正常\",\"色淡红\",\"白带异味\",\"白带色黄\"]', auxiliaryExamination: null, auxiliaryExaminations: [ { toothNos: [12, 44, 24, 33], value: '辅助检查', }, ], chinesePrescription: null, diagnosis: '急性龈炎', diagnosisInfos: [ { code: 'K05.000', name: '急性龈炎', diseaseType: null, hint: '医保ICD10', }, ], extendDiagnosisInfos: [ { toothNos: [12, 44, 23, 33], value: [ { code: 'K05.000', name: '急性龈炎', diseaseType: null, hint: '医保ICD10', }, ], }, ], attachments: [], dentistryExaminations: [ { toothNos: [11, 44, 21, 32], value: '近中面窝沟龋', }, ], treatmentPlans: [ { toothNos: [13, 44, 23, 32], value: '干髓术，建议洁牙', }, ], disposals: [ { toothNos: [12, 43, 22, 34], value: '宣教：引导患者知晓正确的刷牙方法，合理选用牙膏，合理使用牙线等。', }, ], examItems: [ { examinationSheetId: 'ffffffff0000000034fd2abe456bc000', examinationName: '嗜酸性粒细胞', examinationApplySheetId: '3818255055776890881', patientOrderId: 'ffffffff0000000034fd2abe223a4000', chargeSheetId: 'ffffffff0000000034fd2abe297d8004', outpatientFormItemId: 'ffffffff0000000034fd2abe24afc001', chargeFormItemId: 'ffffffff0000000034fd2abe297d8006', examinationGoodsId: 'ffffffff0000000034cd8bcd91e3804a', adviceExecuteItemId: null, mergeSheetId: '3818255055776890882', created: '2025-05-16T02:37:39Z', goodsType: 3, type: 1, subType: 0, deviceType: 1, businessType: 10, status: 0, sampleStatus: 0, }, ], wearGlassesHistory: null, eyeExamination: null, target: null, prognosis: null, symptomTime: '2025-05-16T02:33:35Z', }, chronicArchivesInfo: null, type: 0, sceneType: 0, isDraft: 0, outpatientFrom: 0, barcode: '00031038-02', airPharmacyOrderId: null, canAdjustmentFee: null, canAdjustment: 0, prescriptionChineseForms: [], prescriptionWesternForms: [ { totalPrice: 150.0, sourceTotalPrice: 150.0, displayTotalPrice: 150.0, promotionPrice: 0.0, adjustmentPrice: 0.0, unitAdjustmentFee: 0, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: 150.0, needPayFee: 150.0, owedFee: 0, receivedPrice: 0.0, receivedIgnoreOwePrice: 0.0, printTotalPrice: 150.0, id: 'ffffffff0000000034fd2aac44afc002', keyId: null, qrid: 'PXLujUMVc', patientOrderId: 'ffffffff0000000034fd2aac223a4000', outpatientSheetId: 'ffffffff0000000034fd2a9fc4afc000', patientId: '958e99beb2fc4340b1cf4fd4418d530e', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', departmentId: '59140f8ecdeb4553ab570f710274e0ab', doctorId: '6e45706922a74966ab51e4ed1e604641', type: 1, specification: '', doseCount: 0, refundDoseCount: null, dailyDosage: '', usage: '', freq: '', requirement: null, usageLevel: '', sort: 0, source: 0, originDoseCount: 0, optometristId: null, optometristName: null, glassesType: null, glassesParams: null, processUsage: null, processBagUnitCount: 0, processBagUnit: null, totalProcessCount: null, processRemark: null, processPrice: null, contactMobile: null, processUsageInfo: null, isDecoction: false, usageType: null, usageSubType: null, chargeStatus: 0, chargeStatusName: '待收费', prescriptionFormItems: [ { totalPrice: 150.0, sourceTotalPrice: 150.0, displayTotalPrice: 0, promotionPrice: 0.0, adjustmentPrice: 0.0, unitAdjustmentFee: null, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: 150.0, needPayFee: 150.0, owedFee: 0, receivedPrice: 0.0, receivedIgnoreOwePrice: 0.0, printTotalPrice: 150.0, id: 'ffffffff0000000034fd2aac44afc003', keyId: '7f66a1eda2194852b44e693ed941b294', patientOrderId: 'ffffffff0000000034fd2aac223a4000', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', outpatientSheetId: 'ffffffff0000000034fd2a9fc4afc000', prescriptionFormId: 'ffffffff0000000034fd2aac44afc002', goodsId: 'ffffffff0000000034db67e1f4098000', domainMedicineId: '', type: 1, subType: 1, composeType: 0, composeParentFormItemId: null, medicineCadn: '测试厂家西药', name: '测试厂家西药(田哥)', specification: '', manufacturer: '', ast: 0, chargeType: 0, usage: '口服', ivgtt: 0.0, ivgttUnit: '', freq: 'qd', dosage: '1', dosageUnit: '支', days: 3, specialRequirement: null, useDismounting: 1, sort: 0, groupId: null, stockPieceCount: 1.0, stockPackageCount: 9998.0, astResult: null, productInfo: { goodsVersion: 0, sourceFlag: 1, id: 'ffffffff0000000034db67e1f4098000', goodsId: 'ffffffff0000000034db67e1f4098000', status: 1, name: '田哥', displayName: '测试厂家西药(田哥)', displaySpec: '25g*2支/盒', organId: '6a869c22abee4ffbaef3e527bbb70aeb', typeId: 12, type: 1, subType: 1, barCode: '6925895400992', pieceNum: 2, pieceUnit: '支', packageUnit: '盒', dismounting: 1, medicineCadn: '测试厂家西药', medicineNmpn: '豫G妆网备字**********', medicineDosageNum: 25, medicineDosageUnit: 'g', specType: 0, position: null, chainPackagePrice: 1e2, chainPiecePrice: 5e1, piecePrice: 5e1, packagePrice: 1e2, packageCostPrice: 99, minPackagePrice: 1e2, maxPackagePrice: 1e2, minPackageCostPrice: 99, maxPackageCostPrice: 99, fixedPackagePrice: 1e2, fixedPiecePrice: 5e1, totalSalePrice: 1.5e2, priceType: 1, subClinicPriceFlag: 1, inTaxRat: 9, outTaxRat: 12, pieceCount: 0, packageCount: 9999, dispGoodsCount: '9999盒', stockPieceCount: 1, stockPackageCount: 9998, dispStockGoodsCount: '9998盒1支', availablePackageCount: 9999, availablePieceCount: 0, outPieceCount: 1, outPackageCount: 9998, dispOutGoodsCount: '9998盒1支', prohibitPieceCount: 0, prohibitPackageCount: 0, dispProhibitGoodsCount: '0盒', lockingPieceCount: 1, lockingPackageCount: 0, dispLockingGoodsCount: '1支', lastPackageCostPrice: 99, needExecutive: 0, hospitalNeedExecutive: 0, shortId: '300000905086614', composeUseDismounting: 0, composeSort: 0, disableComposePrint: 0, createdUserId: '6e45706922a74966ab51e4ed1e604641', lastModifiedUserId: '6e45706922a74966ab51e4ed1e604641', lastModifiedDate: '2024-10-23T05:48:00Z', combineType: 0, bizRelevantId: null, extendSpec: '', medicalFeeGrade: 0, disable: 0, chainDisable: 0, v2DisableStatus: 0, chainV2DisableStatus: 0, disableSell: 0, isSell: 1, customTypeId: 0, chainPackageCostPrice: 99, chainId: '6a869c22abee4ffbaef3e527bbb70aeb', shebao: { goodsId: 'ffffffff0000000034db67e1f4098000', goodsType: 1, isDummy: 0, medicineNum: '300000905086614', medicalFeeGrade: 0, }, profitRat: 1, lastStockInId: *********, lastStockInOrderSupplier: '国药集团西南医药有限公司', pharmacyType: 0, pharmacyNo: 0, pharmacyName: '住院药房1', pharmacyGoodsStockList: [ { pharmacyName: '住院药房1', pharmacyNo: 0, lastPackageCostPrice: 99, stockPieceCount: 1, stockPackageCount: 9998, availablePackageCount: 9999, availablePieceCount: 0, esInorder: 1, }, ], defaultInOutTax: 1, dispenseAveragePackageCostPrice: 99, innerFlag: 0, deviceInnerFlag: 1, feeComposeType: 0, feeTypeId: '12', usePieceUnitFlag: 1, copiedFlag: 0, coopFlag: 0, cloudSupplierFlag: 0, expiredWarnMonths: 30, otcType: 1, otcTypeName: '处方药', dosageFormType: 401, dosageFormTypeName: '乳膏剂', dangerIngredient: 0, keyId: 'ffffffff0000000034fd2aac44afc003', goodsBatchInfoList: [ { batchId: *********, pieceNum: 2, pharmacyType: 0, pharmacyNo: 0, inDate: '2024-10-23T05:49:53Z', supplierId: '3f73cd1bc2684bdeaf1fe66793baa0b4', supplierName: '国药集团西南医药有限公司', batchNo: 'jhgfds123456', packageCostPrice: 99, packagePrice: 1e2, piecePrice: 5e1, pieceCount: 0, packageCount: 9999, dispGoodsCount: '9999盒', stockPieceCount: 1, stockPackageCount: 9998, dispStockGoodsCount: '9998盒1支', lockingPieceCount: 1, lockingPackageCount: 0, dispLockingGoodsCount: '1支', cutTotalPieceCount: 3, cutPieceCount: 3, totalSalePrice: 1.5e2, status: 0, }, ], lastMonthSellCount: 0, isPreciousDevice: 0, shebaoPayLimitPriceRule: { limitPriceType: 0, typeRule: { priceType: 1, exceedLimitPriceRule: 0, limitDetail: [ { minPriceLimit: 0, limitRate: 10, }, ], }, }, lockBatchOps: 2, cMSpec: '', }, acupoints: null, externalGoodsItems: null, payType: null, billingType: null, verifySignatures: null, unitCount: 3.0, unit: '支', unitPrice: 50.0, currentUnitPrice: null, sourceUnitPrice: 50.0, totalPriceRatio: 1, fractionPrice: 0, isUnitPriceChanged: 0, isTotalPriceChanged: 0, formFlatPrice: null, sheetFlatPrice: null, executeStatus: 1, pharmacyType: 0, pharmacyNo: 0, pharmacyName: null, externalUnitCount: null, chargeStatus: 0, refundUnitCount: null, canAdjustment: 0, }, ], created: '2025-05-16T02:35:14Z', lastModified: '2025-05-16T02:35:14Z', pharmacyType: 0, pharmacyNo: 0, pharmacyName: '住院药房1', vendorId: null, vendorName: null, usageScopeId: null, medicineStateScopeId: null, ingredientPrice: null, usageDays: null, medicinePriceInfo: null, processInfo: null, deliveryInfo: null, ingredientInfo: null, expectedTotalPrice: null, adjustmentFee: null, isTotalPriceChanged: 0, psychotropicNarcoticType: 0, sheetFlatPrice: null, finishedRate: null, finishedRateMin: null, vendorUsageScopeId: null, verifySignatureStatus: null, canAdjustmentFee: null, canAdjustment: 0, }, ], prescriptionInfusionForms: [], prescriptionExternalForms: [], prescriptionGlassesForms: [], productForms: [ { totalPrice: 30.0, sourceTotalPrice: 30.0, displayTotalPrice: 30.0, promotionPrice: 0.0, adjustmentPrice: 0.0, unitAdjustmentFee: 0, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: 30.0, needPayFee: 30.0, owedFee: 0, receivedPrice: 0.0, receivedIgnoreOwePrice: 0.0, printTotalPrice: 30.0, id: 'ffffffff0000000034fd2aac44afc000', keyId: null, patientOrderId: 'ffffffff0000000034fd2aac223a4000', outpatientSheetId: 'ffffffff0000000034fd2a9fc4afc000', patientId: '958e99beb2fc4340b1cf4fd4418d530e', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', departmentId: '59140f8ecdeb4553ab570f710274e0ab', doctorId: '6e45706922a74966ab51e4ed1e604641', sort: 0, source: 0, chargeStatus: 0, chargeStatusName: '待收费', sourceFormType: 3, productFormItems: [ { totalPrice: 30.0, sourceTotalPrice: 30.0, displayTotalPrice: 30.0, promotionPrice: 0.0, adjustmentPrice: 0.0, unitAdjustmentFee: null, refundTotalPrice: 0, refundDiscountedPrice: 0, refundedFee: 0, unselectedFee: 0, receivableFee: 30.0, needPayFee: 30.0, owedFee: 0, receivedPrice: 0.0, receivedIgnoreOwePrice: 0.0, printTotalPrice: 30.0, id: 'ffffffff0000000034fd2aac44afc001', keyId: null, patientOrderId: 'ffffffff0000000034fd2aac223a4000', clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d', outpatientSheetId: 'ffffffff0000000034fd2a9fc4afc000', productFormId: 'ffffffff0000000034fd2aac44afc000', productId: '0dd6658b57d39435a66d1590c7ea9a34', name: '理疗1', unitCount: 1.0, unit: '次', costUnitPrice: 20.0, useDismounting: 0, sort: 0, type: 4, subType: 2, composeType: 0, goodsFeeType: 0, composeParentFormItemId: null, chargeStatus: 0, executedUnitCount: 0.0, executeStatus: 1, executeStatusName: null, examinationResult: null, productInfo: { goodsVersion: 0, sourceFlag: 1, id: '0dd6658b57d39435a66d1590c7ea9a34', goodsId: '0dd6658b57d39435a66d1590c7ea9a34', status: 1, name: '理疗1', displayName: '理疗1', displaySpec: '次', organId: '6a869c22abee4ffbaef3e527bbb70aeb', typeId: 23, type: 4, subType: 2, pieceNum: 1, pieceUnit: null, packageUnit: '次', dismounting: 0, medicineCadn: null, position: null, chainPackagePrice: 3e1, piecePrice: null, packagePrice: 3e1, packageCostPrice: 2e1, fixedPackagePrice: 3e1, priceType: 1, subClinicPriceFlag: 1, inTaxRat: 0, outTaxRat: 0, needExecutive: 1, hospitalNeedExecutive: 0, shortId: '*********', composeUseDismounting: 0, composeSort: 0, disableComposePrint: 0, createdUserId: '6e45706922a74966ab51e4ed1e604641', lastModifiedUserId: '6e45706922a74966ab51e4ed1e604641', lastModifiedDate: '2023-12-28T10:42:23Z', combineType: 0, bizRelevantId: null, extendSpec: null, inspectionSite: 0, medicalFeeGrade: 0, disable: 0, chainDisable: 0, v2DisableStatus: 0, chainV2DisableStatus: 0, disableSell: 0, isSell: 1, customTypeId: 0, chainPackageCostPrice: 2e1, chainId: '6a869c22abee4ffbaef3e527bbb70aeb', shebao: { goodsId: '0dd6658b57d39435a66d1590c7ea9a34', goodsType: 4, payMode: 0, isDummy: 0, medicineNum: '*********', medicalFeeGrade: 0, }, pharmacyType: 0, pharmacyNo: 0, defaultInOutTax: 0, dispenseAveragePackageCostPrice: 2e1, shebaoPayMode: 0, innerFlag: 0, deviceInnerFlag: 1, feeComposeType: 0, feeTypeId: '23', usePieceUnitFlag: 0, copiedFlag: 0, coopFlag: 0, cloudSupplierFlag: 0, expiredWarnMonths: 30, dangerIngredient: 0, keyId: 'ffffffff0000000034fd2aac44afc001', isPreciousDevice: 0, cMSpec: null, }, days: 1, dailyDosage: null, freq: '1日1次', stockPieceCount: null, stockPackageCount: null, needExecutive: 1, remark: null, payType: null, toothNos: [], unitPrice: 30.0, currentUnitPrice: null, totalPriceRatio: 1, sourceUnitPrice: 30.0, fractionPrice: 0, isUnitPriceChanged: 0, isTotalPriceChanged: 0, formFlatPrice: null, sheetFlatPrice: null, pharmacyType: 0, pharmacyNo: 0, doctorId: null, doctorName: null, departmentId: null, departmentName: null, nurseId: null, nurseName: null, refundUnitCount: null, purposeOfExamination: null, feeComposeType: 0, feeTypeId: 23, canAdjustment: 0, }, ], created: '2025-05-16T02:35:14Z', lastModified: '2025-05-16T02:35:14Z', expectedTotalPrice: null, sheetFlatPrice: null, isTotalPriceChanged: 0, canAdjustmentFee: null, canAdjustment: 0, }, ], consultationSheet: null, questionSheets: [], shebaoCardInfo: null, revisitStatus: 2, shebaoChargeType: 2, chargeForms: [], currentTime: '2025-05-16T02:46:15.368Z', firstChargedTime: null, outpatientFlag: 0, chargeRoundingTips: '', diagnoseCount: 83, isPhotoPrescriptionCopied: 0, }, } }, } </script>", "props": [{"name": "loading"}, {"name": "customStyle"}, {"name": "detail"}, {"name": "hasPermission"}, {"name": "showOperationBtnGroup", "default": "true"}, {"name": "hasCopy"}, {"name": "hasCopyMr"}, {"name": "canViewHistoryPr"}, {"name": "canViewHistoryGlassesPr"}, {"name": "clientWidth", "default": "1920"}, {"name": "showEditBtn"}, {"name": "printOptions", "default": "[]"}, {"name": "showPrintMr"}, {"name": "canCopyForm"}, {"name": "doctorAdviceInForm"}, {"name": "isSupportSurgery"}, {"name": "showTotalPrice"}, {"name": "formatDiagnosisTreatmentUnit"}], "events": ["copy", "print", "edit", "copySingleForm", "viewReport", "previewIt", "openSurgeryDetailDialog"]}