{"name": "BizGoodsSelectDialog", "description": "", "usage": "<template> <abc-flex vertical :gap=\"16\"> <abc-space> <abc-button variant=\"ghost\" @click=\"openDialog(CATEGORY_TYPE_ENUM.INSPECTION)\" >检查</abc-button > <abc-button variant=\"ghost\" @click=\"openDialog(CATEGORY_TYPE_ENUM.ASSAY)\" >检验</abc-button > <abc-button variant=\"ghost\" @click=\"openDialog(CATEGORY_TYPE_ENUM.TREATMENT)\" >治疗理疗</abc-button > <abc-button variant=\"ghost\" @click=\"openDialog(CATEGORY_TYPE_ENUM.MEDICINE_WESTERN)\" >西药</abc-button > </abc-space> <abc-flex vertical> <abc-text v-for=\"item in result\" :key=\"item.id\" theme=\"gray\"> { name: {{ item.name }}, id: {{ item.id }} } </abc-text> </abc-flex> </abc-flex> </template> <script> export default { data() { return { CATEGORY_TYPE_ENUM, categoryList: [ { name: '检查项目', value: CATEGORY_TYPE_ENUM.INSPECTION, subCategoryList: [ { name: '常用项目', value: -99, }, { name: 'CT', value: 1, }, { name: 'DR', value: 2, }, { name: '彩超', value: 8, }, ], }, { name: '检验项目', value: CATEGORY_TYPE_ENUM.ASSAY, subCategoryList: [ { name: '常用项目', value: -99, }, { name: '临床检验', value: 1, }, { name: '生化检验', value: 2, }, { name: '微生物检验', value: 4, }, ], }, { name: '治疗项目', value: CATEGORY_TYPE_ENUM.TREATMENT, subCategoryList: [ { name: '常用项目', value: -99, }, { name: '康复', value: 1, }, { name: '推拿', value: 2, }, { name: '按摩', value: 4, }, ], }, { name: '西药', value: CATEGORY_TYPE_ENUM.MEDICINE_WESTERN, subCategoryList: [ { name: '常用项目', value: -99, }, { name: '注射剂', value: 1, }, { name: '丸剂', value: 2, }, { name: '溶液剂', value: 4, }, ], }, ], result: [], } }, methods: { openDialog(value) { new BizGoodsSelectDialog({ categoryList: this.categoryList, defaultCategoryValue: value, fetchFn: this.fetchData, onSubmit: (data) => { this.result = data }, }).generateDialogAsync() }, getRandomList(list) { const count = Math.floor(Math.random() * (list.length + 1)) if (count === 0) { return [] } const result = [] for (let i = 0; i < count; i++) { const randomIndex = Math.floor(Math.random() * list.length) result.push(list[randomIndex]) list.splice(randomIndex, 1) } return result }, async fetchData({ keyword }) { return new Promise((resolve) => { console.log('keyword', keyword) // eslint-disable-next-line abc/no-timer-id setTimeout(() => { const data = { list: this.getRandomList(clone(allList)), offset: 0, limit: 10, total: 10, } resolve(data) }, 300) }) }, }, } </script>"}