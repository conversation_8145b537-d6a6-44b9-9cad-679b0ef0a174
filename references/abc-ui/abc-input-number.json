{"name": "AbcInputNumber", "description": "数字输入框组件，用于输入数字内容，支持点击加减实现值的变化", "usage": "<template> <div style=\"display: flex; justify-content: space-around\"> <abc-input-number v-model=\"count1\" :width=\"136\"> </abc-input-number> <abc-input-number data-cy=\"storybook-test-input-number\" fixed-button v-model=\"count2\" button-placement=\"top\" :width=\"40\" > </abc-input-number> <abc-input-number fixed-button v-model=\"count3\" button-placement=\"left\" :width=\"40\" > </abc-input-number> </div> </template> <script> export default { data() { return { count1: 0, count2: 0, count3: 0, } }, } </script>", "props": [{"name": "size", "description": "表单尺寸大小", "values": ["tiny/small/medium/large/huge"], "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "value"}, {"name": "config", "default": "{\n    max: null, // 最多输入\n    min: null, // 最小输入\n    supportZero: false, // 是否支持零\n    supportNegative: false, // 是否支持负数\n    formatLength: 0, // 支持小数点后几位\n}"}, {"name": "width"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "input 原生属性，最大输入长度"}, {"name": "margin"}, {"name": "placeholder"}, {"name": "disabled"}, {"name": "readonly"}, {"name": "fixedButton", "description": "加减按钮常驻"}, {"name": "inputCustomStyle", "description": "自定义 input 样式"}, {"name": "tabindex", "description": "原生属性"}, {"name": "step", "description": "加减步长", "default": "1"}, {"name": "autoStep", "description": "自动步长，会根据当前值决定步长，比如 1.11，此时步长就是 0.01", "default": "false"}, {"name": "disabledAddBtn", "description": "禁用增加按钮", "default": "false"}, {"name": "buttonPlacement", "description": "按钮位置：top/left", "default": "'top'"}, {"name": "allowInput", "description": "是否允许输入", "default": "true"}], "slots": ["icon-minus", "append", "icon-plus"], "events": ["input", "blur", "focus", "change", "enter", "tab", "click", "keyup"]}