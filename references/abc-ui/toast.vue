<template>
  <div class="toast-wrapper">
    <button @click="toast">toast</button>
    <button @click="toastHandle">直接调用toast函数, 并绑定referenceEl</button>
  </div>
</template>
<script>
export default {
  methods: {
    toast() {
      this.$Toast({
        message: '成功信息',
        type: 'success',
      })
    },
    toastHandle() {
      Toast({
        message: '成功信息',
        type: 'success',
        referenceEl: document.querySelectorAll('.toast-wrapper')[0],
      })
    },
  },
}
</script>
