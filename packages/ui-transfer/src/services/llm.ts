export interface LLMRequest {
  nodeType: string;
  nodeName: string;
  nodeProperties: Record<string, any>;
  childrenInfo?: {
    count: number;
    types: string[];
  };
}

export interface LLMResponse {
  suggestedComponent: string;
  suggestedProps: Record<string, any>;
  layoutInfo: Record<string, any>;
  confidence: number;
  reasoning?: string;
  transformation?: string;
  type?: string;
}

export class LLMService {
  private apiKey: string;
  private endpoint: string;
  private model: string;

  constructor(apiKey: string = '') {
    this.apiKey = apiKey;
    this.endpoint = 'https://api.openai.com/v1';
    this.model = 'gpt-3.5-turbo';
  }

  // 准备 LLM 请求数据
  private prepareLLMRequest(node: SceneNode): LLMRequest {
    const request: LLMRequest = {
      nodeType: node.type,
      nodeName: node.name,
      nodeProperties: {}
    };

    // 添加基本属性
    if ('opacity' in node) {
      request.nodeProperties.opacity = node.opacity;
    }

    if ('visible' in node) {
      request.nodeProperties.visible = node.visible;
    }

    // 添加文本属性
    if (node.type === 'TEXT') {
      const textNode = node as TextNode;
      request.nodeProperties.text = textNode.characters;
      request.nodeProperties.fontSize = textNode.fontSize;
      request.nodeProperties.fontWeight = textNode.fontWeight;
    }

    // 添加布局属性
    if ('layoutMode' in node) {
      const frameNode = node as FrameNode;
      request.nodeProperties.layoutMode = frameNode.layoutMode;
      request.nodeProperties.primaryAxisSizingMode = frameNode.primaryAxisSizingMode;
      request.nodeProperties.counterAxisSizingMode = frameNode.counterAxisSizingMode;
    }

    // 添加子节点信息
    if ('children' in node) {
      const children = (node as FrameNode).children;
      request.childrenInfo = {
        count: children.length,
        types: children.map(child => child.type)
      };
    }

    return request;
  }

  // 调用 LLM 服务
  async analyze(node: SceneNode): Promise<LLMResponse> {
    const request = this.prepareLLMRequest(node);

    console.log('LLM Request:', request);
    
    // TODO: 实现实际的 LLM API 调用
    // 这里先返回模拟数据
    return {
      suggestedComponent: 'AbcFlex',
      suggestedProps: {
        direction: 'column',
        gap: 8
      },
      layoutInfo: {
        direction: 'column',
        gap: 8
      },
      confidence: 0.9,
      reasoning: '基于节点的布局属性和子节点结构，建议使用 AbcFlex 组件'
    };
  }
}
