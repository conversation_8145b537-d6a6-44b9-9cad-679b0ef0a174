// 扩展 Figma 节点类型
export type FigmaNode = BaseNode & {
  children?: BaseNode[];
  strokes?: Paint[];
  fills?: Paint[];
  opacity?: number;
  visible?: boolean;
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  constraints?: {
    horizontal: "MIN" | "MAX" | "CENTER" | "STRETCH";
    vertical: "MIN" | "MAX" | "CENTER" | "STRETCH";
  };
  layoutMode?: "HORIZONTAL" | "VERTICAL" | "NONE";
  primaryAxisSizingMode?: "FIXED" | "AUTO";
  counterAxisSizingMode?: "FIXED" | "AUTO";
  primaryAxisAlignItems?: "MIN" | "MAX" | "CENTER" | "SPACE_BETWEEN";
  counterAxisAlignItems?: "MIN" | "MAX" | "CENTER";
  paddingLeft?: number;
  paddingRight?: number;
  paddingTop?: number;
  paddingBottom?: number;
  itemSpacing?: number;
  strokeWeight?: number;
  cornerRadius?: number;
  componentProperties?: {
    [key: string]: {
      type: string;
      value: any;
    };
  };
};
