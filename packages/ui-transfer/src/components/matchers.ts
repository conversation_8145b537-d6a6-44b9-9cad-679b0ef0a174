// 组件子元素匹配器类型
export type ChildMatcher = (node: SceneNode) => boolean;

// 通用的匹配器
export const matchers = {
  isIcon: (node: SceneNode) => node.type === 'INSTANCE' || node.type === 'COMPONENT',
  isText: (node: SceneNode) => node.type === 'TEXT',
  isPrefixIcon: (node: SceneNode) => node.name.toLowerCase().includes('prefix'),
  isSuffixIcon: (node: SceneNode) => node.name.toLowerCase().includes('suffix'),
  isLabel: (node: SceneNode) => node.type === 'TEXT' && node.name.toLowerCase().includes('label'),
  isPlaceholder: (node: SceneNode) => node.type === 'TEXT' && node.name.toLowerCase().includes('placeholder'),
  isHeader: (node: SceneNode) => node.name.toLowerCase().includes('header'),
  isCell: (node: SceneNode) => node.name.toLowerCase().includes('cell'),
  isFooter: (node: SceneNode) => node.name.toLowerCase().includes('footer'),
};