import {LLMService} from '@/services/llm';
import {componentDocs} from '../../../../references/index'
import {convertNode} from './process-utils';
import {GlobalContext, NodeContext} from "@/components/types";

// 智能组件转换器类
export class ComponentConverter {
    private llmService: LLMService;
    private globalContext: GlobalContext;

    constructor() {
        this.llmService = new LLMService();
        // 初始化全局上下文
        this.resetGlobalContext();
    }

    resetGlobalContext() {
        this.globalContext = {
            suggestedComponents: new Set<string>(),
            config: {
                smartConvert: true,
                debug: false
            },
            stats: {
                totalNodes: 0,
                convertedNodes: 0,
                failedNodes: 0,
                startTime: Date.now()
            }
        };
    }


    simplifyNode(node: any): any {
        if (!node?.children || node.children?.length === 0) {
            return node; // 叶子节点直接返回
        }

        // 递归处理子节点
        node.children = node.children.map((child: any) => this.simplifyNode(child));

        // 应用规则：父节点只有一个子节点，并且名字相同 → 删除父节点
        if (node.children.length === 1 && node.componentType === node.children[0].componentType) {
            // 特殊处理 AbcFlex：需要判断 vertical 属性是否一致
            if (node.componentType === 'AbcFlex') {
                const parentVertical = node.componentProperties?.vertical;
                const childVertical = node.children[0].componentProperties?.vertical;
                
                // 只有当 vertical 属性一致时才精简节点
                if (parentVertical === childVertical) {
                    // ⚡️ 返回递归后的子节点（确保新节点也被彻底简化）
                    return node.children[0];
                }
            } else {
                // 非 AbcFlex 组件，直接精简
                // ⚡️ 返回递归后的子节点（确保新节点也被彻底简化）
                return node.children[0];
            }
        }

        return node;
    }



    // 智能转换节点
    async smartConvert(node: SceneNode): Promise<any> {
        this.resetGlobalContext();
        console.log('smartConvert', node);

        // 创建节点上下文
        const nodeContext: NodeContext = {
            node
        };

        // 转换节点
        const tempFigmaInfo = await convertNode(nodeContext, this.globalContext);

        // 简化figma节点数据
        const figmaInfo = this.simplifyNode(tempFigmaInfo);
        console.log('simplifyNode', figmaInfo)


        // 预填充常用组件
        this.globalContext.suggestedComponents.add('AbcIcon');

        // 更新统计信息
        if (this.globalContext.stats) {
            this.globalContext.stats.endTime = Date.now();
        }

        // 整理建议的组件列表
        const suggestedComponentList = [...this.globalContext.suggestedComponents].map(componentName => {
            const component = componentDocs[componentName];

            if (!component) {
                console.warn(`未找到组件文档: ${componentName}`);
                return {
                    componentName,
                    usage: '需要查看文档了解该组件用法'
                }
            } else {
                return {
                    componentName,
                    usage: component.yamlContent,
                }
            }
        });

        // 输出调试信息
        if (this.globalContext.config?.debug) {
            console.log('转换统计:', {
                totalTime: `${(this.globalContext.stats?.endTime || 0) - (this.globalContext.stats?.startTime || 0)}ms`,
                ...this.globalContext.stats
            });
        }

        return {
            figmaInfo,
            suggestedComponentList,
            // stats: this.globalContext.stats
        };

        try {
            // 尝试使用 LLM 进行分析
            const llmResponse = await this.llmService.analyze(node);

            if (llmResponse.confidence > 0.8) {
                // 如果 LLM 的置信度高，使用其建议
                return {
                    type: llmResponse.suggestedComponent,
                    props: llmResponse.suggestedProps,
                    layout: llmResponse.layoutInfo
                };
            }
        } catch (error) {
            console.error('LLM analysis failed:', error);
        }

    }
}
