import { ComponentConfig } from './types';

// 使用 webpack require.context 动态导入所有配置文件
const context = require.context('./configs', false, /\.config\.ts$/);

// 动态导入所有配置
const componentConfigs: Record<string, ComponentConfig> = {};

// 遍历所有配置文件
context.keys().forEach((key: string) => {
  // 排除 .new.config.ts 文件
  if (!key.includes('.new.config.ts')) {
    const configModule = context(key);
    const config = configModule[Object.keys(configModule)[0]];

    // 从配置中获取组件类型并注册
    if (config && config.type) {
      componentConfigs[config.type] = config;
    }
  }
});

// 默认组件映射
export const defaultComponentMapping: Record<string, string> = {
  'TEXT': 'AbcText',
  // 'FRAME': 'AbcFlex',
  // 'GROUP': 'AbcFlex',
  // 'RECTANGLE': 'AbcCard',
  // 'INSTANCE': 'AbcFlex',
  // 'COMPONENT': 'AbcFlex'
};

export { componentConfigs };
