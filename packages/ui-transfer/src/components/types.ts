// 节点上下文组合类型
export interface NodeContext {
  // 原始 Figma 节点
  node: SceneNode | null;
  // 节点的转换上下文
  context?: ConversionContext;
  // 父节点的上下文
  parent?: NodeContext;
}

// 全局上下文接口
export interface GlobalContext {
  // 建议的组件集合
  suggestedComponents: Set<string>;
  // 转换配置
  config?: {
    // 是否启用智能转换
    smartConvert?: boolean;
    // 是否输出调试信息
    debug?: boolean;
  };
  // 转换统计
  stats?: {
    // 转换的节点总数
    totalNodes: number;
    // 成功转换的节点数
    convertedNodes: number;
    // 转换失败的节点数
    failedNodes: number;
    // 转换开始时间
    startTime: number;
    // 转换结束时间
    endTime?: number;
  };
}

// 转换上下文接口
export interface ConversionContext {
  // 基础信息
  componentType?: string;
  name?: string;
  // 组件属性
  componentProperties?: Record<string, any>;
  style?: Record<string, any>;
  // 布局信息
  layout?: LayoutInfo;
  // 子节点
  children?: AbcUIComponent[];
  // 元数据
  metadata?: {
    transformationLog: string[];
  };
}

export interface ComponentSlot {
  name: string;
  content: any;
}

// 组件子元素匹配器类型
export type ChildMatcher = (node: SceneNode) => boolean;

// 组件配置接口
export interface ComponentConfig {
  // 组件类型，用于映射到 ABC UI 组件
  type: string;
  // 是否是容器组件
  isContainer?: boolean;
  // 自定义 children 处理
  customChildrenProcess?: boolean;
  // 节点白名单
  nodeWhitelist?: string[];
  // 自定义子节点
  customChildren?: Record<string, ComponentConfig>;
  // 自定义转换器
  customTransformer?: (nodeContext: NodeContext, globalContext: GlobalContext) => Promise<Partial<ConversionContext>>;
}




export type ResponsiveType = 'fixed' | 'fluid' | 'auto';

export interface LayoutInfo {
  type: 'flex' | 'grid' | 'normal';
  direction?: 'horizontal' | 'vertical';
  gap?: number;
  alignment?: {
    horizontal?: 'start' | 'center' | 'end' | 'space-between';
    vertical?: 'start' | 'center' | 'end' | 'space-between';
  };
  spacing?: {
    padding?: { top?: number; right?: number; bottom?: number; left?: number };
    margin?: { top?: number; right?: number; bottom?: number; left?: number };
  };
  position?: 'relative' | 'absolute';
  gridInfo?: {
    columns?: number;
    rows?: number;
    columnGap?: number;
    rowGap?: number;
  };
  responsive?: {
    widthType: ResponsiveType;
    heightType: ResponsiveType;
    maxWidth?: number;
    minWidth?: number;
    maxHeight?: number;
    minHeight?: number;
  };
}


export interface AbcUIComponent {
  context: ConversionContext;
  // 自定义数据
  customData?: Record<string, any>;
}
