import { ComponentConfig, NodeContext } from '../types';

export const BizGoodsSelectDialog: ComponentConfig = {
  type: 'BizGoodsSelectDialog',
  isContainer: true,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };
    
    const { context} = nodeContext;

    const {
      componentProperties,
    } = context!;
    
    if (componentProperties) {
      componentProperties.categoryList = []
      delete componentProperties.variant;
    }
    return result;
  },
};
