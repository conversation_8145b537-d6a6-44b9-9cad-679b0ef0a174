import {ComponentConfig, GlobalContext, NodeContext} from '../types';

export const BizValueAddedCard: ComponentConfig = {
    type: 'BizValueAddedCard',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentProperties: {},
        };
        const {
            context,
            node,
        } = nodeContext;
        const {
            componentProperties
        } = context;

        delete componentProperties.states;

        return result;
    }
};
