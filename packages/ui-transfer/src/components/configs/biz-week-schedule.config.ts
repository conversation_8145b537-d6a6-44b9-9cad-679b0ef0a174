import { ComponentConfig } from '../types';

export const BizWeekSchedule: ComponentConfig = {
  type: 'BizWeekSchedule',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };
    
    const { node, context } = nodeContext;
    const { componentProperties } = context;
    
 
    result.componentGuideline.push('需要给 BizWeekSchedule props 补充 data 属性');
    result.componentGuideline.push('需要使用 BizWeekScheduleCell 来渲染slot');
    result.componentGuideline.push('注意：只有 variant="fill" 才支持分页，才提供分页props 和 events');
    

    return result;
  }
};
