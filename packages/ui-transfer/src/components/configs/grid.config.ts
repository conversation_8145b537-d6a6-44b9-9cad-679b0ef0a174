import { ComponentConfig } from '../types';

export const gridConfig: ComponentConfig = {
  type: 'AbcGrid',
  isContainer: true,
  customTransformer(nodeContext) {
    const result: Record<string, any> = {};
    const {
      context: {
        componentProperties
      }
    } = nodeContext;
    if (componentProperties?.theme === 'grid') {
      delete componentProperties.theme;
      componentProperties.grid = true;
    }
    if (componentProperties?.background === 'yellow') {
       componentProperties.background = true;
    }
    return result;
  },
};
