import { ComponentConfig } from '../types';

export const selectConfig: ComponentConfig = {
  type: 'AbcSelect',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('selectConfig customTransformer', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
    };
    const {
      context,
      node,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    
    if (componentProperties) {
      // 处理宽度
      if (node.width) {
        result.componentProperties.width = node.width;
      }
      if (componentProperties.referenceMode === 'default') {
        delete componentProperties.referenceMode;
      }
      
      if (componentProperties.size === 'default') {
        delete componentProperties.size;
      }
      
      if (componentProperties.placement === 'bottom-start') {
        delete componentProperties.placement;
      }
      
      if (componentProperties.disabled === true) {
        result.componentProperties.disabled = true;
      } else {
        delete componentProperties.disabled;
      }
      
      if (componentProperties.icon === false) {
        result.componentProperties.noIcon = true;
      } else {
        delete componentProperties.icon;
      }
      
      result.componentGuideline.push(`通过给 AbcOption 设置default 插槽:  label 为 "选项", value 为 "选项", 默认添加 5 个`);
      
      const option = componentProperties.option;
      delete componentProperties.option;
      if (option) {
        // 需要处理abc-option
        const optionNode = node.children.find(o => o.name === 'AbcOption');
        if (!optionNode) {
          return result;
        }
        
        if (optionNode.width && optionNode.width !== node.width) {
          result.componentProperties.innerWidth = optionNode.width;
        }
        
        const withSearchNode = optionNode.children.find(o => o.name === 'searchBar');
        if (withSearchNode) {
          result.componentProperties.withSearch = true;
        }
        
        const settingNode = optionNode.children.find(o => o.name === 'bottomWrapper');
        if (settingNode) {
          if (settingNode.variantProperties?.type === 'setting') {
            componentProperties.setting = true;
            result.componentGuideline.push(`拥有setting Props, 需要给 AbcSelect 添加 @set Event`);
          }
        }
        
        const optionGroupNode = optionNode.children.find(o => o.name === 'optionGroup');
        if (optionGroupNode) {
          console.log('optionGroupNode', optionGroupNode);
          const item = optionGroupNode.children.find(o => o.name === 'item');
          if (!item) {
            return result;
          }
          
          const checkboxNode = item.children.find(o => o.name === 'Container');
          if (checkboxNode) {
            result.componentProperties.multiple = true;
          }
          
        }
      }
    }
    return result;
  }
};
