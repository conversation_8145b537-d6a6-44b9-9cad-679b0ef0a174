import {ComponentConfig, ComponentSlot, GlobalContext, NodeContext} from '../types';
import { extractTextContent } from '../process-utils';

export const radioConfig: ComponentConfig = {
  type: 'AbcRadio',
  isContainer: false,
  nodeWhitelist: ["radio base"],
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;
    const result = {
        componentSlot: <ComponentSlot[]>[],
    };

    // size 处理
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }

    // enableCancel 处理 - 是否允许反选
    if (componentProperties?.['enableCancel'] === true) {
      componentProperties.enableCancel = true;
    } else {
      componentProperties.enableCancel = false;
    }

    // disabled 处理
    if (componentProperties?.['disabled'] === true) {
      componentProperties.disabled = true;
    } else {
      componentProperties.disabled = false;
    }

    // 处理子内容作为默认插槽（深度遍历，找到第一个 TEXT 节点并返回其文本）
    if ('children' in node && node.children?.length) {
      const findFirstTextContent = (n: any): string | null => {
        if (!n || !('children' in n) || !Array.isArray(n.children)) return null;
        for (const child of n.children) {
          if (child?.type === 'TEXT') {
            const text = extractTextContent(child as any);
            if (text) return text;
          }
          const nested = findFirstTextContent(child);
          if (nested) return nested;
        }
        return null;
      };

      const firstText = findFirstTextContent(node as any);
      if (firstText) {
        const defaultSlot = {
          name: 'default',
          content: firstText,
        };
        result.componentSlot.push(defaultSlot);
      }
    }
    delete  componentProperties['states'];
    delete componentProperties['disable'];
    delete componentProperties['theme'];
    return result;
  }
};
