import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {convertNode} from "@/components/process-utils";

export const menuConfig: ComponentConfig = {
    type: 'AbcMenu',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        console.log('menuConfig customTransformer===', { nodeContext, globalContext });
        const result: Record<string, any> = {
            componentGuideline: [],
            iconList: [],
        };
        const {
            context,
            node,
        } = nodeContext;
        const {
            // @ts-ignore
            componentProperties
        } = context;
        // let menuItemList = [];
        if (componentProperties) {
            if (componentProperties.badgeVariant === 'round') {
                delete componentProperties.badgeVariant;
            }
            delete componentProperties.states;
            delete componentProperties.type;
        }
            const menuItemNodes = [];
            // @ts-ignore
            node.children?.forEach(child => {
                if (child.name === 'menu item' || child.name === 'item 母组件/ 导航') {
                    menuItemNodes.push(child);
                }
            });
        for (let i = 0; i < menuItemNodes.length; i++) {
            const dataListItem: Record<string, any> = {
                index: i + 1,
                icon: '',
                value: '',
                type: 'menuItem',
                count: 0,
            }
            const menuItemNode = menuItemNodes[i];
            const menuItemNodeContext = {
                node: menuItemNode,
            }
            const menuItemNodeResult = await convertNode(menuItemNodeContext, globalContext);
            console.log('menuItemNodeResult=', menuItemNodeResult);
            const icon = menuItemNodeResult?.children?.find(it=>{
                return it.componentType === 'AbcIcon';
            })?.name || '';
            dataListItem.icon = icon;
            // 构造渲染数据
            result.iconList.push(dataListItem);
            result.componentGuideline.push(`第${i+1}个item或者subMenu的icon可以参考${dataListItem.icon}图标`);
        }
        // console.log('node.children=', menuItemList);
        //
        // if (menuItemList.length) {
        //     componentProperties.menuItemList = menuItemList;
        //     result.componentGuideline.push(`分析参考截图后，请直接使用解析好的componentProperties.menuItemList中的数据渲染abc-menu-item子项，将menuItemList放置data中，menuItemList是一个数组，每一项包含icon、value、type、count等属性。请遍历menuItemList数组，依次渲染每个菜单项。对于type为menuItem的项，使用abc-menu-item组件渲染，将icon属性用于图标，value用于默认插槽内容；对于type为AbcDivider的项，使用分割线并将前后内容用abc-menu-group包裹，但是没有divider是不需要渲染abc-menu-group的。严格按照AbcMenu组件的用法文档进行实现，不要使用其他组件代替。`);
        // }
        // }
        result.componentGuideline.push('根据截图分析 menu 需要的信息 尤其需要注意item之间有分割线divider的情况，需要注意如果出现分割线并不需要单独渲染分割线，而是应该用group把item包裹起来,group之间会自动出现分割线，如果不存在分割线，不需要用group包裹元素；如果截图中的菜单项之间没有明确的分割线或分组，则不需要使用abc-menu-group，直接使用abc-menu-item或者abc-sub-menu即可，另外需要注意item的value值不是通过props传入的，而是个defaultSlot，是直接放置在插槽中的,而subMenu的value是需要通过props传入的，而不是一个defaultSlot插槽,这点需要item和subMenu做区别;此外badge组件不需要自己写，传入count属性就能生成;详细用法参考AbcMenu的usage常规用法。注意，需要严格按照规范和截图来生成');

        return result;
    }
};
