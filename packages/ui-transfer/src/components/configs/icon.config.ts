import { ComponentConfig } from '../types';
import { getNodeStyles } from "@/utils/utils";

export const iconConfig: ComponentConfig = {
  type: 'AbcIcon',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {}
    };

    const { node, context } = nodeContext;

    // 根据 width 设置 size
    const width = node.width;
    if (width && width != 16) {
      result.componentProperties.size = width;
    }

    if (context.componentProperties) {
      // 处理 color;
      const { svg } = context.componentProperties;

      if (svg === false) {
        const child = node.children[0];

        if (child) {
          const { backgroundColor } = getNodeStyles(child);

          if (backgroundColor) {
            result.componentProperties.color = backgroundColor;
          }
        }
      }

      delete context.componentProperties.svg
    }

    return result;
  }
};
