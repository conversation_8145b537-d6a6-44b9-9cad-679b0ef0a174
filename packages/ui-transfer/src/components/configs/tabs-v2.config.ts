import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import { traverseAndExtractAbcText } from "@/components/process-utils";

export const tabsConfig: ComponentConfig = {
  type: 'AbcTabsV2',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentGuideline: [],
      componentProperties: {},
      componentSlot: [],
      children: [],
    };

    const {
      context,
      node,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    
    if (componentProperties) {
      // 如果是默认的则删除
      if (componentProperties.direction === 'horizontal') {
        delete componentProperties.direction;
      }
      
      if (componentProperties.theme === 'default') {
        delete componentProperties.theme;
      }
      
      if (componentProperties.type === 'default') {
        delete componentProperties.type;
      }
      
      if (componentProperties.border === true) {
        delete componentProperties.border;
      } else if (componentProperties.type === 'default') {
        result.componentProperties.border = false;
      } else {
        delete componentProperties.border;
      } 
      
      if (componentProperties.noCloseBorder === true) {
        result.componentProperties.noCloseBorder = true;
      } else {
        delete componentProperties.noCloseBorder;
      }
      
      if (componentProperties.canAppend === true) {
        result.componentProperties.canAppend = true;
      } else {
        delete componentProperties.canAppend;
      }
      
      if (componentProperties.canAppend === true) {
        result.componentProperties.canAppend = true;
      } else {
        delete componentProperties.canAppend;
      }
      
      if (componentProperties.canDel === true) {
        result.componentProperties.canDel = true;
      } else {
        delete componentProperties.canDel;
      }
      
      if (componentProperties.adaptation === true) {
        result.componentProperties.adaptation = true;
      } else {
        delete componentProperties.adaptation;
      }
      
      const item = componentProperties.item;
      delete componentProperties.item;

      const options = [];
      if (node?.children?.length) {
        for (let i = 0; i < node.children.length; i++) { 
          const label = traverseAndExtractAbcText(node.children[i]);
          if (label) {
            options.push({
              label: label,
              value: i,
            });
          }
        }
      }
      componentProperties.option = options;
      componentProperties.style = 'width: 100%;';
      
      result.componentGuideline.push(`需要给 AbcTabsV2 添加 v-model 的 value，value 默认第一个`);
    }
    
    return result;
  }
};
