import { ComponentConfig, NodeContext, GlobalContext } from '../types';
import {convertNode} from "@/components/process-utils";

export const BizSettingForm: ComponentConfig = {
  type: 'BizSettingForm',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
      const result: Record<string, any> = {
          componentGuideline: [],
          children: [],
      };
      
      const {
          context,
          node
      } = nodeContext;

      const nodeList = node?.children || [];

      for (let i = 0; i < nodeList.length; i++) {
          const node = nodeList[i];
          const nodeContext = {
              node: node,
          };

          const nodeResult = await convertNode(nodeContext, globalContext);
          if (nodeResult) {
              result.children.push(nodeResult);
          }
      }

      return result;
  }
};
