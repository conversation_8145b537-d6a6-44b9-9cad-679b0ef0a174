import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {convertNode} from "@/components/process-utils";

export const formItemGroupConfig: ComponentConfig = {
    type: 'AbcFormItemGroup',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentSlot: [],
        };
        const {context, node} = nodeContext;

        context.componentProperties = context?.componentProperties || {};
        const componentProperties = context.componentProperties;
        const parentNode = node?.parent?.componentProperties || {};


        if (node && 'children' in node && node.children?.length) {
            for (const child of node.children) {
                if (child.children && child.children.length) {

                    for (const ch of child.children) {
                        const itemChildContext = {
                            node: ch
                        };

                        const convertedItemChild = await convertNode(itemChildContext, globalContext);
                        // 添加到 componentSlot
                        if (result.componentSlot) {
                            result.componentSlot.push({
                                name: 'default',
                                content: convertedItemChild,
                            });
                        }
                    }
                }
            }
        }
        const gridColumnCount = parentNode?.gridColumnCount || 1;
        // 处理 gridColumnCount 属性，即分割成几列
        if (gridColumnCount.value) {
            const columnCount = gridColumnCount.value;
            if (!isNaN(columnCount) && columnCount > 0) {
                componentProperties.gridColumnCount = columnCount;
            } else {
                // 默认值为 1
                componentProperties.gridColumnCount = 1;
            }
            componentProperties.grid = true;
        }

        // 处理 isExcel 属性，输入框是否有 Excel 样式
        if (componentProperties.isExcel === true) {
            componentProperties.isExcel = true;
        } else {
            componentProperties.isExcel = false;
        }
        return result;
    }
};
