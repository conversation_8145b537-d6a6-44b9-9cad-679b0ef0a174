import { ComponentConfig } from '../types';
import { convertNode } from '../process-utils';


export const stepsConfig: ComponentConfig = {
  type: 'AbcSteps',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };
    
    const defaultActiveColor = '#0090ff'
    

    const { node } = nodeContext;
    const stepNodes = node.children.filter((child) => child.name === 'AbcStep');
    
    
    for (const [index, child] of stepNodes.entries()) {
      const stepItem = await convertNode({ node: child, index }, globalContext);
      if (stepItem.extendInfo.activeIconColor && stepItem.extendInfo.activeIconColor !== defaultActiveColor) {
        result.componentProperties.activeColor = stepItem.extendInfo.activeIconColor;
      }
      if (stepItem.extendInfo.activeIndex) {
        result.componentProperties.active = stepItem.extendInfo.activeIndex;
      }
      
      result.componentSlot.push({
        name: 'default',
        figmaInfo: stepItem
      })
    }
    
    return result;
  }
};
