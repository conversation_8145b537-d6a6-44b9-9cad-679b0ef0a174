import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const linkConfig: ComponentConfig = {
  type: 'AbcLink',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentSlot: [],
    };
    const {
      context,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      // 处理 prepend 和 append
      if (componentProperties.prepend) {
        result.componentSlot.push({
          name: 'prepend',
          content: {
            type: 'AbcIcon',
            name: componentProperties['ic-name-prepend'],
          }
        })
      }
      if (componentProperties.append) {
        result.componentSlot.push({
          name: 'append',
          content: {
            type: 'AbcIcon',
            name: componentProperties['ic-name-append'],
          }
        });
      }

      result.componentSlot.push({
        name: 'default',
        content: componentProperties.text,
      })

      delete componentProperties['ic-name-prepend'];
      delete componentProperties['prepend'];
      delete componentProperties['append'];
      delete componentProperties['ic-name-append'];
      delete componentProperties.text;

      delete componentProperties.states;
    }
    return result;
  },
};
