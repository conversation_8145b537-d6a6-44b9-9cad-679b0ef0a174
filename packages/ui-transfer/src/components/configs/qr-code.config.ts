import { ComponentConfig } from '../types';

export const qrCodeConfig: ComponentConfig = {
  type: 'AbcQrCode',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('qrCodeConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentGuideline: [],
    };
    const {
      context,
      node
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (node.width) {
        if (node.margin !== '200' && node.margin !== 200) {
          componentProperties.width = node.width;
        }
      }
      if (node.margin) {
        if (node.margin !== '4' && node.margin !== 4) {
          componentProperties.margin = node.margin;
        }
      }
      result.componentGuideline.push('AbcQrCode组件，用于生成二维码,默认不必生成src的内容，width和margin属性要传入才传入，不传入不需要设置');
      delete componentProperties.states;
    }
    return result;
  }
};
