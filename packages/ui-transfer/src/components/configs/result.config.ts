import { convertNode, extractTextContent } from '../process-utils';
import { ComponentConfig } from '../types';

export const resultConfig: ComponentConfig = {
  type: 'AbcResult',
  isContainer: false,
  async customTransformer(nodeContext, globalContext) {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };
    const {
      node,
    } = nodeContext;

    // 解析 Title 和 content
    const titleContentNode = node.children?.find(child => child.name === 'TitleContentIcon')?.children?.find(child => child.name === 'TitleContent');
    if (titleContentNode) {
      titleContentNode.children?.forEach(child => {
        if (child.name === 'Title') {
          result.componentProperties.title = extractTextContent(child.children?.[0]);
        }
        if (child.name === 'Content') {
          result.componentProperties.content = extractTextContent(child.children?.[0]);
        }
      })
    }

    // 解析 button-group
    const buttonGroupNode = node.children?.find(child => child.name === 'button-group');
    if (buttonGroupNode) {
      const buttonGroupContext = {
        node: buttonGroupNode,
      }
      const buttonGroupNodeResult = await convertNode(buttonGroupContext, globalContext);
      if (buttonGroupNodeResult) {
        if (buttonGroupNodeResult.componentProperties) {
          delete buttonGroupNodeResult.componentProperties
        }
        result.componentSlot.push({
          name: 'btn',
          content: buttonGroupNodeResult
        })
      }
    }

    return result;
  },
};
