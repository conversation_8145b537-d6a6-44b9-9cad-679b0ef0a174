import { ComponentConfig, NodeContext, GlobalContext } from '../types';
import { traverseAndExtractAbcText } from "../process-utils";

export const checkboxGroupConfig: ComponentConfig = {
    type: 'AbcCheckboxGroup',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
          componentGuideline: [],
          componentProperties: {},
          componentSlot: [],
        };
    
        const {
          context,
          node,
        } = nodeContext;
        const {
          componentProperties
        } = context;
        
        if (componentProperties) {
          delete componentProperties.item;
    
          const options = [];
          if (node?.children?.length) {
            for (let i = 0; i < node.children.length; i++) { 
              const label = traverseAndExtractAbcText(node.children[i], 'text');
              if (label) {
                options.push({
                  label: label,
                  value: i,
                });
              }
            }
          }
          
          result.componentGuideline.push(`options为${JSON.stringify(options)}`);
          result.componentGuideline.push(`需要给 AbcCheckboxGroup 添加 v-model 的 value，value 默认options的第一个value`);
          result.componentGuideline.push(`AbcCheckboxGroup中需要循环遍历 options 生成 AbcCheckbox，每个 AbcCheckbox 添加 label 属性，值为 option.value，AbcCheckbox中的文本节点为 option.label`);
        }
        
        return result;
      }
}
