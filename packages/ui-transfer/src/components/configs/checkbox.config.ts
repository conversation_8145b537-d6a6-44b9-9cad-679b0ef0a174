import { ComponentConfig } from '../types';

export const checkboxConfig: ComponentConfig = {
  type: 'AbcCheckbox',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('checkboxConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {};
    const {
      context,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (componentProperties.disabled === false) {
        delete componentProperties.disabled;
      }
      if (componentProperties.theme === 'default') {
        delete componentProperties.variant;
      }
      if (componentProperties.shape === 'shape') {
        delete componentProperties.shape;
      }
      if (componentProperties.noBorder === false)  {
        delete componentProperties.noBorder;
      }
      if (componentProperties.states === 'indeterminate') {
        componentProperties.indeterminate = true;
      }
      delete componentProperties.states;
      // 不存在下列属性，需要移除
      delete componentProperties.tips;
      delete componentProperties.theme;
    }
    return result;
  }
};
