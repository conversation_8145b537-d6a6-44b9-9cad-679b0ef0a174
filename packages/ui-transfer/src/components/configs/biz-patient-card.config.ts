import {ComponentConfig, ComponentSlot, GlobalContext, NodeContext} from '../types';

export const BizPatientCard: ComponentConfig = {
  type: 'BizPatientCard',
  isContainer: false,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentGuideline: [],
    }
    const {
      context,
      node,
    } = nodeContext;
    // @ts-ignore
    const { componentProperties } = context;
    if (componentProperties) {
      // 需要处理宽度
      if (node.width) {
        componentProperties.cardWidth = node.width;
      }
      if (node.height) {
        componentProperties.cardHeight = node.height;
      }
    }
    result.componentGuideline.push('请注意生成BizPatientCard的时候插槽内不需要生成其他内容');
    return result;
  },
};
