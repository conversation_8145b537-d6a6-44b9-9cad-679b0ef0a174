import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {convertNode, extractTextContent} from "@/components/process-utils";
import {isTruthyForFigma} from "@/utils/utils";

export const descriptionsConfig: ComponentConfig = {
    type: 'AbcDescriptions',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentProperties: {},
            componentGuideline: [],
            componentSlot: [],
        };

        const {node, context} = nodeContext;

        // 先看下是否解组了
        const frameNode = node as InstanceNode;

        const componentProperties = context.componentProperties;

        let lastRow = null;
        let lastRowFirstItem = null;
        let itemLength = 4;
        // 找到名字为 Descriptions 的组件
        const descriptionsNode = frameNode.children.find(child => child.name.toLowerCase() === 'abcdescriptionitemsgroup');
        if (descriptionsNode) {
            // 在这里可以处理找到的 Descriptions 节点
            const firstRow = descriptionsNode.children?.[0];
            lastRow = descriptionsNode?.children[descriptionsNode?.children?.length - 1];
            lastRowFirstItem = lastRow?.children?.[0];
            itemLength = firstRow?.children?.length || 4;
        }


        if (!componentProperties) {
            // 读取不到，说明被解组了，需要特殊处理下
            // 处理 size，取最后一个子节点的，height 推测 size

            const height = lastRow.height;
            const sizeMap = {
                28: 'small',
                32: '',
                40: 'large',
            };
            const size = sizeMap[height];
            if (size) {
                result.componentProperties.size = size;
            }

            // 判断是否是 grid，取最后一行第一个子节点 title 的 theme
            console.log('lastRowFirstItem', lastRowFirstItem);
            if (lastRowFirstItem?.children?.[0]?.componentProperties?.theme?.value === 'grid') {
                result.componentProperties.grid = true;
                result.componentProperties.bordered = true;
            }
        } else {
            // 列数
            componentProperties.column = itemLength;

            delete  componentProperties.theme;
            delete componentProperties.form;

            if (isTruthyForFigma(componentProperties.grid)) {
                delete componentProperties.theme;
                componentProperties.grid = true;
                result.componentProperties.bordered = true;
            } else {
                delete componentProperties.grid;
            }

            if (isTruthyForFigma(componentProperties.background)) {
                componentProperties.background = true;
            } else {
                delete componentProperties.background;
            }

            if (!isTruthyForFigma(componentProperties.customTitleStyle) || !componentProperties.customTitleStyle) {
                delete componentProperties.customTitleStyle;
            }
            if (isTruthyForFigma(componentProperties.disabled)) {
                componentProperties.disabled = true;
            } else {
                delete componentProperties.disabled;
            }

            if (componentProperties.size === 'default') {
                delete componentProperties.size;
            }


            if (isTruthyForFigma(componentProperties.title)) {
                const titleContentNode = frameNode.children?.find(child => child.name === 'title')?.children?.find(child => child.name === 'AbcText');
                if (titleContentNode) {
                    result.componentProperties.title = extractTextContent(titleContentNode.children?.[0]);
                }
                // 处理 slot
                const headerNode = frameNode.children.find(child => child.name.toLowerCase() === 'title')?.children?.find(child => child.name === 'slot');
                if(headerNode) {
                    const headerNodeContext: NodeContext = {
                        node: headerNode as SceneNode,
                    };
                    const headerSlotInfo = await convertNode(headerNodeContext, globalContext);
                    if (headerSlotInfo) {
                        result.componentSlot.push({
                            slotName: 'title',
                            figmaInfo: headerSlotInfo
                        });
                    }
                }

                delete componentProperties.title;
            } else {
                delete componentProperties.title;
            }
            if (componentProperties.bordered) {
                componentProperties.borderStyle = componentProperties.bordered
                componentProperties.bordered = true;
            } else {
                componentProperties.bordered = false;
            }

            componentProperties.style = "width: 100%";

        }


        // 根据 lastRowFirstItem 推断 labelWidth
        if (lastRowFirstItem) {
            const labelWidth = lastRowFirstItem.children?.[0]?.width;
            if (labelWidth) {
                result.componentProperties = {
                    ...result.componentProperties,
                    labelWidth
                }
            }
        }

        // 处理描述项
        if (descriptionsNode?.children?.length) {
            let nodesToConvert = [];
            for (let i = 0; i < descriptionsNode.children.length; i++) {
                const group = descriptionsNode.children[i];
                if (group && group.children) {
                    nodesToConvert = nodesToConvert.concat(group.children);
                }
            }

            console.log('nodesToConvert', nodesToConvert)

            const convertedItems: any[] = [];

            for (const node of nodesToConvert) {
                const nodeContent: NodeContext = {
                    node,
                };
                const converted = await convertNode(nodeContent, globalContext);
                convertedItems.push(converted);
            }

            result.componentSlot.push({
                slotName: 'default',
                content: convertedItems,
            });
        }

        return result;
    }
};
