import {convertNode} from '../process-utils';
import {ComponentConfig, ComponentSlot, ConversionContext, GlobalContext, NodeContext} from '../types';

export const formConfig: ComponentConfig = {
    type: 'AbcForm',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Partial<ConversionContext> & {
            componentSlot?: ComponentSlot[],
            componentGuideline?: string[]
        } = {};
        result.componentSlot = [];
        result.componentGuideline = [];
        const {context, node} = nodeContext;

        const componentProperties = context?.componentProperties;

        if (componentProperties) {
            // 处理 labelPosition 属性
            if (componentProperties.labelPosition) {
                const validPositions = ['left-top', 'top', 'left', 'inner'];
                if (!validPositions.includes(componentProperties.labelPosition)) {
                    componentProperties.labelPosition = 'left';
                }
            }

            // 处理 itemNoMargin 属性
            if (componentProperties.itemNoMargin === true) {
                componentProperties.itemNoMargin = true;
            } else {
                componentProperties.itemNoMargin = false;
            }

            // 处理 item-block 属性
            if (componentProperties['item-block'] === true) {
                componentProperties.itemBlock = true;
                // 删除原始属性，使用驼峰命名的属性替代
            } else {
                componentProperties.itemBlock = false;
            }
            delete componentProperties['item-block']


            // 处理 labelWidth 属性
            if (componentProperties.labelWidth) {
                // 确保 labelWidth 是数字
                const labelWidth = parseInt(componentProperties.labelWidth);
                if (!isNaN(labelWidth)) {
                    componentProperties.labelWidth = labelWidth;
                }
            }
            // 找到第一个formItem下面的label元素的宽度作为label的宽度
            // 仅当未显式传入 labelWidth 时，尝试从子节点中自动推断
            if (!componentProperties.labelWidth && node && 'children' in node) {
                const findFirstLabelWidth = (n: any): number | null => {
                    if (!n || !('children' in n) || !Array.isArray(n.children)) return null;
                    for (const child of n.children) {
                        const childName = child?.name?.toLowerCase?.();
                        if (childName === 'label' && typeof child.width === 'number' && child.width > 0) {
                            return Math.round(child.width);
                        }
                        const nested = findFirstLabelWidth(child);
                        if (typeof nested === 'number') return nested;
                    }
                    return null;
                };

                const autoLabelWidth = findFirstLabelWidth(node as any);
                if (typeof autoLabelWidth === 'number' && autoLabelWidth > 0) {
                    componentProperties.labelWidth = autoLabelWidth;
                }
            }

            // 处理 marginSize 属性
            if (componentProperties.marginSize) {
                const validSizes = ['default', 'large'];
                if (!validSizes.includes(componentProperties.marginSize)) {
                    componentProperties.marginSize = 'default';
                }
            }

            // 处理 isDetail 属性
            if (componentProperties.isDetail === true) {
                componentProperties.isDetail = true;
            } else {
                componentProperties.isDetail = false;
            }

            // 处理 labelAlign 属性
            if (componentProperties.labelAlign) {
                const validAligns = ['left', 'right'];
                if (!validAligns.includes(componentProperties.labelAlign)) {
                    componentProperties.labelAlign = 'right';
                }
            }
        }
        delete componentProperties.gridColumnCount;

        // 遍历查找 itemGroup 子节点
        if (node && 'children' in node && node.children?.length) {
            for (const child of node.children) {
                if (child.name && child.name.toLowerCase() === 'abcformitemgroup') {

                    const itemChildContext = {
                        node: child
                    };
                    const convertedItemChild = await convertNode(itemChildContext, globalContext);
                    // 添加到 componentSlot
                    if (result.componentSlot) {
                        result.componentSlot.push({
                            name: 'default',
                            content: convertedItemChild,
                        });
                    }
                }
            }
        }

        if (result.componentGuideline && !result.componentGuideline.length) {
            result.componentGuideline = undefined;
        }

        if (result.componentSlot && !result.componentSlot.length) {
            result.componentSlot = undefined;
        }

        return result;
    }
};
