import { ComponentConfig } from '../types';

export const datePaginationConfig: ComponentConfig = {
  type: 'AbcDatePagination',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('datePaginationConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentGuideline: [],
    };
    const {
      context,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (componentProperties.variant === 'fill') {
        delete componentProperties.variant;
      }
      if (componentProperties.size === 'normal') {
        delete componentProperties.size;
      }
      delete componentProperties.states;
    }
    result.componentGuideline.push('AbcDatePagination组件，周分页器, 不需要必须用abc-space来布局，需要视情况而定，仅需要生成需要生成的内容');
    return result;
  }
};
