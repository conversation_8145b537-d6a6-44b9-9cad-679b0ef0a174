import {ComponentConfig, NodeContext} from '../types';

export const loadingConfig: ComponentConfig = {
    type: 'AbcLoading',
    isContainer: true,
    customTransformer: async (nodeContext: NodeContext) => {
        const result: Record<string, any> = {};

        const {context} = nodeContext;

        const {
            componentProperties
        } = context!;
        if (componentProperties) {
            delete componentProperties['showText'];

            if (!componentProperties.variant) {
                componentProperties['variant'] = 'content';
            }
        }

        return result;
    }
};
