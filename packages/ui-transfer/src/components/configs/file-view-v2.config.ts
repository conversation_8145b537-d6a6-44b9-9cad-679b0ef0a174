import {ComponentConfig} from '../types';

export const fileViewConfig: ComponentConfig = {
    type: 'AbcFileViewV2',
    isContainer: false,
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {};

        const {
            context,
            node,
        } = nodeContext;

        console.log('fileViewConfig', node, globalContext);

        const {componentProperties} = context!;

        if (componentProperties) {
            // 文件名称
            if (componentProperties.fileName) {
                componentProperties.file = {
                    fileName: componentProperties.fileName,
                }
                delete componentProperties.fileName;
            }
            // 文件地址
            if (componentProperties.url) {
                componentProperties.file = {
                    ...componentProperties.file,
                    url: componentProperties.url ?? ''
                }
                delete componentProperties.url;
            }

            if (componentProperties.width === true) {
                componentProperties.width = node.width
            }

            if (componentProperties.height === true) {
                componentProperties.height = node.height
            }
        }

        return result;
    }
};
