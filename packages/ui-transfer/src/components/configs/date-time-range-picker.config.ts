import {
  ComponentConfig,
  ComponentSlot,
  NodeContext,
} from "@/components/types";

export const dateTimeRangePickerConfig: ComponentConfig = {
  type: "AbcDateTimeRangePicker",
  isContainer: false,
  customTransformer: async (
    nodeContext: NodeContext,
  ) => {
    const result: Record<string, any> = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };

    const { context } = nodeContext;

    /**
     * 使用可选的组件属性
     */
    const componentProperties = context?.componentProperties;

    if (componentProperties) {
      if (
        componentProperties.adaptiveWidth === "false" ||
        componentProperties.adaptiveWidth === false
      ) {
        delete componentProperties.adaptiveWidth;
      } else if (componentProperties.adaptiveWidth === "true") {
        componentProperties.adaptiveWidth = true;
      }

      if (
        componentProperties.clearable === "true" ||
        componentProperties.clearable === true
      ) {
        delete componentProperties.clearable;
      } else if (componentProperties.clearable === "false") {
        componentProperties.clearable = false;
      }

      if (
        componentProperties.defaultShowTimeCheck === "true" ||
        componentProperties.defaultShowTimeCheck === true
      ) {
        delete componentProperties.defaultShowTimeCheck;
      } else if (componentProperties.defaultShowTimeCheck === "false") {
        componentProperties.defaultShowTimeCheck = false;
      }

      if (
        componentProperties.showIcon === "true" ||
        componentProperties.showIcon === true
      ) {
        delete componentProperties.showIcon;
      } else if (componentProperties.showIcon === "false") {
        componentProperties.showIcon = false;
      }

      if (componentProperties.size === "default") {
        delete componentProperties.size;
      }

      if (componentProperties.states) {
        delete componentProperties.states;
      }

      if (
        componentProperties.disabled === "false" ||
        componentProperties.disabled === false
      ) {
        delete componentProperties.disabled;
      } else if (componentProperties.disabled === "true") {
        componentProperties.disabled = true;
      }
    }

    return result;
  },
};
