import {ComponentConfig, ComponentSlot, GlobalContext, NodeContext} from '../types';

export const BizPatientSelector: ComponentConfig = {
  type: 'BizPatientSelector',
  isContainer: false,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentGuideline: [],
    }
    const {
      context,
      node,
    } = nodeContext;
    
    // 添加BizPatientSelector的引用路径信息
    result.componentGuideline.push('BizPatientSelector的引用路径是src/views/layout/patient/patient-section/index.vue');
    result.componentGuideline.push('在生成的vue sfc 代码中需要对BizPatientSelector组件进行导入与局部注册');
    result.componentGuideline.push('导入格式如下：import BizPatientSelector from \'src/views/layout/patient/patient-section/index.vue\';');
    result.componentGuideline.push('注册格式如下：components: { BizPatientSelector }');
    result.componentGuideline.push('该组件的 v-model 接收 patient，patient 初始值键值对分别为 id: "", name: "", sex: "", age: { year: null, month: null, day: null }, birthday: "", created: "", mobile: "", countryCode: "", idCard: "", idCardType: "身份证", sn: "", shebaoCardInfo: null');
    
    return result;
  },
};
