import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {convertNode, extractTextContent} from "@/components/process-utils";
import {isTruthyForFigma} from "@/utils/utils";

export const descriptionsConfig: ComponentConfig = {
    type: 'AbcDescriptionsItem',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            componentProperties: {},
            componentSlot: [],
            componentGuideline: [],
        };

        const { node, context } = nodeContext;
        const componentProperties = context.componentProperties;

        let labelText = '标题';
        const labelNode = node.children.find( child => child.name.toLowerCase() === 'label');
        if(labelNode?.children?.[0]) {
            labelText = extractTextContent(labelNode.children[0])
            componentProperties.label = labelText;
        }
        delete componentProperties.size;
        delete componentProperties.grid;

        const defaultSlot= node.children.find( child => child.name.toLowerCase() === 'slot');
        if(defaultSlot?.children?.[0]) {
            const slotNode = await convertNode({
                node: defaultSlot?.children?.[0],
            }, globalContext);

            result.componentSlot.push({
                name: 'default',
                content: slotNode
            })
        }
        return result;
    }
};
