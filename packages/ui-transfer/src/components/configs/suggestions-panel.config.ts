import { convertNode } from '../process-utils';
import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const suggestionsPanelConfig: ComponentConfig = {
  type: 'AbcSuggestionsPanel',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;
    
    if (!context || !context.componentProperties) {
      return {};
    }
    
    const result = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    
    // 遍历子节点，查找是否存在指定名称的插槽
    if ('children' in node && node.children?.length) {
      // 定义支持的插槽名称
      const supportedSlots = [
        'suggestionHeader',     // 下拉面板抬头
        'suggestionItemGroup', // 搜索列表
        'suggestionFixedFooter' // 下拉面板固定底部
      ];
      
      console.log('AbcSuggestionsPanel', node)
      // 遍历子节点
      for (const child of node.children) {
        if (child.name) {
          const nodeName = child.name.toLowerCase();
          
          // 检查节点名称是否匹配支持的插槽
          for (const slotName of supportedSlots) {
            if (nodeName === slotName.toLowerCase()) {
              result.componentGuideline.push(`使用 AbcSuggestionsPanel 的 ${slotName} slot`);
              
              // 如果有子节点，则转换子节点
              if ('children' in child && child.children?.length && child.children[0]) {
                const slotNodeContext = {
                  node: child.children[0]
                };
                
                const slotNode = await convertNode(slotNodeContext, globalContext);
                
                result.componentSlot.push({
                  name: slotName,
                  content: slotNode,
                });
              }
              
              break; // 找到匹配的插槽后跳出内层循环
            }
          }
        }
      }
    }
    
    // 如果没有指南，删除指南属性
    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }
    
    // 如果没有插槽，删除插槽属性
    if (!result.componentSlot.length) {
      delete result.componentSlot;
    }
    
    return result;
  }
};
