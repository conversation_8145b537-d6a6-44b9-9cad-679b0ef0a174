import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import { convertToFlex, convertNode } from '../process-utils';

export const flexConfig: ComponentConfig = {
  type: 'AbcFlex',
  isContainer: true,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      style: undefined,
      componentGuideline: [],
      componentSlot: [],
    };

    const {
      node,
    } = nodeContext;

    const flexInfo = convertToFlex(node as FrameNode);

      if (node.layoutSizingHorizontal === 'FILL') {
        result.style = {
          width: '100%'
        }
        result.componentGuideline.push('通过给 AbcFlex 设置 style="width: 100%"实现撑满');
      }

    // 设置 padding
    if (node.paddingTop !== 0 || node.paddingRight !== 0 || node.paddingBottom !== 0 || node.paddingLeft !== 0) {
      const padding = `${node.paddingTop}px ${node.paddingRight}px ${node.paddingBottom}px ${node.paddingLeft}px`;
      result.style = {
        ...result.style,
        padding,
      }
      result.componentGuideline.push(`通过给 AbcFlex 设置 style="padding: ${padding}"实现设置padding`);
    }

    result.componentProperties = {
      ...flexInfo
    }

    // console.log('flex.customTransformer', node.children);
    // 处理子节点为 default slot
    if ('children' in node && node.children?.length) {
      const slotContent = [];
      for (const child of node.children) {
        const slotNodeContext = { node: child };
        const slotNode = await convertNode(slotNodeContext, globalContext);
        if (slotNode) {
          slotContent.push(slotNode);
        }
      }
      if (slotContent.length) {
        result.componentSlot.push({
          name: 'default',
          content: slotContent,
        });
        result.componentGuideline.push('通过 default slot 渲染子节点');
      }
    }

    if (!Object.keys(result.componentProperties).length) {
      delete result.componentProperties;
    }

    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }

    if (!result.componentSlot.length) {
      delete result.componentSlot;
    }

    return result;
  },
};
