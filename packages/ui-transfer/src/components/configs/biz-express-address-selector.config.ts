import { ComponentConfig } from '../types';

export const BizExpressAddressSelector: ComponentConfig = {
  type: 'BizExpressAddressSelector',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };
    
    const { node, context } = nodeContext;
    const { componentProperties } = context;
    Object.keys(componentProperties).forEach(key => {
      delete componentProperties[key];
    });
    
    result.componentGuideline.push('需要给 BizExpressAddressSelector props 模拟 addressList 和 value 属性');
    result.componentGuideline.push('需要给 BizExpressAddressSelector 绑定对应的 events');
    

    return result;
  }
};
