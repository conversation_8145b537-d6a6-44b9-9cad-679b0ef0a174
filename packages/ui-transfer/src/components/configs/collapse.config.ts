import { ComponentConfig } from '../types';

export const collapseConfig: ComponentConfig = {
  type: 'AbcCollapse',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentGuideline: [],
    };

    result.componentGuideline.push('使用 abc-collapse 搭配 abc-collapse-item 使用');
    return result;
  },
};
