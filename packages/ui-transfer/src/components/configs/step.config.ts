import { ComponentConfig } from '../types';
import { getNodeStyles } from "@/utils/utils";
import { convertNode } from '../process-utils';


export const stepConfig: ComponentConfig = {
  type: 'AbcStep',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { node, context, index } = nodeContext;

    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [
        {
          name: 'default',
          figmaInfo: null
        }
      ],
      extendInfo: {

      }
    };
    
    if (context.componentProperties.states === 'active') {
      result.extendInfo.activeIndex = index;
    }
    
    result.componentProperties.index = index;

    
    const icon = node.children.find(child => child.name === 'icon')
    const text = node.children.find(child => child.name === 'text')
    
    if (icon) {
        const iconNode = await convertNode({ node: icon }, globalContext);
        if (iconNode.componentProperties.theme === 'custom-icon') {
          result.componentProperties.icon = icon.children[0].name;
        }
        
        if (context.componentProperties.states === 'active') {
          if (iconNode.componentProperties.theme === 'dot') {
            const { backgroundColor } = getNodeStyles(icon.children[0].children[0]);
            result.extendInfo.activeIconColor = backgroundColor;
          }
          
          if (iconNode.componentProperties.theme === 'default') {
            const { backgroundColor } = getNodeStyles(icon.children[0]);
            result.extendInfo.activeIconColor = backgroundColor;
          }
          
          if (iconNode.componentProperties.theme === 'custom-icon') {
            const { backgroundColor } = getNodeStyles(icon.children[0].children[0]);
            result.extendInfo.activeIconColor = backgroundColor;
          }
        }
    }

    if (text) {
      const textNode = await convertNode({ node: text }, globalContext);
      const title = textNode.children.find(child => child.name === 'title');
      if (title) {
        const titleSlot = title.children[0];
        if (titleSlot.componentType === 'AbcText') {
          delete titleSlot.componentProperties.theme;
        }
        result.componentSlot[0].figmaInfo = titleSlot;
      }
      
      const content = textNode.children.find(child => child.name === 'content');
      if (content) {
        const contentSlot = content.children[0];
        if (contentSlot.componentType === 'AbcText') {
          delete contentSlot.componentProperties.theme;
        }
        result.componentSlot.push({
          name: 'text',
          figmaInfo: contentSlot
        })
      }
    }
    
    context.componentProperties = {};
    
    return result;
  }
};
