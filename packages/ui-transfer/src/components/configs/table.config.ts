import { convertNode } from '../process-utils';
import { ComponentConfig, GlobalContext, NodeContext } from '../types';

interface ColumnDef {
  label: string;
  key: string;
  style: {
    width?: string;
    minWidth?: string;
    maxWidth?: string;
    flex?: string;
    textAlign?: string;
  };
  children?: ColumnDef[];
}

/**
 * 递归将合并表头转换为 ColumnDef 结构
 * @param node 当前节点
 * @param columnIndex 列索引
 * @returns ColumnDef 结构
 */
function transformMergedHeaderToColumnDef(node: SceneNode, columnIndex: number): ColumnDef | null {
  // 找到当前层级的 header 节点
  if(node.name === 'header') {
    const titleNode = node.children?.find(child => child.name === 'AbcText');
    const columnDef: ColumnDef = {
      label: titleNode.children?.[0]?.characters || `SubColumn${columnIndex + 1}`,
      key: `column${columnIndex}SubColumn${columnIndex+1}`,
      style: {
        width: node.width + 'px',
        minWidth: node.minWidth ? node.minWidth + 'px' : undefined,
        maxWidth: node.maxWidth ? node.maxWidth + 'px' : undefined,
        flex: node.layoutSizingHorizontal === 'FILL' ? '1' : undefined,
        textAlign: titleNode.componentProperties?.align?.value
      }
    };
    return columnDef;
  }
  
  const headerInstance = node.children?.find(child =>
    child.name.toLowerCase().includes('header')
  );

  if (!headerInstance?.type || headerInstance.type !== 'INSTANCE') {
    return {
      label: '',
      key: `column${columnIndex}`,
      style: {
        width: node.width + 'px',
        minWidth: node.width ? node.width + 'px' : undefined,
        maxWidth: node.width ? node.width + 'px' : undefined,
        flex: node.layoutSizingHorizontal === 'FILL' ? '1' : undefined,
        textAlign: headerInstance?.componentProperties?.align?.value
      }
    };
  }
  
  const titleNode = headerInstance.children?.find(child => child.name === 'AbcText');
  if (!titleNode?.type || titleNode.type !== 'INSTANCE') {
    return {
      label: '',
      key: `column${columnIndex}`,
      style: {
        width: node.width + 'px',
        minWidth: node.width ? node.width + 'px' : undefined,
        maxWidth: node.width ? node.width + 'px' : undefined,
        flex: node.layoutSizingHorizontal === 'FILL' ? '1' : undefined,
        textAlign: titleNode?.componentProperties?.align?.value
      }
    };
  }

  // 构造当前列的 ColumnDef
  const columnDef: ColumnDef = {
    label: titleNode.children?.[0]?.characters || `Column${columnIndex + 1}`,
    key: `column${columnIndex}`,
    style: {
      width: node.width + 'px',
      minWidth: node.minWidth ? node.minWidth + 'px' : undefined,
      maxWidth: node.maxWidth ? node.maxWidth + 'px' : undefined,
      flex: node.layoutSizingHorizontal === 'FILL' ? '1' : undefined,
      textAlign: titleNode.componentProperties?.align?.value
    }
  };

  // 查找下一层的 merged-header 节点
  const nextMergedHeader = node.children?.find(child =>
    child.name.toLowerCase().includes('merged-header')
  );

  if (nextMergedHeader) {
    // 如果有下一层，递归查找其中的 header 节点
    const childrenHeaders = nextMergedHeader.children?.filter(child =>
      child.name.toLowerCase().includes('header')
    ) || [];

    if (childrenHeaders.length) {
      // 递归处理每个子 header
      columnDef.children = childrenHeaders.map((header, index) => {
        const subColumnDef = transformMergedHeaderToColumnDef(header, index);
        return subColumnDef || {
          label: `SubColumn${index + 1}`,
          key: `subColumn${columnIndex}_${index}`,
          style: {}
        };
      });
    }
  }

  return columnDef;
}

export const tableConfig: ComponentConfig = {
  type: 'AbcTable', 
  isContainer: true,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      renderConfig: {
        hasInnerBorder: false,
        list: []
      },
      componentSlot: [],
      componentGuideline: [],
    };

    const {
      node,
      context,
    } = nodeContext;

    console.log('table.customTransformer', node);

    if (node.name !== 'AbcTable') {
      return result;
    }

    const frameNode = node as InstanceNode;
    // 先处理下 slot
    const columnGroup = frameNode.children.find(child => {
      const columnGroup = child.name.toLowerCase().includes('columngroup')
      if (columnGroup) {
        return true;
      }
      // 再尝试再看一层
      const hasColumn = child.children?.find(child => child.name.toLowerCase().includes('column'));
      if (hasColumn) {
        return true;
      }
      return false;
    });

    const topHeader = frameNode.children.find(child => child.name.toLowerCase().includes('top-header'));

    const footer = frameNode.children.find(child => child.name.toLowerCase().includes('footer'));

    const componentProperties = frameNode.componentProperties;
    if (componentProperties) {
      if (componentProperties.border?.value === true) {
        result.renderConfig.hasInnerBorder = true;
      }
      if (componentProperties.drag === true) {
        result.componentProperties.draggable = true;
      }
      if (componentProperties.pagination?.value === true) {
        result.componentGuideline.push('使用 AbcTable 的 pagination 属性，配置表格的分页');
      }
    }
    if (topHeader) {
      result.componentGuideline.push('使用 AbcTable 的 topHeader slot， 自定义表格顶部内容，节点信息参考componentSlot');
      const topHeaderNodeContext = {
        node: topHeader
      };
      const topHeaderNode = await convertNode(topHeaderNodeContext, globalContext);
      console.log('topHeaderNode', topHeaderNode);
      if (topHeaderNode) {
        result.componentSlot.push(
          {
            slotName: 'topHeader',
            figmaInfo: topHeaderNode
          }
        )
      }
    }

    // 处理下 footer
    if (footer) {
      // footer 需要判断下是否包含 AbcPagination，如果包含的话，不需要再添加 footer，其他场景才认为是自定义 footer
      const footerChildren = footer.children;
      const hasAbcPagination = footerChildren.some(child => child.name.toLowerCase().includes('pagination'));
      if (!hasAbcPagination) {
        result.componentGuideline.push('使用 AbcTable 的 footer slot， 自定义表格底部内容，节点信息参考componentSlot');
        const footerNodeContext = {
          node: footer
        };
        const footerNode = await convertNode(footerNodeContext, globalContext);
        console.log('footerNode', footerNode);
        if (footerNode) {
          result.componentSlot.push(
              {
                slotName: 'footer',
                figmaInfo: footerNode
              }
          )
        }
      } else {
        // 存在 AbcPagination，
        if (!context.componentProperties?.pagination) {
          result.componentGuideline.push('使用 AbcTable 的 pagination 属性，配置表格的分页');
        }
      }
    }

    const hasSlot = !!columnGroup;

    console.log(`table.customTransformer, hasSlot: ${hasSlot}, columnGroup: ${columnGroup?.name}, topHeader: ${topHeader?.name}, footer: ${footer?.name}`);

    let columns;

    if (!hasSlot) {
      columns = (frameNode as FigmaNode).children || [];
    } else {
      columns = (columnGroup as FigmaNode)?.children || [];
    }

    const renderConfig = transformColumns(node, columns, result);

    if (renderConfig) {
      result.componentProperties.renderConfig = renderConfig;
    }
    delete result.renderConfig;

    delete result.componentProperties.type;


    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }

    if (!result.componentSlot.length) {
      delete result.componentSlot;
    }

    console.log('abc table result', result);
    return result;
  }
};

function transformColumns(tableNode: ComponentNode, columnsNode: SceneNode[], context: any) {
  console.log('columnsNode', columnsNode)
  const renderConfig = context.renderConfig;
  const componentProperties = tableNode.componentProperties;
  const mergeTable = componentProperties?.type?.value === 'merged-cells';
  console.log('mergeTable', mergeTable)
  if (componentProperties) {
    if (componentProperties?.checkbox?.value === true) {
      context.componentGuideline.push('通过 headerConfig 中的 isCheckbox 列，配置表格的多选');
      renderConfig.list.push({
        label: ' ',
        isCheckbox: true,
        style: {
          flex: 'none',
          width: '40px',
          maxWidth: '',
          paddingLeft: '',
          paddingRight: '',
          textAlign: 'left',
        },
      });
    }
  }

  // 处理列信息
  columnsNode.forEach((column: BaseNode, columnIndex: number) => {
    if (column.type === 'FRAME') {
      console.log('column', column);
      const {
        width,
        minWidth,
        maxWidth,
        layoutSizingHorizontal,
      } = column;
      console.log('column', {
        width,
        minWidth,
        maxWidth,
        layoutSizingHorizontal,
      });
      const headerInstance = column.children?.find(child =>
          child.name.toLowerCase().includes('header')
      );

      const cellInstance = column.children?.find(child =>
          child.name.toLowerCase().includes('cell')
      );

      // 过滤掉删除列
      const isDeleteColumn = cellInstance?.children?.length === 1 && cellInstance.children[0].name.toLowerCase().includes('delete');

      if (isDeleteColumn) {
        context.componentGuideline.push('通过 AbcTable 的 supportDeleteTr 属性，配置表格支持删除');
        context.componentProperties.supportDeleteTr = true;
        return;
      }

      if (headerInstance?.type === 'INSTANCE' && !isDeleteColumn) {
        console.log('headerInstance', headerInstance)

        const mergeHeaderInstance = column.children?.find(child =>
            child.name.toLowerCase().includes('merged-header')
        );
        // 如果是合并表格，先查找 merged-header
        if (mergeTable && mergeHeaderInstance?.name === 'merged-header') {

          const mergedHeaderWrapper = column.children?.find(child =>
              child.name.toLowerCase().includes('merged-header')
          );
          console.log('mergedHeaderWrapper', mergedHeaderWrapper)
          if (mergedHeaderWrapper) {
            const columnDef = transformMergedHeaderToColumnDef(mergedHeaderWrapper, columnIndex);
            console.log('mergedcolumnDef', columnDef);
            renderConfig.list.push(columnDef);
          }
        } else {
          // 获取列标题
          const titleNode = headerInstance.children?.find(child => child.name === 'AbcText');
          let columnDef = {
            label: '',
            key: `column${columnIndex}`,
            style: {
              width: width + 'px',
              minWidth: minWidth ? minWidth + 'px' : undefined,
              maxWidth: maxWidth ? maxWidth + 'px' : undefined,
              flex: layoutSizingHorizontal === 'FILL' ? '1' : undefined,
              textAlign: headerInstance.componentProperties?.align?.value
            },
          }
          if (titleNode?.type === 'INSTANCE') {
            const label = titleNode.children?.[0]?.characters || `Column${columnIndex + 1}`;
            columnDef = {
              label,
              key: `column${columnIndex}`,
              style: {
                width: width + 'px',
                minWidth: minWidth ? minWidth + 'px' : undefined,
                maxWidth: maxWidth ? maxWidth + 'px' : undefined,
                flex: layoutSizingHorizontal === 'FILL' ? '1' : undefined,
                textAlign: headerInstance.componentProperties?.align?.value
              },
            };
          }
          renderConfig.list.push(columnDef);
        }
      }

      // 收集该列的所有数据
      const cells = column.children?.filter(child =>
          child.name.toLowerCase().includes('cell')
      ) || [];

      const hasBorder = cells.every(cell => cell?.componentProperties?.['has-border']?.value === true);
      // 判断下是否需要 hasBorder
      if (hasBorder) {
        context.renderConfig.hasInnerBorder = true;
      }
      console.log('renderConfig', renderConfig)

      // 处理每个单元格的数据
      //   cells.forEach((cell, rowIndex) => {
      //     if (cell.type === 'INSTANCE') {
      //       const textNode = cell.children?.find(child =>
      //         child.name === 'AbcText'
      //       );

      //       if (textNode?.type === 'INSTANCE') {
      //         const cellValue = textNode.children?.[0]?.characters || '';

      //         // 确保该行数据对象存在
      //         if (!result.dataList[rowIndex]) {
      //           result.dataList[rowIndex] = { key: `${rowIndex + 1}` };
      //         }

      //         // 将单元格数据添加到对应的行中
      //         result.dataList[rowIndex][`column${columnIndex}`] = cellValue;
      //       }
      //     }
      //   });
      // }
    }
  })

  return renderConfig;
}

function transformTopHeader(topHeaderNode: SceneNode) {

}