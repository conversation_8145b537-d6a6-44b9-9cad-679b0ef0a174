import { ComponentConfig } from '../types';
import {convertNode} from "@/components/process-utils";

export const BizQuickList: ComponentConfig = {
    type: 'BizQuickList',
    isContainer: false,
    async customTransformer(nodeContext: any, globalContext) {
        const result: Record<string, any> = {
            componentGuideline: [],
            componentSlot: [],
        };

        const {
            node,
        } = nodeContext;

        const tools = node.children.find((child: any) => child.name === 'ToolsWrapper')
        if(tools) {
            result.componentGuideline.push('根据截图按 BizQuickList 的usage生成tools，注意 tools 直接传给组件，BizQuickList组件内部会自己渲染 tools')
        }

        const defaultContent = node.children.find((child: any) => child.name === 'QuickContentWrapper')

        if(defaultContent) {
            const bizQuickListItem = defaultContent.children?.find((child: any) => child.name === 'BizQuickListItem');
            const qlItemNodeContext = {
                node: bizQuickListItem,
            }
            const prependNodeResult = await convertNode(qlItemNodeContext, globalContext);

            result.componentSlot.push({
                name: 'default',
                content: prependNodeResult
            });
        }


        return result;
    },
};
