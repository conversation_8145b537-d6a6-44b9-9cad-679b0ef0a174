import {ComponentConfig} from '../types';
import {convertNode} from "@/components/process-utils";

export const layoutConfig: ComponentConfig = {
    type: 'AbcLayout',
    isContainer: true,
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {
            componentGuideline: [],
            children: [],
        };

        const {
            context,
            node
        } = nodeContext;

        const {componentProperties} = context!;

        if (componentProperties) {
            delete componentProperties.type;

            const nodeList = node?.children || [];

            for (let i = 0; i < nodeList.length; i++) {
                const node = nodeList[i];

                if (node.name === 'AbcLayoutHeader') {
                    const headerContext = {
                        node: node,
                    }
                    const headerResult = await convertNode(headerContext, globalContext);
                    result.children.push(headerResult);
                } else if (node.name === 'AbcLayoutContent') {
                    const contentContext = {
                        node: node,
                    }
                    const contentResult = await convertNode(contentContext, globalContext);
                    result.children.push(contentResult);
                } else if (node.name === 'AbcLayoutFooter') {
                    const footerContext = {
                        node: node,
                    }
                    const footerResult = await convertNode(footerContext, globalContext);
                    result.children.push(footerResult);
                } else if (node.name === 'AbcLayoutSidebar') {
                    const sidebarContext = {
                        node: node,
                    }
                    const sidebarResult = await convertNode(sidebarContext, globalContext);
                    result.children.push(sidebarResult);
                    componentProperties.hasSidebar = true
                }
            }

            result.componentGuideline.push(
                `AbcLayout组件是容器组件，子项只能是AbcLayoutHeader、AbcLayoutContent、AbcLayoutFooter、AbcLayoutSidebar组件，允许嵌套 AbcLayout 组件使用，AbcLayout开头的组件都属于基础组件不用注册在vue sfc 文件的components属性中`
            );

            if (componentProperties.preset === 'page-table') {
                result.componentGuideline.push(
                    `通过设置 abc-layout 的 preset="page-table" 实现页面表格布局，特别注意：
                    1.如果存在顶部筛选栏，放到 abc-layout-header 中，使用 abc-flex 两端对齐，并分别用 abc-space 控制元素间距
                    2.监听 abc-layout-content 的 layout-mounted 事件，在该事件中确定分页条数并触发数据获取
                    3.如果存在分页，abc-pagination 单独放到 abc-layout-footer 中，可通过 ul > li > span 的结构写分页信息，自带样式`
                );
            }
        }

        return result;
    },
};
