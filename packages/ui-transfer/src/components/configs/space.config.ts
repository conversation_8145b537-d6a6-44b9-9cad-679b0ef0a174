import { convertToSpace } from '../process-utils';
import { ComponentConfig, NodeContext } from '../types';

export const spaceConfig: ComponentConfig = {
  type: 'AbcSpace',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      style: undefined,
      componentGuideline: [],
    };

    const {
      node,
    } = nodeContext;

    const spaceInfo = convertToSpace(node as FrameNode);

    result.componentProperties = {
      ...spaceInfo
    }

    if (!Object.keys(result.componentProperties).length) {
      delete result.componentProperties;
    }

    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }

    return result;
  },
};
