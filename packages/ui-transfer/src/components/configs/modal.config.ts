import { convertNode } from '../process-utils';
import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const modalConfig: ComponentConfig = {
  type: 'AbcModal',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentSlot: [],
      componentGuideline: []
    };
    const { context } = nodeContext;
    
    const componentProperties = context?.componentProperties;
    const node = nodeContext.node;
    const frameNode = node as InstanceNode;
    
    console.log('modalConfig customTransformer', { context, nodeContext });
    
    if (componentProperties) {
      // 处理 size 属性
      if (componentProperties.size) {
        const validSizes = ['small','default', 'medium', 'large','xlarge', 'huge', 'hugely'];
        if (!validSizes.includes(componentProperties.size)) {
          componentProperties.size = 'default';
        }
      }

      
      // 处理布尔值属性
      const booleanProps = ['showIcon', 'closeAfterConfirm', 'needHighLevel', 'showConfirm', 'showCancel', 'showClose', 'responsive'];
      booleanProps.forEach(prop => {
        if (componentProperties[prop] !== undefined) {
          componentProperties[prop] = componentProperties[prop] === true;
        }
      });


      // 处理 preset 属性
      if (componentProperties.preset) {
        const validPresets = ['confirm', 'alert', 'message', 'step', 'custom'];
        if (!validPresets.includes(componentProperties.preset)) {
          componentProperties.preset = 'custom';
        }
        if(componentProperties.preset === 'custom') {
          componentProperties.showFooter = false;
        }
      }
    
      
      
    console.log('modal componentProperties', componentProperties)
      // 处理 slot

      const topExtendNode = frameNode.children.find(child => child.name.toLowerCase() === 'top-extend');

      // 处理 topExtend 插槽
      if (componentProperties.topExtend === true && topExtendNode) {
        const topExtendNodeContext: NodeContext = {
          node: topExtendNode as SceneNode,
        };
        const topExtendSlotInfo = await convertNode(topExtendNodeContext, globalContext);
        if (topExtendSlotInfo) {
          result.componentSlot.push({
            slotName: 'topExtend',
            figmaInfo: topExtendSlotInfo
          });
        }
      }

      const defaultNode = frameNode.children.find(child => child.name.toLowerCase() === 'content');
      console.log('defaultNode', defaultNode)
      // 处理 default 插槽（如果有专门的 default 节点）
      if (defaultNode) {
        const defaultNodeContext: NodeContext = {
          node: defaultNode,
        };
        
        const defaultSlotInfo = await convertNode(defaultNodeContext, globalContext);
        if (defaultSlotInfo) {
          result.componentSlot.push({
            slotName: 'default',
            content: defaultSlotInfo
          });
        }
      }
    }
    delete componentProperties?.topExtend;
    
    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }
    
    return result;
  }
};
