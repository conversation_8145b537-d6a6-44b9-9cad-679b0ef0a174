import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const noticeConfig: ComponentConfig = {
  type: 'AbcNotice',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentGuideline: [],
    };

    const { node, context } = nodeContext;
    console.log('noticeConfig customTransformer', { node, context });

    const { componentProperties } = context;
    if (componentProperties) {
      // 处理属性
      delete componentProperties['type'];
      
      // 处理特定属性的默认值
      if (componentProperties.theme && !['info', 'success', 'warning'].includes(componentProperties.theme)) {
        componentProperties.theme = 'success';
      }
      
      if (componentProperties.variant && !['outline', 'fill'].includes(componentProperties.variant)) {
        componentProperties.variant = 'fill';
      }
      
      // 处理布尔值属性
      if (componentProperties.withNoticeIcon === false) {
        componentProperties.withNoticeIcon = false;
      } else{
        componentProperties.withNoticeIcon = true;
      }
      
      if (componentProperties.withDeleteIcon === false) {
        componentProperties.withDeleteIcon = false;
      } else{
        componentProperties.withDeleteIcon = true;
      }
      result.componentGuideline.push(`可以在AbcNotice中使用默认插槽添加内容，插入${componentProperties?.content}`);
    }
    
    return result;
  }
};
