import {ComponentConfig} from '../types';
import {convertNode} from "@/components/process-utils";

export const rowConfig: ComponentConfig = {
    type: 'AbcRow',
    isContainer: true,
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {
            componentGuideline: [],
            componentSlot: [],
            children: []
        };

        const {
            context,
            node,
        } = nodeContext;

        result.componentGuideline.push('根据截图分析需要的信息，尤其注意设置到 abc-row上的属性(如：gutter)和 abc-col的属性(如:span)，使用 abc-row 搭配 abc-col 使用，详细用法参考AbcGrid的usage');


        const {componentProperties} = context!;

        if (componentProperties) {

            const colNodes = node.children?.forEach(child => child.name === 'AbcCol') ?? [];

            for (let i = 0; i < colNodes.length; i++) {
                const colNode = colNodes[i];

                const colContext = {
                    node: colNode,
                }
                const colResult = await convertNode(colContext, globalContext);

                result.children.push(colResult);
            }
        }
    }
};
