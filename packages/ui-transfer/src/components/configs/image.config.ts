import { ComponentConfig } from '../types';

export const imageConfig: ComponentConfig = {
  type: 'AbcImage',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('imageConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentGuideline: [],
    };
    const {
      context,
      node
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (node.width) {
        componentProperties.width = node.width;
      }
      if (node.height) {
        componentProperties.height = node.height;
      }
      delete componentProperties.size;
      delete componentProperties.states;
    }
    result.componentGuideline.push('AbcImage组件，用于展示图片内容,默认不必生成src的内容，也不需要插入slot为error的内容，fit和ossStyleName属性要传入才传入，不传入不需要设置');
    return result;
  }
};
