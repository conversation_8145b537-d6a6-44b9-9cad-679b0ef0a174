import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const addressSelectorConfig: ComponentConfig = {
  type: 'AbcAddressSelector',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentSlot: <ComponentSlot[]>[],
    };
    const { context, node} = nodeContext;
    const { componentProperties } = context;

    if (componentProperties) {
      // 宽度设置
      if (componentProperties?.['width'] || node.width) {
        componentProperties.width = Number(componentProperties.width || node.width);
      }

      // size 处理
      if (componentProperties?.['size']) {
        const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
        if (!validSizes.includes(componentProperties['size'])) {
          delete componentProperties['size']; 
        }
      }

      // clearable
      if (componentProperties?.['clearable'] === true) {
        componentProperties.clearable = true;
      } else {
        componentProperties.clearable = false;
      }

      // disabled
      if (componentProperties?.['disabled'] === true) {
        componentProperties.disabled = true;
      } else {
        componentProperties.disabled = false;
      }

      // 删除 states 属性
      if (componentProperties?.['states'] === 'disable') {
        componentProperties.disabled = true;
      }
      delete componentProperties?.['states'];
      
      console.log('address-selector componentProperties',  componentProperties);
    }
    return result;
  }
};
