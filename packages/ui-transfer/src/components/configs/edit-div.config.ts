import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const editDivConfig: ComponentConfig = {
  type: 'AbcEditDiv',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {};
    const { context, node } = nodeContext;
    console.log('editDivConfig customTransformer', { nodeContext, globalContext });

    // 确保 context 存在
    if (!context || !context.componentProperties) {
      return {};
    }

    // 直接使用 context 中的 componentProperties
    const { componentProperties } = context;


    // 处理 size 属性，默认为 tiny
   // size 处理
   if (componentProperties?.['size']) {
    const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
    if (!validSizes.includes(componentProperties['size'])) {
      delete componentProperties['size'];
    }
  }

    // 处理 states 属性
    if (componentProperties['states'] === 'disabled') {
      componentProperties.disabled = true;
    } 
    if (componentProperties['states'] === 'readonly') {
      componentProperties.readonly = true;
    }
    // 处理 responsive 属性
    if(componentProperties['responsive'] === false) {
      componentProperties.responsive = false;
    } else {
      componentProperties.responsive = true;
    }
    componentProperties.autoHeight = true;
    // 删除不需要的属性
    delete componentProperties['states'];

    return result;
  }
};
