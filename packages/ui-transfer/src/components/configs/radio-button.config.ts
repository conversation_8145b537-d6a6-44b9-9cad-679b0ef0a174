import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const radioButtonConfig: ComponentConfig = {
  type: 'AbcRadioButton',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const { context, node } = nodeContext;

      const result: Record<string, any> = {
          componentSlot: [],
      };

    if (!context || !context.componentProperties) {
      return {};
    }
    
    const { componentProperties } = context;

    // 宽度设置
    if (componentProperties?.['width'] || node.width) {
      componentProperties.width = Number(componentProperties.width || node.width);
    }

    // variant 处理 - card 或者 default
    if (componentProperties?.['variant']) {
      if (componentProperties['variant'] !== 'card' ) {
          delete componentProperties['variant'];
      }
    }

    // checkIcon 处理 - 是否显示选中icon
    if (componentProperties?.['checkIcon'] === true) {
      componentProperties.checkIcon = true;
    } else {
      componentProperties.checkIcon = false;
    }

    // theme 处理 - gray 或者 default
    if (componentProperties?.['theme']) {
      if (componentProperties['theme'] !== 'gray') {
          delete componentProperties['theme'];
      }
    }

    // size 处理
    if (componentProperties?.['size']) {
      const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
      if (!validSizes.includes(componentProperties['size'])) {
        delete componentProperties['size'];
      }
    }

    const textSlot = node.children[0];

    if(textSlot) {
        const textStr = textSlot.characters;
        result.componentSlot.push({
            name: 'default',
            content:textStr,
        })
        componentProperties.label = textStr;
    }
    return result;
  }
};
