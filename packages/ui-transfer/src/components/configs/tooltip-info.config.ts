import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const tooltipInfoConfig: ComponentConfig = {
  type: 'AbcTooltipInfo',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentGuideline: [],
    };

    const { node, context } = nodeContext;
    console.log('tooltipInfoConfig customTransformer', { node, context });

    const { componentProperties } = context;
    if (componentProperties) {
      // 处理placement属性
      const validPlacements = ['top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 
                              'bottom-start', 'bottom', 'bottom-end', 'left-start', 'left', 'left-end'];
      if (componentProperties.placement && !validPlacements.includes(componentProperties.placement)) {
        delete componentProperties.placement;
      }
      delete componentProperties?.reference;
      // content属性
      if (!componentProperties.content) {
        result.componentGuideline.push('当有自定义样式时，可以使用默认插槽');
      }
    }
    
    return result;
  }
};
