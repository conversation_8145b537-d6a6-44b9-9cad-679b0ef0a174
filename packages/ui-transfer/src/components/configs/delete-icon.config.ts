import { ComponentConfig } from '../types';

export const deleteIconConfig: ComponentConfig = {
  type: 'AbcDeleteIcon',
  isContainer: false,
  customTransformer(nodeContext, globalContext) {
    const result: Record<string, any> = {};
    const {
      context,
    } = nodeContext;
    if (context.componentProperties) {
      delete context.componentProperties.states;
    }
    return result;
  },
};
