import { ComponentConfig } from '../types';

export const buttonPaginationConfig: ComponentConfig = {
  type: 'AbcButtonPagination',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('buttonPaginationConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
    };
    const {
      context,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (componentProperties.size === 'normal') {
        delete componentProperties.size;
      }
      delete componentProperties.states;
    }
    return result;
  }
};
