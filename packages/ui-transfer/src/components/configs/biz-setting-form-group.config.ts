import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {convertNode, extractTextContent} from "@/components/process-utils";

export const BizSettingFormGroup: ComponentConfig = {
    type: 'BizSettingFormGroup',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            children: [],
        };

        const {
            context,
            node
        } = nodeContext;

        const {
            componentProperties
        } = context;

        const nodeList = node?.children || [];

        if (componentProperties) {
            const titleNode = nodeList.find((node: any) => node.name === 'title')?.children?.[0];
            if (titleNode) {
                componentProperties.title = extractTextContent(titleNode);
            }
            delete componentProperties['divider'];
        }

        for (let i = 0; i < nodeList.length; i++) {
            const node = nodeList[i];
            if(node.name.toLowerCase() !== 'bizsettingformitem') {
                continue;
            }
            const nodeContext = {
                node: node,
            };
            const childResult = await convertNode(nodeContext, globalContext);
            if (childResult) {
                result.children.push(childResult);
            }
        }
        return result
    }
};