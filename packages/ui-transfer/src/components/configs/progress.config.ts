import { ComponentConfig } from '../types';

export const progressConfig: ComponentConfig = {
  type: 'AbcProgress',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('progressConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {};
    const {context, node} = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (componentProperties.theme === 'blue') {
        delete componentProperties.theme;
      }
    }
    const {
      children
    } = node;
    // 处理 text
    if (children?.length) {
      const textNode = children.find(child => child.type === 'TEXT');
      if (textNode) {
        const content = textNode.characters;
        // 处理 text，看有没有 circleTotal
        const textList = content?.split('/');
        const circleTotal = Number(textList[1] || 100);
        if (circleTotal !== 100) {
          componentProperties.circleTotal = circleTotal;
          componentProperties.percentage = Number(textList[0] || 0);
        } else {
          delete componentProperties.circleTotal;
        }
      }
    }
    return result;
  }
};
