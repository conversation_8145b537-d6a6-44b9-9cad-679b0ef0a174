import { ComponentConfig } from '../types';

export const dropdownItemConfig: ComponentConfig = {
  type: 'AbcDropdownItem',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
    };
    
    const { node, context } = nodeContext;
    
    const text = context.componentProperties.text;
    const icon = context.componentProperties.icon;
    const icName = context.componentProperties['ic-name'];
    
    delete context.componentProperties.states;
    delete context.componentProperties.text;
    delete context.componentProperties.icon;
    delete context.componentProperties['ic-name'];
    delete context.componentProperties.size;
    
    result.componentProperties.label = text;
    result.componentProperties.disabled = context.componentProperties.disabled === true;
    
    if (icon) {
        result.componentGuideline.push(`通过给 AbcDropdownItem 设置default 插槽:  AbcIcon 的 icon 为 ${icName}, AbcText 的内容 为 ${text}`);
    }


    return result;
  }
};
