import {ComponentConfig, ComponentSlot, GlobalContext, NodeContext} from '../types';

export const BizTagSelector: ComponentConfig = {
  type: 'BizTagSelector',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentGuideline: [],
    }
    result.componentGuideline.push('请注意生成BizTagSelector的时候除了图标插槽，插槽内不需要生成其他内容');
    return result;
  },
};
