import { ComponentConfig } from '../types';

export const inputNumberConfig: ComponentConfig = {
  type: 'AbcInputNumber',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentSlot: <ComponentSlot[]>[],
    };
    const { context, node } = nodeContext;
    const { componentProperties } = context;

    if (componentProperties) {
      // 宽度设置
      if (componentProperties?.['width'] || node.width) {
        componentProperties.width = Number(componentProperties.width || node.width);
      }

      // size 处理
      if (componentProperties?.['size']) {
        const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
        if (!validSizes.includes(componentProperties['size'])) {
          delete componentProperties['size'];
        }
      }

      // placeholder 处理
      if (!componentProperties?.['placeholder']) {
        delete componentProperties?.['placeholder'];
      }

      // button-placement 处理
      if (componentProperties?.['button-placement']) {
        const validPlacements = ['left', 'top'];
        if (validPlacements.includes(componentProperties['button-placement'])) {
          componentProperties.buttonPlacement = componentProperties['button-placement'];
        } else {
          componentProperties.buttonPlacement = 'top'; // 默认值
        }
      }

      // disable 处理
      if (componentProperties?.['disable'] === true) {
        componentProperties.disabled = true;
      } else {
        componentProperties.disabled = false;
      }
      delete componentProperties['inputSize'];
      delete componentProperties['ReduceBtn'];
      delete componentProperties['AddBtn'];
      delete componentProperties['button-placement'];
    }
    return result;
  }
};
