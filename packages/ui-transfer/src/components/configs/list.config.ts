import {convertNode, extractTextContent, isSingleSubIcon} from '../process-utils';
import {ComponentConfig} from '../types';
import {isTruthyForFigma} from "@/utils/utils";

export const listConfig: ComponentConfig = {
    type: 'AbcList',
    isContainer: false,
    customChildren: {
        'AbcListItem': {
            type: 'AbcListItem',
            nodeWhitelist: ['prepend', 'append', 'default', 'centerSlot', 'title', 'content']
        },
    },
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {
            componentGuideline: [],
            componentSlot: [],
        };

        const {
            context,
            node,
        } = nodeContext;

        const {componentProperties} = context!;

        if (componentProperties) {
            const dataList = [];
            // 下面两个同级节点
            const listItemNodes = [];
            const dividerNodes = [];

            if (isTruthyForFigma(componentProperties.isFixedHeight)) {
                componentProperties.height = node.height
            }

            componentProperties.noHoverBorder = !isTruthyForFigma(componentProperties.hoverBorder);
            componentProperties.noTopBorder = !isTruthyForFigma(componentProperties.topBorder);
            componentProperties.noCloseBorder = !isTruthyForFigma(componentProperties.closeBorder);

            node.children?.forEach(child => {
                if (child.name === 'AbcListItem') {
                    listItemNodes.push(child);
                }
                if (child.name === 'AbcDivider') {
                    dividerNodes.push(child);
                }
            });


            if (dividerNodes?.length) {
                const dividerContext = {
                    node: dividerNodes[0],
                }
                const res = await convertNode(dividerContext, globalContext);
                componentProperties.dividerConfig = res?.componentProperties ?? {};
            }

            for (let i = 0; i < listItemNodes.length; i++) {
                const dataListItem: Record<string, any> = {
                    id: i + 1,
                    title: '',
                    content: '',
                    checked: false,
                }
                const listItemNode = listItemNodes[i];
                const listItemContext = {
                    node: listItemNode,
                    customChildrenProcess: true
                }
                // 大部分属性是在 item 上的所以需要提出来
                const listItemResult = await convertNode(listItemContext, globalContext);

                console.log('_listItemResult', listItemResult);
                componentProperties.needSelected = isTruthyForFigma(listItemResult?.componentProperties?.needSelected);
                componentProperties.showIcon = isTruthyForFigma(listItemResult?.componentProperties?.showIcon);
                componentProperties.readonly = isTruthyForFigma(listItemResult?.componentProperties?.readonly);
                if (listItemResult?.componentProperties?.size) {
                    componentProperties.size = listItemResult?.componentProperties?.size;
                }

                // 支持数据默认带选中状态
                if (listItemResult?.componentProperties?.states === 'checked' || isTruthyForFigma(listItemResult?.componentProperties?.checked)) {
                    dataListItem.checked = true;
                }
                //  带默认样式的title
                const titleNode = listItemNode?.children?.find(child => child.name === 'title');
                if (titleNode) {
                    dataListItem.title = extractTextContent(titleNode.children?.[0]);
                }
                // prepend 插槽处理
                if (isTruthyForFigma(listItemResult?.componentProperties?.prepend)) {
                    // 从 listItemResult 中提取 abc-icon 的图标值
                    const prependNode = listItemNode.children?.find(child => child.name === 'prepend');
                    console.log('prependNode', prependNode)
                    // 只有一个 icon
                    if (isSingleSubIcon(prependNode)) {
                        result.componentSlot.push({
                            name: 'prepend',
                            content: {
                                type: 'AbcIcon',
                                name: prependNode.children[0].name,
                            }
                        })
                    } else {
                        // 支持复杂的前插槽内容
                        const prependNodeContext = {
                            node: prependNode,
                        }
                        const prependNodeResult = await convertNode(prependNodeContext, globalContext);

                        result.componentSlot.push({
                            name: 'prepend',
                            content: prependNodeResult
                        });

                    }
                }

                // append 插槽处理
                if (isTruthyForFigma(listItemResult?.componentProperties?.append)) {
                    // 从 listItemResult 中提取 abc-icon 的图标值
                    const appendNode = listItemNode.children?.find(child => child.name === 'append');

                    // 只有一个 icon
                    if (isSingleSubIcon(appendNode)) {
                        result.componentSlot.push({
                            name: 'append',
                            content: {
                                type: 'AbcIcon',
                                name: appendNode.children[0].name,
                            }
                        })
                    } else {
                        // 支持复杂的前插槽内容
                        const appendNodeContext = {
                            node: appendNode,
                        }
                        const appendNodeResult = await convertNode(appendNodeContext, globalContext);

                        result.componentSlot.push({
                            name: 'append',
                            content: appendNodeResult
                        });

                    }
                }

                // default 插槽处理(centerSlot)
                if (isTruthyForFigma(listItemResult?.componentProperties?.centerSlot)) {
                    // 从 listItemResult 中提取 abc-icon 的图标值
                    const centerSlotNode = listItemNode.children?.find(child => child.name === 'centerSlot');
                    const centerSlotNodeContext = {
                        node: centerSlotNode,
                    }
                    if (centerSlotNode?.children?.length === 1 && centerSlotNode.children?.[0]?.name === 'title') {
                        //  带默认样式的title
                        const titleNode = centerSlotNode.children?.[0];
                        if (titleNode) {
                            dataListItem.title = extractTextContent(titleNode.children?.[0]);
                        }
                    } else if (centerSlotNode?.children?.length === 2 && centerSlotNode.children[0].name === 'title' && centerSlotNode.children[1].name === 'content') {
                        //  带默认样式的title
                        const titleNode = centerSlotNode.children?.[0];
                        if (titleNode) {
                            dataListItem.title = extractTextContent(titleNode.children?.[0]);
                        }
                        //  带默认样式的content
                        const contentNode = centerSlotNode.children?.[1];
                        if (contentNode) {
                            dataListItem.content = extractTextContent(titleNode.children?.[0]);
                        }
                    } else {
                        const centerSlotNodeResult = await convertNode(centerSlotNodeContext, globalContext);
                        result.componentSlot.push({
                            name: 'default',
                            content: centerSlotNodeResult
                        });

                        result.componentGuideline.push('分析截图，提取数据，丰富到现有的dataList中，详细用法参考AbcList的usage');
                    }
                } else {
                    if (dataListItem.title) {
                        result.componentGuideline.push('当前组件无自定义渲染要求，可不使用 default 默认插槽渲染 title，详细用法参考AbcList的usage');
                    }
                }

                // 构造渲染数据
                dataList.push(dataListItem);
            }

            if (!dataList.length) {
                result.componentGuideline.push('分析截图，生成 dataDist 指定列表数据，提供了 prepend，append，default 插槽自定义列表项，详细用法参考AbcList的usage');
            } else {
                // 数据源
                componentProperties.dataList = dataList;
            }
        }

        return result;
    },
};
