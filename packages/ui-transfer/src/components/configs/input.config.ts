import { convertNode } from '../process-utils';
import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

export const inputConfig: ComponentConfig = {
  type: 'AbcInput',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    const {context, node} = nodeContext;
    console.log('inputConfig customTransformer', {nodeContext, globalContext});

    const {
      componentProperties,
    } = context;

    if (componentProperties) {

      // 宽度设置
      if (componentProperties?.['width'] || node.width) {
        componentProperties.width = Number(componentProperties.width || node.width);
      }

      // size 处理
      if (componentProperties?.['size']) {
        const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
        if (!validSizes.includes(componentProperties['size'])) {
          delete componentProperties['size'];
        } 
      }

      // 处理布尔值属性
      // clearable
      if (componentProperties?.['clearable'] === false) {
        componentProperties.clearable = false;
      } 

      // disabled
      if (componentProperties?.['disabled'] === true) {
        componentProperties.disabled = true;
      } else {
        componentProperties.disabled = false;
      }

      // readonly
      if (componentProperties?.['readonly'] === true) {
        componentProperties.readonly = true;
      } {
          delete componentProperties.readonly;
      }

      // onlyBottomBorder
      if (componentProperties?.['onlyBottomBorder'] === true) {
        componentProperties.onlyBottomBorder = true;
      } else {
          delete  componentProperties.onlyBottomBorder;
      }

      // loading
      if (componentProperties?.['loading'] === true) {
        componentProperties.loading = true;
      } {
        delete componentProperties.loading
      }

      console.log('prependprependprepend', componentProperties)

      // loadingPosition
      if (componentProperties.loading) {
        if (componentProperties?.['loadingPosition-left'] === true) {
          componentProperties.loadingPosition = 'left';
        }
        if (componentProperties?.['loadingPosition-right'] === true) {
          componentProperties.loadingPosition = 'right';
        }
      }

      if (componentProperties?.['states'] === 'disable') {
        componentProperties.disabled = true;
      }


      const targetInput = node.children[0];
      if(targetInput && targetInput.name === 'AbcInput') {
      
        
        // 遍历 targetInput.children 检测是否存在特定节点
        if ('children' in targetInput && targetInput.children?.length) {
          
          // 使用 for...of 循环支持异步操作
          for (const child of targetInput.children) {
            if (child.name) {
              const nodeName = child.name.toLowerCase();
              
              // 检查是否是 prepend 节点
              if (nodeName === 'prepend' && componentProperties?.['prepend'] === true) {
                result.componentGuideline.push('使用 AbcInput 的 prepend slot');
                if(child.children[0]) {
                  const prependNodeContext = {
                    node: child.children[0]
                  }
                  const prependNode = await convertNode(prependNodeContext, globalContext);
                  result.componentSlot.push({
                    name: 'prepend',
                    content: prependNode,
                  });
                }
              }
              
              // 检查是否是 append 节点
              if (nodeName === 'append' && componentProperties?.['append'] === true) {
                result.componentGuideline.push('使用 AbcInput 的 append slot');
                if(child.children[0]) {
                  const appendNodeContext = {
                    node: child.children[0]
                  }
                  const appendNode = await convertNode(appendNodeContext, globalContext);
                  result.componentSlot.push({
                    name: 'append',
                    content: appendNode,
                  });
                }
              }
              
              // 检查是否是 appendInner 节点
              if (nodeName === 'appendinner' && componentProperties?.['appendinner'] === true) {
                result.componentGuideline.push('使用 AbcInput 的 appendInner slot');
                if(child.children[0]) {
                  const appendInnerNodeContext = {
                    node: child.children[0]
                  }
                  const appendInnerNode = await convertNode(appendInnerNodeContext, globalContext);
                  result.componentSlot.push({
                    name: 'appendInner',
                    content: appendInnerNode,
                  });
                }
              }
            }
          }
        } 
      } 
      
      
      const appendLabel = node.children[1];
      if(appendLabel) {
        if (appendLabel.name?.toLowerCase() === 'appendlabel' && componentProperties?.['appendLabel'] === true) {
          if(appendLabel.children[0]) {
            result.componentGuideline.push('使用 AbcInput 的 appenLabel slot');
            const appendLabelNodeContext = {
              node: appendLabel.children[0]
            };
            const appendLabelNode = await convertNode(appendLabelNodeContext, globalContext);
            result.componentSlot.push({
              name: 'appendLabel',
              content: appendLabelNode,
            });
          }
        }
      }
    }
    
    
    delete componentProperties?.['states'];
    delete componentProperties?.['appendinner'];
    delete componentProperties?.['loadingPosition-right'];
    delete componentProperties?.['loadingPosition-left'];
    delete componentProperties?.['prepend'];
    delete componentProperties?.['appendLabel'];


    // 删除所有包含 appendinner-icon 和 prepend-icon 的属性
    if (componentProperties) {
      Object.keys(componentProperties).forEach(key => {
        if (key.includes('appendinner-icon') || key.includes('prepend-icon')) {
          delete componentProperties[key];
        }
      });
    }

    if (!result.componentGuideline.length) {
      delete result.componentGuideline;
    }

    if (!result.componentSlot.length) {
      delete result.componentSlot;
    }
    
    return result;
  }
};
