import {ComponentConfig, GlobalContext, NodeContext} from '../types';
import {figmaGradientToCSS} from "@/utils/utils";

export const cardConfig: ComponentConfig = {
  type: 'AbcCard',
  isContainer: true,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };

    console.log('nodeContext', nodeContext)
    console.log('globalContext', globalContext)
    const {
      node,
      context: {
        componentProperties
      }
    } = nodeContext;

    if (node.name !== 'AbcCard') {
      return result;
    }
    const frameNode = node as InstanceNode;

    if (componentProperties?.background === 'custom') {
      componentProperties.style = figmaGradientToCSS(frameNode.backgrounds[0]);
    }

    componentProperties.style = `${componentProperties.style || ''}width: 100%;`

    return result;
  }
};
