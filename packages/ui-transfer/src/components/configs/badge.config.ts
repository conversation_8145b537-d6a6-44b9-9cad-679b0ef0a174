import { ComponentConfig } from '../types';

export const badgeConfig: ComponentConfig = {
  type: 'AbcBadge',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('badgeConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {};
    const {
      context,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      if (componentProperties.variant === 'text') {
        delete componentProperties.variant;
      }
      // 转代码兼容 此处属性需要UI对齐开发 TODO
      if (componentProperties.theme === 'warning') {
        componentProperties.theme = 'warning';
      }
      delete componentProperties.states;
    }
    return result;
  }
};
