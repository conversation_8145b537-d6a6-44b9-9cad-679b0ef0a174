import { ComponentConfig } from '../types';

export const transferConfig: ComponentConfig = {
  type: 'AbcTransferV2',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };

    const { node, context } = nodeContext;
    const { componentProperties } = context;
    console.log(componentProperties)
    
    result.componentGuideline.push('需要给 AbcTransferV2 props nodeKey (唯一标识)');
    result.componentGuideline.push('请结合 AbcTransferV2 props 的 props 和 nodeKey 组装一个 data 赋值给 AbcTransferV2 的 props');
    
    if (!componentProperties.isTabTransfer) {
      delete componentProperties.isTabTransfer;
    } else {
      result.componentGuideline.push('需要组装一个AbcTabsV2 的 options 赋值给 AbcTransferV2 props 的 transferTabOptions， transferTabOptions 默认两个即可');
      result.componentGuideline.push('需要给 AbcTransferV2 props 的 transferCurrentTab 需要配合 transferTabOptions 通过 .sync 修饰符绑定');
      result.componentGuideline.push('需要给每一个 transferTabOption 需要加一个data, 就不需要AbcTransferV2 props 的 data');
    }
    
    if (!componentProperties.showSearch) {
      delete componentProperties.showSearch;
    } 
    
    if (componentProperties.size === 'default') {
      delete componentProperties.size;
    }
    
    delete componentProperties.type;
    
    const resultWrapper = node.children.find(child => child.name === "resultWrapper")
    if (resultWrapper && resultWrapper.width !== 320) {
      result.componentProperties.resultWidth = resultWrapper.width;
    }
    
    result.componentGuideline.push('只针对提供的 componentProperties 进行处理');
    
    return result;
  }
};
