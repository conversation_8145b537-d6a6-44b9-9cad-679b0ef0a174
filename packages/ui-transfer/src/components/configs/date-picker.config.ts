import { convertNode } from '../process-utils';
import { ComponentConfig, ComponentSlot, GlobalContext, NodeContext } from '../types';

// 类型守卫函数，检查节点是否有 children 属性
function hasChildren(node: any): node is { children: any[] } {
  return node && 'children' in node && Array.isArray(node.children);
}

export const datePickerConfig: ComponentConfig = {
  type: 'AbcDatePicker',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentSlot: <ComponentSlot[]>[],
      componentGuideline: [],
    };
    const {
      context,
      node
    } = nodeContext;
    /**
     * 使用可选的组件属性
     */
    const componentProperties = context?.componentProperties;
    
    console.log('datePickerConfig customTransformer', { context, nodeContext });
    if (componentProperties) {
      // 处理 type 属性
      const type = componentProperties.type;
      if(type === 'MonthDayRange') {
        componentProperties.type = 'monthDayRange';
      }
      if(type === 'YearMonthDay') {
        delete componentProperties.type;
      }
      if(type === 'YearMonthDayRange') {
        componentProperties.type = 'daterange';
      }
      if(type === 'DateQuickPicker') {
        componentProperties.type = 'datequick';
      }
      
      // 宽度设置
      if (componentProperties?.['width'] || node.width) {
        componentProperties.width = Number(componentProperties.width || node.width);
      }
      
      // 处理 size 属性
      if (componentProperties.size) {
        const validSizes = ['tiny', 'small', 'medium', 'large'];
        if( !validSizes.includes(componentProperties.size) || componentProperties.size  === 'default'){
          componentProperties.size = '';
        }
      }
      
      // 处理 onlyBottomBorder 属性
      if (componentProperties.onlyBottomBorder === true) {
        componentProperties.onlyBottomBorder = true;
      } else {
        componentProperties.onlyBottomBorder = false;
      }
      
      if(componentProperties.states === 'disable') {
        componentProperties.disabled = true;
      }
      delete componentProperties.states;
      
      // 检查 children 中是否存在名为 reference 的节点
      if (hasChildren(node)) {
        for (const child of node.children) {
          if (child.name && child.name.toLowerCase() === 'reference') {
            result.componentGuideline.push('使用 AbcDatePicker 的默认插槽');
            if (hasChildren(child) && child.children.length > 0) {
              const referenceNodeContext = {
                node: child.children[0]
              };
              const referenceNode = await convertNode(referenceNodeContext, globalContext);
              result.componentSlot.push({
                name: 'default',
                content: referenceNode,
              });
            }
          }
        }
      }
    }
    
    // 如果没有指南，删除该属性
    if (!(result.componentGuideline as string[]).length) {
      delete result.componentGuideline;
    }
    
    // 如果没有插槽，删除该属性
    if (!(result.componentSlot as ComponentSlot[]).length) {
      delete result.componentSlot;
    }
    
    return result;
  }
};
