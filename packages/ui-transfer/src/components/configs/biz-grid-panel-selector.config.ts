import { ComponentConfig, NodeContext, GlobalContext } from '../types';

export const BizGridPanelSelector: ComponentConfig = {
  type: 'BizGridPanelSelector',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
    };

    const {
      context,
      node,
    } = nodeContext;

    const {
      componentProperties
    } = context;

    if (componentProperties) {
      // 删除不需要的属性
      delete componentProperties.states;
      delete componentProperties.Panel;

      result.componentProperties = {
        ...componentProperties
      }
      // 提取 abc-select-input 组件的属性
      const abcSelectInputNode = node.children?.find((item: any)=>item.name==="AbcSelectInput");
      if(abcSelectInputNode){
        if (abcSelectInputNode.width) {
          result.componentProperties.width = abcSelectInputNode.width;
        }
        // 提取 abc-grid-selector-panel 组件的属性
        const abcGridSelectorPanelNode = abcSelectInputNode.children?.find((item: any)=>item.name==="GridSelectorPanel");
        if(abcGridSelectorPanelNode){
          // 处理面板宽度
          if (abcGridSelectorPanelNode.width) {
            result.componentProperties.panelWidth = abcGridSelectorPanelNode.width;
          }
          // 处理面板列数
          if (abcGridSelectorPanelNode.componentProperties?.columns?.value) {
            result.componentProperties.columns = abcGridSelectorPanelNode.componentProperties.columns.value;
          }
        }
      }
    }

    return result;
  }
};
