import {ComponentConfig} from '../types';
import {convertNode} from "@/components/process-utils";

export const statisticConfig: ComponentConfig = {
    type: 'AbcStatistic',
    isContainer: false,
    async customTransformer(nodeContext, globalContext) {
        const result: Record<string, any> = {
            componentSlot: [],
        };
        const {
            node,
            context
        } = nodeContext;

        const {componentProperties} = context;

        if (componentProperties) {
            const hasIcon = componentProperties.icon === true;
            delete componentProperties.icon;

            if (componentProperties.variant === 'colorful' && hasIcon) {
                // 读取下 icon 名称
                const icon = node.children.find(child => child.name.startsWith('s-'));
                if (icon) {
                    componentProperties.icon = icon.name;
                }
            }

            const topNode = node.children.find(child => child.name === 'topGroup');
            const bottomNode = node.children.find(child => child.name === 'bottomGroup');
            const lowerMostNode = node.children.find(child => child.name === 'lowerMostGroup');

            if (topNode) {
                topNode.children.forEach(child => {
                    if (child.name === 'topTitle') {
                        componentProperties.topTitle = child.characters;
                    }
                    if (child.name === 'topContent') {
                        componentProperties.topContent = child.characters;
                    }
                });
            }

            if (bottomNode) {
                for (let i = 0; i < bottomNode.children.length; i++) {
                    const child = bottomNode.children[i];

                    if (child.name === 'bottomTitle') {
                        componentProperties.title = child.characters;
                    }

                    if (child.name === 'bottomContent') {
                        if (componentProperties.contentSlot === true) {
                            const nodeContext = {
                                node: child,
                            }
                            const nodeResult = convertNode(nodeContext, globalContext);

                            result.componentSlot.push({
                                name: 'content',
                                content: nodeResult
                            });
                        } else {
                            componentProperties.content = child.characters;
                        }
                    }
                }

            }

            // 开启了最底部插槽
            if (lowerMostNode && componentProperties.bottomContentSlot === true) {
                const nodeContext = {
                    node: lowerMostNode,
                }
                const nodeResult = await convertNode(nodeContext, globalContext);

                result.componentSlot.push({
                    name: 'bottomContent',
                    content: nodeResult
                });
            }

        }
        return result;
    },
};
