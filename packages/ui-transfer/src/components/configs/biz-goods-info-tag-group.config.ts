import { ComponentConfig, NodeContext, ConversionContext } from '../types';

export const BizGoodsInfoTagGroup: ComponentConfig = {
  type: 'BizGoodsInfoTagGroup',
  isContainer: false,
  async customTransformer(nodeContext: NodeContext): Promise<Partial<ConversionContext>> {
    // 收集标签信息，从 AbcTagV2 组件中提取标签文本和属性
    function collectTags(n: any): { text: string, theme?: string }[] {
      let tags: { text: string, theme?: string }[] = [];
      if (n.name === 'AbcTag' || n.name === 'AbcTagV2') {
        // 从组件属性或者插槽中提取文本内容
        const text = n.componentProperties?.text || '';
        const theme = n.componentProperties?.theme || 'success';
        const defaultSlot = n.componentSlot?.find((slot: any) => slot.name === 'default');
        
        tags.push({
          text: defaultSlot?.content || text || n.characters || n.name,
          theme: theme
        });
      }
      if (n.children && Array.isArray(n.children)) {
        for (const child of n.children) {
          tags = tags.concat(collectTags(child));
        }
      }
      return tags;
    }

    // 入口节点是 BizGoodsInfoTagGroup
    const { node } = nodeContext;
    // 将 SceneNode 类型转换为 any 以便访问 children 属性
    const nodeAny = node as any;
    const goodsTagList = collectTags(nodeAny);

    // 检查是否有折叠标签的配置
    const foldTagsNode = nodeAny.children && Array.isArray(nodeAny.children) 
      ? nodeAny.children.find((child: any) => child.name === 'FoldTags') 
      : undefined;
    const isFoldTags = !!foldTagsNode;

    // 返回组件所需的数据结构
    return {
      componentProperties: {
        productInfo: {
          goodsTagList: goodsTagList.map(tag => ({
            name: tag.text,
            theme: tag.theme
          }))
        },
        isFoldTags
      }
    };
  },
};
