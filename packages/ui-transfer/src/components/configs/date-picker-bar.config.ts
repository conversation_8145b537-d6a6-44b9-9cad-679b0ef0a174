import { ComponentConfig, NodeContext } from '../types';

export const datePickerBarConfig: ComponentConfig = {
  type: 'AbcDatePickerBar',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };
    
    const { context } = nodeContext;

    const {
      componentProperties,
    } = context!;
    
    if (componentProperties) {
      componentProperties.options = [
        {
          label: 'day',
          name: '今天',
          getValue() {},
        },
        {
          label: 'week',
          name: '本周',
          getValue() {},
        },
        {
          label: 'month',
          name: '本月',
          getValue() {},
        },
        {
          label: 'year',
          name: '今年',
          getValue() {},
        },
      ]
    }
    
    return result;
  }
};
