import { ComponentConfig, NodeContext, GlobalContext } from '../types';
import {convertNode} from "@/components/process-utils";

export const BizSettingLayout: ComponentConfig = {
  type: 'BizSettingLayout',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
      const result: Record<string, any> = {
          children: [],
      };

      const {context, node} = nodeContext;

      const {
          componentProperties,
      } = context;

      Object.keys(componentProperties).forEach(key => {
          delete componentProperties[key];
      });

      const nodeList = node?.children || [];

      for (let i = 0; i < nodeList.length; i++) {
          const node = nodeList[i];
          const nodeContext = {
              node,
          };
          if(node.name === 'BizSettingLayoutContent') {
              const settingContentNode = node.children.find(c => c.name === 'BizSettingContent');
              const settingFooterNode = node.children.find(c => c.name === 'BizSettingFooter');

              const settingContentTransformResult = await convertNode({
                  node: settingContentNode,
              }, globalContext);

              const settingFooterTransformResult = await convertNode({
                  node: settingFooterNode,
              }, globalContext);

              if(settingFooterTransformResult) {
                  settingContentTransformResult && settingContentTransformResult.componentSlot.push({
                      name: 'footer',
                      content: settingFooterTransformResult,
                  });
              }

              if(settingContentTransformResult) {
                  result.children.push(settingContentTransformResult);
              }
          } else {
              const nodeResult = await convertNode(nodeContext, globalContext);
              if (nodeResult) {
                  result.children.push(nodeResult);
              }
          }
      }

      return result;
  }
};
