import { ComponentConfig } from '../types';
import { convertNode } from '../process-utils';


export const dropdownConfig: ComponentConfig = {
  type: 'AbcDropdown',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
      componentGuideline: [],
    };

    const { node, context } = nodeContext;

    delete context.componentProperties.reference;
    delete context.componentProperties.instance;

    const referenceNode = node.children[0];
    const referenceSlot = await convertNode({ node: referenceNode }, globalContext);
    result.componentSlot.push({
      name: 'reference',
        content: referenceSlot
    })

    const dropdownItemNodes = node.children[1].children;
    
    const promises = dropdownItemNodes.map(async (child) => {
      const dropdownItemSlot = await convertNode({ node: child }, globalContext);
      return {
        name: 'default',
        content: dropdownItemSlot
      }
    })
    
    const dropdownItemSlots = await Promise.all(promises);
    
    result.componentSlot.push(...dropdownItemSlots);

    return result;
  }
};
