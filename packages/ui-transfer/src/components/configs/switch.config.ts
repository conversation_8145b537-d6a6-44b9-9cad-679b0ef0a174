import { ComponentConfig } from '../types';

export const switchConfig: ComponentConfig = {
  type: 'AbcSwitch',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    console.log('switchConfig customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {};
    const {
      context,
      node,
    } = nodeContext;
    const {
      componentProperties
    } = context;
    if (componentProperties) {
      // 需要处理宽度
      if ((componentProperties.theme === 'fill' && node.width && node.width !== 36 && node.width !== '36px') || (componentProperties.theme === 'text'  && node.width && node.width !== 38 && node.width !== '38px')) {
        componentProperties.width = node.width;
      } else {
        delete componentProperties.width;
      }
      if (componentProperties.theme === 'fill') {
        delete componentProperties.theme;
      }
      delete componentProperties.states;
    }
    return result;
  }
};
