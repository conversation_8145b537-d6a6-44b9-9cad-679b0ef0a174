import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const tooltipConfig: ComponentConfig = {
  type: 'AbcTooltip',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
    const result: Record<string, any> = {
      componentGuideline: [],
      componentSlot: [],
    };

    const { node, context } = nodeContext;
    console.log('tooltipConfig customTransformer', { node, context });

    const { componentProperties } = context;
    if (componentProperties) {
      if (componentProperties.theme && !['yellow', 'black'].includes(componentProperties.theme)) {
        delete componentProperties.theme;
      }
      
      const validPlacements = ['top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 
                              'bottom-start', 'bottom', 'bottom-end', 'left-start', 'left', 'left-end'];
      if (componentProperties.placement && !validPlacements.includes(componentProperties.placement)) {
        componentProperties.placement = 'right-start';
      }
      
      if (componentProperties.size && componentProperties.size !== 'small') {
        delete componentProperties.size;
      }
      
      if (componentProperties.visibleArrow === false) {
        componentProperties.visibleArrow = false;
      } else{
        componentProperties.visibleArrow = true;
      }
      
      
      if (!componentProperties.content) {
        delete componentProperties.content;
        // 遍历子节点
        const targetChild = node.children?.find(child => child.name === 'popover');
        if (targetChild && targetChild.children) {
          const contentNode = targetChild.children.find(child => child.name === 'content');
          if (contentNode) {
            result.componentSlot.push({
              name: 'content',
              content: contentNode,
            });
            result.componentGuideline.push('使用 content 插槽自定义提示内容');
          }
        }
      }
      if(componentProperties.reference) {
        // 查找 reference 容器
        const referenceNode = node.children?.find(child => child.name === 'reference');
        if (referenceNode) {
          // 使用 div 容器包裹 reference 节点作为默认插槽内容
          result.componentSlot.push({
            name: 'default',
            content: {
              type: 'div',
              children: [referenceNode]
            }
          });
          result.componentGuideline.push('默认插槽内容使用 div 容器包裹 reference 元素');
        }
      }
      
    }
    
    // 删除不需要的属性
    if (componentProperties) {
      delete componentProperties.reference;
      delete componentProperties.instance;
      delete componentProperties.text;
    }
    
    // 如果没有插槽内容，则删除插槽属性
    if (result.componentSlot.length === 0) {
      delete result.componentSlot;
    }
    
    return result;
  }
};
