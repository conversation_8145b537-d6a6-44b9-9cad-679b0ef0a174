import { ComponentConfig, NodeContext, ConversionContext } from '../types';
import { extractTextContent, convertNode } from "@/components/process-utils";
import { isTruthyForFigma } from "@/utils/utils";

interface StatisticData {
  type: 'statistic';
  topTitle?: string;
  title?: string;
  content?: string;
  topContent?: string;
  bottomContent?: string;
  variant?: string;
  icon?: string;
  labelTips?: string;
}

interface OptionCardData {
  type: 'optionCard';
  title?: string;
  content?: string;
  variant?: string;
  checked?: boolean;
  disabled?: boolean;
}

export const BizStatisticCardBoard: ComponentConfig = {
    type: 'BizStatisticCardBoard',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext): Promise<Partial<ConversionContext>> => {
        const result: Record<string, any> = {
            componentProperties: {},
            componentSlot: [],
            componentGuideline: [],
        };

        const { node, context } = nodeContext;
        const frameNode = node as any;

        // 获取组件属性 - 只处理组件实际支持的属性
        if (context?.componentProperties) {
            const props = context.componentProperties;

            // 只保留组件实际支持的属性
            if (props.gutter !== undefined) {
                result.componentProperties.gutter = props.gutter;
            }
            if (props.minColWidth !== undefined) {
                result.componentProperties.minColWidth = props.minColWidth;
            }
            // type 属性会根据 dataList 的内容自动确定，不从 props 直接复制
        }

        const dataList: (StatisticData | OptionCardData)[] = [];
        const customSlots: any[] = [];

        // 遍历所有子节点
        if (frameNode.children && Array.isArray(frameNode.children)) {
            for (let i = 0; i < frameNode.children.length; i++) {
                const childNode = frameNode.children[i];

                if (childNode.name === 'AbcStatistic') {
                    // 处理 AbcStatistic 节点
                    const statisticData = await extractStatisticData(childNode);
                    if (statisticData) {
                        dataList.push(statisticData);
                    }
                } else if (childNode.name === 'AbcOptionCard') {
                    // 处理 AbcOptionCard 节点
                    const optionCardData = await extractOptionCardData(childNode);
                    if (optionCardData) {
                        dataList.push(optionCardData);
                    }
                } else {
                    // 其他节点作为自定义 slot 处理，或者转换为 custom 类型的数据项
                    const slotName = `custom-slot-${i}`;
                    const childNodeContext = {
                        node: childNode,
                        context: {}
                    };
                    const nodeResult = await convertNode(childNodeContext, {});

                    if (nodeResult) {
                        // 可以选择作为插槽或者作为 custom 类型的数据项
                        customSlots.push({
                            name: slotName,
                            content: nodeResult
                        });

                        // 同时也可以作为 custom 类型添加到 dataList
                        dataList.push({
                            type: 'custom',
                            slotName: slotName,
                            content: nodeResult
                        } as any);
                    }
                }
            }
        }

        // 设置 dataList 属性 - 这是组件的核心数据
        if (dataList.length > 0) {
            result.componentProperties.dataList = dataList;

            // 根据 dataList 的内容确定 type 属性
            const statisticItems = dataList.filter(item => item.type === 'statistic');
            const optionCardItems = dataList.filter(item => item.type === 'optionCard');

            // 判断类型：全是同一种类型才设置对应的 type，否则为 custom
            if (statisticItems.length === dataList.length && dataList.length > 0) {
                // 全是 AbcStatistic
                result.componentProperties.type = 'statistic';
            } else if (optionCardItems.length === dataList.length && dataList.length > 0) {
                // 全是 AbcOptionCard
                result.componentProperties.type = 'optionCard';
            } else {
                // 混合类型或包含其他节点
                result.componentProperties.type = 'custom';
            }
        } else {
            // 如果没有数据，提供默认的空数组和默认类型
            result.componentProperties.dataList = [];
            result.componentProperties.type = 'custom';
        }

        // 设置自定义插槽
        if (customSlots.length > 0) {
            result.componentSlot = customSlots;
        }

        // 添加组件使用指南
        result.componentGuideline.push('组件只支持 type、gutter、minColWidth、dataList 四个属性');
        result.componentGuideline.push('type 属性规则：全是 AbcStatistic → statistic，全是 AbcOptionCard → optionCard，其他情况 → custom');
        result.componentGuideline.push('AbcStatistic 节点会被转换为 type: "statistic" 的数据项，添加到 dataList 中');
        result.componentGuideline.push('AbcOptionCard 节点会被转换为 type: "optionCard" 的数据项，添加到 dataList 中');
        result.componentGuideline.push('其他节点会被转换为 type: "custom" 的数据项或自定义插槽');

        return result;
    }
};

// 提取 AbcStatistic 数据的辅助函数
async function extractStatisticData(node: any): Promise<StatisticData | null> {
    const data: StatisticData = {
        type: 'statistic'
    };

    // 获取组件属性
    if (node.componentProperties) {
        const props = node.componentProperties;

        if (props.variant) {
            data.variant = props.variant;
        }

        // 检查是否有图标
        if (isTruthyForFigma(props.icon) && props.variant === 'colorful') {
            const iconNode = node.children?.find((child: any) => child.name.startsWith('s-'));
            if (iconNode) {
                data.icon = iconNode.name;
            }
        }

        // 提取 labelTips
        if (props.labelTips) {
            data.labelTips = props.labelTips;
        }
    }

    // 提取文本内容
    if (node.children && Array.isArray(node.children)) {
        const topNode = node.children.find((child: any) => child.name === 'topGroup');
        const bottomNode = node.children.find((child: any) => child.name === 'bottomGroup');
        const lowerMostNode = node.children.find((child: any) => child.name === 'lowerMostGroup');

        if (topNode && topNode.children) {
            topNode.children.forEach((child: any) => {
                if (child.name === 'topTitle') {
                    data.topTitle = extractTextContent(child) || child.characters;
                }
                if (child.name === 'topContent') {
                    data.topContent = extractTextContent(child) || child.characters;
                }
            });
        }

        if (bottomNode && bottomNode.children) {
            bottomNode.children.forEach((child: any) => {
                if (child.name === 'bottomTitle') {
                    data.title = extractTextContent(child) || child.characters;
                }
                if (child.name === 'bottomContent') {
                    data.content = extractTextContent(child) || child.characters;
                }
            });
        }

        if (lowerMostNode) {
            data.bottomContent = extractTextContent(lowerMostNode) || lowerMostNode.characters;
        }
    }

    return data;
}

// 提取 AbcOptionCard 数据的辅助函数
async function extractOptionCardData(node: any): Promise<OptionCardData | null> {
    const data: OptionCardData = {
        type: 'optionCard'
    };

    // 获取组件属性
    if (node.componentProperties) {
        const props = node.componentProperties;

        if (props.variant) {
            data.variant = props.variant;
        }
        if (isTruthyForFigma(props.checked)) {
            data.checked = true;
        }
        if (isTruthyForFigma(props.disabled)) {
            data.disabled = true;
        }
    }

    // 提取文本内容
    if (node.children && Array.isArray(node.children)) {
        // 查找标题和内容节点
        const titleNode = node.children.find((child: any) =>
            child.name === 'title' || child.name === 'AbcText' || child.name.toLowerCase().includes('title'));
        const contentNode = node.children.find((child: any) =>
            child.name === 'content' || child.name.toLowerCase().includes('content'));

        if (titleNode) {
            data.title = extractTextContent(titleNode) || titleNode.characters;
        }
        if (contentNode) {
            data.content = extractTextContent(contentNode) || contentNode.characters;
        }

        // 如果没有找到特定的节点，尝试从第一个文本节点获取标题
        if (!data.title && !data.content) {
            const textNodes = node.children.filter((child: any) =>
                child.type === 'TEXT' || child.name === 'AbcText');
            if (textNodes.length > 0) {
                data.title = extractTextContent(textNodes[0]) || textNodes[0].characters;
            }
            if (textNodes.length > 1) {
                data.content = extractTextContent(textNodes[1]) || textNodes[1].characters;
            }
        }
    }

    return data;
}
