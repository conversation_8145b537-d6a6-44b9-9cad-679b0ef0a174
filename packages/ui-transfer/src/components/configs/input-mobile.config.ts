import { ComponentConfig, GlobalContext, NodeContext } from '../types';

export const inputMobileConfig: ComponentConfig = {
  type: 'AbcInputMobile',
  isContainer: false,
  customTransformer(nodeContext: NodeContext, globalContext: GlobalContext) {
    console.log('mobile customTransformer===', { nodeContext, globalContext });
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
    };
    const { context, node } = nodeContext;
    const { componentProperties } = context;

    if (componentProperties) {
      // 宽度设置
      if (componentProperties?.['width'] || node.width) {
        componentProperties.width = Number(componentProperties.width || node.width);
      }
      // size 处理
      if (componentProperties?.['size']) {
        const validSizes = ['tiny', 'small', 'medium', 'large', 'huge'];
        if (!validSizes.includes(componentProperties['size'])) {
          delete componentProperties['size'];
        }
      }
      if(componentProperties?.['showPrependIcon'] === true){
        componentProperties.showPrependIcon=true
      }else{
        delete componentProperties.showPrependIcon
      }
      if(componentProperties?.['autoWidth'] === true){
        componentProperties.autoWidth=true
      }else{
        delete componentProperties.autoWidth
      }
      if(componentProperties?.['disabled'] === true){
        componentProperties.disabled=true
      }else{
        delete componentProperties.disabled
      }
      if(componentProperties?.['isDisabledCountryCode'] === true){
        componentProperties.isDisabledCountryCode=true
      }else{
        delete componentProperties.isDisabledCountryCode
      }
      if(componentProperties?.['isShowCountryCode'] === false){
        componentProperties.isShowCountryCode=false
      }else{
        delete componentProperties.isShowCountryCode
      }
      if(componentProperties?.['showPrependicon'] === true){
        componentProperties.showPrependIcon=true
      }else{
        delete componentProperties.showPrependIcon
      }
    }

    return result;
  },
};
