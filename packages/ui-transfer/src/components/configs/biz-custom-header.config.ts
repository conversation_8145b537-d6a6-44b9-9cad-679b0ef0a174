import {ComponentConfig, NodeContext, ConversionContext} from '../types';
import { extractTextContent } from "@/components/process-utils";
import {isTruthyForFigma} from "@/utils/utils";

interface HeaderField {
  id: string;
  text: string;
  disabled?: boolean;
  hover?: boolean;
  checked?: boolean;
}

export const BizCustomHeader: ComponentConfig = {
  type: 'BizCustomHeader',
  isContainer: false,
  customTransformer: async (nodeContext: NodeContext): Promise<Partial<ConversionContext>> => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentGuideline: [],
    };

    const { node, context } = nodeContext;
    const frameNode = node as any;
    
    // 获取组件属性
    if (context?.componentProperties) {
      // 复制组件属性
      Object.assign(result.componentProperties, context.componentProperties);
    }
    
    // 处理头部标题
    const headerNode = frameNode.children?.find((child: any) => child.name.toLowerCase() === 'header');
    if (headerNode) {
      const titleNode = headerNode.children?.find((child: any) => child.name === '标题' || child.name === 'title' || child.name === 'AbcText');
      if (titleNode) {
        // 提取标题文本
        const titleText = extractTextContent(titleNode) || titleNode.characters || '';
        if (titleText) {
          result.componentProperties.title = titleText;
        }
      }
      
      // 处理头部属性
      if (headerNode.componentProperties) {
        if (isTruthyForFigma(headerNode.componentProperties.ShowHeaderBorderBottom)) {
          result.componentProperties.showHeaderBorderBottom = true;
        }
        if (isTruthyForFigma(headerNode.componentProperties.tab)) {
          result.componentProperties.showTab = true;
        }
        if (isTruthyForFigma(headerNode.componentProperties.search)) {
          result.componentProperties.showSearch = true;
        }
        if (isTruthyForFigma(headerNode.componentProperties.description)) {
          result.componentProperties.showDescription = true;
        }
      }
    }
    
    // 处理表格字段
    const tableFields: HeaderField[] = [];
    
    // 查找所有的 item-group 节点
    const bodyNodes = frameNode.children?.filter((child: any) => 
      child.name.toLowerCase() === 'body' || child.name.toLowerCase().includes('flex'));
    
    if (bodyNodes && bodyNodes.length > 0) {
      for (const bodyNode of bodyNodes) {
        // 查找 item-group 节点
        const itemGroups = bodyNode.children?.filter((child: any) => 
          child.name.toLowerCase() === 'item-group');
        
        if (itemGroups && itemGroups.length > 0) {
          for (const itemGroup of itemGroups) {
            // 查找所有的 CustomHeader-item 节点
            const itemNodes = itemGroup.children?.filter((child: any) => 
              child.name.toLowerCase().includes('customheader-item') || 
              child.name.toLowerCase().includes('custom-header-item'));
            
            if (itemNodes && itemNodes.length > 0) {
              // 处理每个 item 节点
              for (let i = 0; i < itemNodes.length; i++) {
                const itemNode = itemNodes[i];
                const itemField: HeaderField = {
                  id: `field_${tableFields.length + 1}`,
                  text: ''
                };
                
                // 获取文本内容
                const textNode = itemNode.children?.find((child: any) => 
                  child.name === 'AbcText');
                if (textNode) {
                  itemField.text = extractTextContent(textNode) || textNode.characters || `选项${i + 1}`;
                }
                
                // 获取状态
                if (itemNode.componentProperties) {
                  if (itemNode.componentProperties.states === 'hover') {
                    itemField.hover = true;
                  }
                  if (itemNode.componentProperties.states === 'disabled') {
                    itemField.disabled = true;
                  }
                  if (itemNode.componentProperties.states === 'checked') {
                    itemField.checked = true;
                  }
                }
                
                tableFields.push(itemField);
              }
            }
          }
        }
      }
    }
    
    // 处理 checkbox 节点，提取多选项
    const checkboxNodes: any[] = [];
    const findCheckboxNodes = (node: any) => {
      if (node.name === 'checkbox') {
        checkboxNodes.push(node);
      }
      if (node.children && Array.isArray(node.children)) {
        for (const child of node.children) {
          findCheckboxNodes(child);
        }
      }
    };
    
    findCheckboxNodes(frameNode);
    
    // 如果有 checkbox 节点，创建多选项
    if (checkboxNodes.length > 0) {
      const checkboxOptions = [];
      
      for (let i = 0; i < checkboxNodes.length; i++) {
        const checkboxNode = checkboxNodes[i];
        const option: {
          id: string;
          text: string;
          checked?: boolean;
          disabled?: boolean;
        } = {
          id: `option_${i + 1}`,
          text: ''
        };
        
        // 获取文本内容
        const contentNode = checkboxNode.children?.find((child: any) => child.name === 'content');
        if (contentNode) {
          const textNode = contentNode.children?.find((child: any) => child.name === '多选项' || child.name === 'AbcText');
          if (textNode) {
            option.text = extractTextContent(textNode) || textNode.characters || `选项${i + 1}`;
          }
        }
        
        // 获取状态
        if (checkboxNode.componentProperties) {
          if (checkboxNode.componentProperties.states === 'checked') {
            option.checked = true;
          }
          if (checkboxNode.componentProperties.disabled === true) {
            option.disabled = true;
          }
        }
        
        checkboxOptions.push(option);
      }
      
      if (checkboxOptions.length > 0) {
        result.componentProperties.checkboxOptions = checkboxOptions;
      }
    }
    
    // 如果找到了表格字段，添加到结果中
    if (tableFields.length > 0) {
      result.componentProperties.tableFields = tableFields;
    }
    
    // 添加组件使用指南
    result.componentGuideline.push('通过 tableFields 参数传入表格字段，而不是使用插槽');
    if (checkboxNodes.length > 0) {
      result.componentGuideline.push('通过 checkboxOptions 参数传入多选项');
    }
    
    return result;
  }
};
