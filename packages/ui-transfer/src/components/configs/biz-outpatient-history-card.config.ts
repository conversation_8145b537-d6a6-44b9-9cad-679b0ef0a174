import { ComponentConfig, NodeContext } from '../types';

export const BizOutpatientHistoryCard: ComponentConfig = {
  type: 'BizOutpatientHistoryCard',
  isContainer: false,
  customChildrenProcess: true,
  customTransformer: async (nodeContext: NodeContext) => {
    const result: Record<string, any> = {
      componentProperties: {},
      componentSlot: [],
    };

    const { context } = nodeContext;

    const {
      componentProperties,
    } = context!;

    if (componentProperties) {

    }
    return result;
  },
};
