import { ComponentConfig, NodeContext, GlobalContext } from '../types';
import {convertNode, extractTextContent} from "@/components/process-utils";

export const BizSettingFormItem: ComponentConfig = {
    type: 'BizSettingFormItem',
    isContainer: false,
    customTransformer: async (nodeContext: NodeContext, globalContext: GlobalContext) => {
        const result: Record<string, any> = {
            children: [],
        };

        const {
            context,
            node
        } = nodeContext;

        const {
            componentProperties
        } = context;

        const nodeList = node?.children || [];

        if (componentProperties) {
            const labelNode = nodeList.find((node: any) => node.name === 'label')?.children?.[0];
            if (labelNode) {
                console.error(labelNode,'labelNode')
                componentProperties.label = extractTextContent(labelNode);
            }
        }

        for (let i = 0; i < nodeList.length; i++) {
            const node = nodeList[i];
            if(node.name.toLowerCase() === 'label') {
                continue;
            }
            const nodeContext = {
                node: node,
            };
            const childResult = await convertNode(nodeContext, globalContext);
            if (childResult) {
                result.children.push(childResult);
            }
        }
        return result
    }
};