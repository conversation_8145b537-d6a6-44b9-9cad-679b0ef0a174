/* eslint-disable @typescript-eslint/no-explicit-any */
// 引入 Figma 类型定义
import {FigmaNode} from '@/types/figma';

// 将 Figma 节点转换为 JSON 对象
export function nodeToJSON(node: BaseNode): any {
    // 转换为扩展类型
    const extendedNode = node as unknown as FigmaNode;

    const result: any = {
        id: extendedNode.id,
        name: extendedNode.name,
        type: extendedNode.type,
        visible: extendedNode.visible
    };

    // 处理位置和大小
    if (extendedNode.x !== undefined) {
        result.x = extendedNode.x;
        result.y = extendedNode.y;
    }

    if (extendedNode.width !== undefined) {
        result.width = extendedNode.width;
        result.minWidth = extendedNode.minWidth;
        result.maxWidth = extendedNode.maxWidth;
        result.height = extendedNode.height;
    }

    // 处理透明度
    if (extendedNode.opacity !== undefined) {
        result.opacity = extendedNode.opacity;
    }

    // 处理填充
    if (extendedNode.fills) {
        result.fills = extendedNode.fills;
    }

    // 处理描边
    if (extendedNode.strokes) {
        result.strokes = extendedNode.strokes;
    }

    // 处理文本
    if (extendedNode.type === 'TEXT') {
        const textNode = extendedNode as TextNode;
        result.characters = textNode.characters;
        result.fontSize = textNode.fontSize;
        result.fontName = textNode.fontName;
        result.textAlignHorizontal = textNode.textAlignHorizontal;
        result.textAlignVertical = textNode.textAlignVertical;
    }

    // 处理布局
    if (extendedNode.layoutMode) {
        result.layoutMode = extendedNode.layoutMode;
        result.primaryAxisSizingMode = extendedNode.primaryAxisSizingMode;
        result.counterAxisSizingMode = extendedNode.counterAxisSizingMode;
        result.primaryAxisAlignItems = extendedNode.primaryAxisAlignItems;
        result.counterAxisAlignItems = extendedNode.counterAxisAlignItems;
        result.paddingLeft = extendedNode.paddingLeft;
        result.paddingRight = extendedNode.paddingRight;
        result.paddingTop = extendedNode.paddingTop;
        result.paddingBottom = extendedNode.paddingBottom;
        result.itemSpacing = extendedNode.itemSpacing;
    }

    if (extendedNode.layoutGrow) {
        result.layoutGrow = extendedNode.layoutGrow;
    }
    if (extendedNode.layoutSizingHorizontal) {
        result.layoutSizingHorizontal = extendedNode.layoutSizingHorizontal;
    }

    if (extendedNode.componentProperties) {
        result.componentProperties = extendedNode.componentProperties;
    }

    // 处理子节点
    if (extendedNode.children) {
        result.children = extendedNode.children.map(child => nodeToJSON(child));
    }

    return result;
}

// 将 RGB 颜色转换为十六进制
export function rgbToHex(r: number, g: number, b: number): string {
    const toHex = (n: number) => {
        const hex = Math.round(n * 255).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
    };
    return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
}

// 将 Figma 颜色对象转换为十六进制
export function figmaColorToHex(color: RGB): string {
    return rgbToHex(color.r, color.g, color.b);
}

// 获取节点的文本内容
export function getNodeText(node: SceneNode): string {
    if (node.type === 'TEXT') {
        return (node as TextNode).characters;
    }
    return '';
}

// 获取节点的样式
export function getNodeStyles(node: SceneNode): Record<string, any> {
    const extendedNode = node as unknown as FigmaNode;
    const styles: Record<string, any> = {};

    if (extendedNode.fills && extendedNode.fills.length > 0) {
        const fill = extendedNode.fills[0];
        if (fill.type === 'SOLID') {
            styles.backgroundColor = figmaColorToHex(fill.color);
        }
    }

    if (extendedNode.strokes && extendedNode.strokes.length > 0) {
        const stroke = extendedNode.strokes[0];
        if (stroke.type === 'SOLID') {
            styles.borderColor = figmaColorToHex(stroke.color);
        }
    }

    if (extendedNode.strokeWeight !== undefined) {
        styles.borderWidth = extendedNode.strokeWeight;
    }

    if (extendedNode.cornerRadius !== undefined) {
        styles.borderRadius = extendedNode.cornerRadius;
    }

    return styles;
}

export function pascalToKebabCase(str: string): string {
    return str.replace(/([A-Z])/g, (match, _, offset) => {
        return (offset > 0 ? '-' : '') + match.toLowerCase();
    });
}

export function figmaGradientToCSS(gradientData: any) {
    // 解析渐变变换矩阵（兼容 Figma 的 3x2 矩阵）
    const matrix = gradientData.gradientTransform;
    const radians = Math.atan2(matrix[0][1], matrix[0][0]);
    let angle = (radians * 180) / Math.PI;

    // 修正 Figma 与 CSS 的坐标系差异（Figma 0°=Y轴正方向，CSS 0°=X轴正方向）
    angle = 90 - angle; // 坐标系旋转补偿
    angle = angle < 0 ? angle + 360 : angle; // 确保角度在 0-360 范围内

    // 转换颜色停止点（含 Gamma 校正）
    const stops = gradientData.gradientStops.map(stop => {
        const rgba = stop.color;
        const hex = [
            Math.round(rgba.r * 255).toString(16).padStart(2, '0'),
            Math.round(rgba.g * 255).toString(16).padStart(2, '0'),
            Math.round(rgba.b * 255).toString(16).padStart(2, '0')
        ].join('');

        // 处理透明度（若需要可扩展为 rgba 格式）
        return `#${hex} ${stop.position * 100}%`;
    });

    return `background: linear-gradient(${angle.toFixed(2)}deg, ${stops.join(', ')});`;
}

// 将驼峰式命名转换为短横线分隔命名
export const toKebabCase = (str: string) => {
    return str.replace(/([A-Z])/g, '-$1').toLowerCase().replace(/^-/, '');
};

// 将短横线分隔命名转换为驼峰式命名
export const toPascalCase = (str: string) => {
    return str.split('-').map((word) => word.charAt(0).toUpperCase() + word.slice(1)).join('');
};

// 判断是否为真-figma节点属性专用
export const isTruthyForFigma = (val: any) => {
    return val === true || val === 'true'
}