## PageTable
使用场景：页面上包含表格，顶部带筛选栏，表格分页条数根据页面剩余高度自适应

注意点：
- abc-layout preset 指定为 page-table
- 筛选栏使用 abc-flex 两端对齐，并分别用 abc-space 控制元素间距
- 监听 abc-layout-content 的 layout-mounted 事件，在该事件中确定分页条数并触发数据获取
- abc-pagination 单独放到 abc-layout-footer 中，可通过 ul > li > span 的结构写分页信息，自带样式

代码结构如下：
```vue
<abc-layout preset="page-table">
    <abc-layout-header>
        <abc-flex justify="space-between">
            <abc-space>
              <!--左侧筛选-->
            </abc-space>
            <abc-space>
              <!--右侧操作按钮--> 
            </abc-space>
        </abc-flex>
    </abc-layout-header>

    <abc-layout-content @layout-mounted="handleMounted">
        <abc-table
            :data-list="dataList"
            :loading="loading"
            :render-config="renderConfig"
            @handleClickTr="handleClickTr"
        >
        </abc-table>
    </abc-layout-content>
    <abc-layout-footer>
        <abc-pagination
            :count="panelData.count"
            :pagination-params="pageParams"
            :show-total-page="false"
            @current-change="pageTo"
        >
            <ul v-if="panelData.count > 0" slot="tipsContent">
                <li>
                    共 <span>{{ panelData.count }}</span> 条单据，
                </li>
                <li>
                    数量 <span>{{ panelData.stat.count }}</span>，
                </li>
                <li>
                    含税金额 <span>{{ panelData.stat.amount | formatMoney(false) }}</span>
                </li>
            </ul>
        </abc-pagination>
    </abc-layout-footer>
</abc-layout>
```

## DialogTable
使用场景：弹窗中包含表格，通常包含操作按钮，分页信息通过表格的pagination属性设置

注意点：
- abc-layout preset 指定为 dialog-table
- 筛选栏使用 abc-flex 两端对齐，并分别用 abc-space 控制元素间距
- 分页信息通过 abc-table 的 pagination 属性设置

代码结构如下：
```vue
<abc-dialog
    v-if="modalVisible"
    v-model="modalVisible"
    title="ABC云检订单"
    size="hugely"
    append-to-body
    :auto-focus="false"
>
    <abc-layout preset="dialog-table">
        <abc-layout-header>
            <abc-flex justify="space-between">
                <abc-space>
                    <!--左侧筛选-->
                </abc-space>
                <abc-space>
                    <!--右侧操作按钮-->
                </abc-space>
            </abc-flex>
        </abc-layout-header>

        <abc-layout-content>
            <!--弹窗表格的分页放在 table 上，与表格形成整体，这个很重要-->
            <abc-table
                :loading="loading"
                :render-config="renderConfig"
                :data-list="list"
                :pagination="tablePagination"
                style="height: 513px;"
                @pageChange="handlePageChange"
            >
            </abc-table>
        </abc-layout-content>
    </abc-layout>

    <abc-flex slot="footer" justify="end">
        <abc-button variant="ghost" @click="modalVisible = false;">
            关闭
        </abc-button>
    </abc-flex>
</abc-dialog>
```