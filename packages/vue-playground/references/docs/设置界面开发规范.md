https://abcyun.yuque.com/abc-home/ywhr14/gemckad0tuy0f8u1

## 注意事项
1. 大部分设置模块最外层都加了`main-content`这个类名，这个类名设置了

![](https://cdn.nlark.com/yuque/0/2024/png/13012400/1733794536955-3875a29a-1100-408a-977c-e5a22733c2a9.png)

高度和边距，和我们当前的布局有冲突，但是又不能粗暴的删除这个类名，所以需要在模块内覆写样式

## UI 规范
### 表单
+ **布局**:内容区域定宽（768px）,侧边栏定宽（460px）
+ **底部操作**:跟随内容，超出一屏固定
+ **表单组**:由标题、表单项、分割线组成，组与组之间间距为 24px，最后一组无分割线
+ **表单项**:由标签、自定义表单组件、可选的分割线构成，表单项之间间距为 24px
+ **标签**:顶部对齐，行高待定
+ **操作按钮**:跟随在表单选项后的操作按钮为文字按钮
+ **表单内容**:单选框、多选框根据业务需求同行或换行排列
+ **描述**:换行

### 表格
+ 分页
    - 单表格带分页器：分页条数根据高度计算
    - 表单中的表格带分页器：分页器包裹在表格中，分页条数默认10
    - 没有分页器无尽表格，不处理
+ 对齐
    - 数字右对齐
    - 状态固定字数居中对齐，其他左对齐
    - 性别居中对齐
    - 其他左对齐
+ 文字高亮
    - 有二级弹窗，第一行蓝色高亮
    - 禁用文字置灰 theme: gray-light
    - 警告 theme: warning-light
+ 列宽
    - 定宽，出滚动条
    - 最小宽度加自适应
+ 文字超出
    - ... 展示，hover 有系统提示的 title 展示
+ hover 提示
    - 需要带 tooltip 的图标

## 组件使用
### layout + content + sidebar + footer	
+ **作用：** 控制表单布局，`sidebar`会固定在右侧，拥有内部滚动条；`footer`当`layout`存在滚动条时会固定在底部，否则跟随内容。
+ **用法：**

```vue
<setting-layout>
    <setting-content>
        表单内容
    </setting-content>

    <setting-sidebar>
        侧边栏
    </setting-sidebar>

    <setting-footer>
        <abc-space>
            <abc-button>
                保存
            </abc-button>
            
            <abc-button variant="ghost">
                取消
            </abc-button>
        </abc-space>
    </setting-footer>
</setting-layout>
```

### form + group + item + tip + indent
+ **作用：** 控制表单的样式
    - `form`控制`group`之间的间距以及分割线的展示，最后一个 `group`没有分割线；
    - `group`控制`item`之间的间距；
    - `item`根据参数`hasDivider`控制分割线的展示，`item`的`label`需要对齐内容区域的某一个元素，所以需要指定某个元素的行高作为`label`的行高，给对应元素设置`data-type='label-align'`控制`label`的行高；内部覆写了 `radio`，`checkbox`的间距，使用`useInnerLayout`取消该覆写；`verticle`控制内容选项的排列方向，默认为`true`即纵向排列内容选项；
    - `tip`控制`item`提示内容的展示，支持 `tip`参数或者`tip`插槽；
    - `indent`控制内容的缩进，提供插槽`content`存放缩进内容；
+ **用法**

```vue
<setting-form  :label-width="120">
    <setting-form-group title="预约模式">
        <setting-form-item label="预约人员">
            <abc-radio-group>
                <setting-form-item-tip>
                    <abc-radio :label="1" data-type="label-align">
                        按号源精确时间预约
                    </abc-radio>

                    <template #tip>
                        <abc-text theme="gray" size="mini">
                            适用于为每个患者保证充足服务时间
                        </abc-text>
                    </template>
                </setting-form-item-tip>

                <setting-form-item-indent>
                     <abc-radio :label="0">
                        按设定时段预约，每个时段可预约多个号源
                    </abc-radio>

                    <template #content>
                        <div>
                            缩进内容
                        </div>
                    </template>
                </setting-form-item-indent>
            </abc-radio-group>
        </setting-form-item>
    </setting-form-group>
</setting-form>
```

![](https://cdn.nlark.com/yuque/0/2024/png/13012400/1733293264665-734867f8-37d5-479f-9a0c-7c3c6923d1ea.png)

