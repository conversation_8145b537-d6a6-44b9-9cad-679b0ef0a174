{"name": "AbcSteps", "description": "步骤条组件，用于引导用户按照流程完成任务，提供了水平、垂直、点状等多种展示方式，与AbcStep搭配使用", "usage": "<template> <div> <AbcSteps :active=\"active\" @step-click=\"handleClick\" data-cy=\"storybook-test-default\" > <AbcStep :index=\"0\" icon=\"n-vip-1-line\" data-cy=\"abc-step-completed\" >下载并填写项目模板</AbcStep > <AbcStep :index=\"1\" icon=\"n-link-line\" data-cy=\"abc-step-in-progress\" >上传已填项目模板表</AbcStep > <AbcStep :index=\"2\" icon=\"n-card-line\" data-cy=\"abc-step-unfinished\" >导入完成</AbcStep > </AbcSteps> </div> </template> <script> export default { data() { return { active: 1, } }, methods: { handleClick(index) { alert(index) }, }, } </script>"}