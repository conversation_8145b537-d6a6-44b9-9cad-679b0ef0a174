{"name": "AbcInputNumber", "description": "数字输入框组件，用于输入数字内容，支持点击加减实现值的变化", "usage": "<template> <div style=\"display: flex; justify-content: space-around\"> <abc-input-number v-model=\"count1\" :width=\"136\"> </abc-input-number> <abc-input-number data-cy=\"storybook-test-input-number\" fixed-button v-model=\"count2\" button-placement=\"top\" :width=\"40\" > </abc-input-number> <abc-input-number fixed-button v-model=\"count3\" button-placement=\"left\" :width=\"40\" > </abc-input-number> </div> </template> <script> export default { data() { return { count1: 0, count2: 0, count3: 0, } }, } </script>"}