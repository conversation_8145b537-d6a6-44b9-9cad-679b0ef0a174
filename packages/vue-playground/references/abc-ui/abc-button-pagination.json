{"name": "AbcButtonPagination", "description": "按钮分页器，用于简单的前后分页，不包含中间页码", "usage": "<template> <div> <abc-button-pagination data-cy=\"storybook-test-default\" :total-count=\"paginationParams.totalCount\" :page-size=\"paginationParams.pageSize\" :current-page=\"paginationParams.pageIndex\" @changePage=\"handlePageChange\" ></abc-button-pagination> <abc-button-pagination size=\"large\" :total-count=\"paginationParams.totalCount\" :page-size=\"paginationParams.pageSize\" :current-page=\"paginationParams.pageIndex\" @changePage=\"handlePageChange\" ></abc-button-pagination> <div> <span>总数: {{ paginationParams.totalCount }}</span> <span>每页: {{ paginationParams.pageSize }}</span> <span>当前页: {{ paginationParams.pageIndex + 1 }}</span> </div> </div> </template> <script> export default { data() { return { paginationParams: { totalCount: 100, pageSize: 10, pageIndex: 0, }, } }, methods: { handlePageChange(pageIndex) { this.paginationParams.pageIndex = pageIndex }, }, } </script>"}