{"name": "AbcAutocomplete", "description": "搜索建议组件，用于输入框的搜索建议", "usage": "<template> <div> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\">normal </abc-radio> <abc-radio label=\"medium\">medium </abc-radio> <abc-radio label=\"large\">large </abc-radio> </abc-radio-group> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>append 插槽</span> <abc-switch v-model=\"showAppend\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>appendInner 插槽</span> <abc-switch v-model=\"showAppendInner\"></abc-switch> </abc-space> </abc-flex> <abc-autocomplete style=\"margin-top: 24px\" v-model.trim=\"value\" inner-width=\"200px\" :width=\"320\" :delay-time=\"0\" :async-fetch=\"true\" :fetch-suggestions=\"fetchData\" :max-length=\"20\" focus-show :auto-focus-first=\"false\" @enterEvent=\"handleSelect\" data-cy=\"storybook-test-default\" :size=\"size\" placeholder=\"small\" > <abc-icon slot=\"prepend\" icon=\"renminbi\"></abc-icon> <template slot=\"suggestion-header\"> <div class=\"suggestion-title\"> <span class=\"name\"> 姓名 </span> <span class=\"age\"> 年龄 </span> </div> </template> <template slot=\"suggestions\" slot-scope=\"props\"> <dt class=\"suggestions-item\" :class=\"{ selected: props.suggestion.name == value }\" @click=\"handleSelect(props.suggestion)\" > <div>{{ props.suggestion.name }}</div> <div>{{ props.suggestion.age }}</div> </dt> </template> <abc-icon slot=\"append\" v-if=\"showAppend\" icon=\"renminbi\"></abc-icon> <div slot=\"appendInner\" v-if=\"showAppendInner\">单位</div> </abc-autocomplete> </div> </template> <script> export default { data() { return { options: [ { name: 'bubble', age: 10, }, { name: '刘喜', age: 12, }, { name: '王富民', age: 13, disabled: true, }, { name: '王二小', age: 14, }, { name: 'a', age: 15, }, { name: 'b', age: 16, }, { name: 'c', age: 16, }, { name: 'd', age: 22, disabled: true, }, ], value: '', size: '', showAppend: false, showAppendInner: false, } }, methods: { fetchData(key, callback) { console.log('fetchData', key) return callback(this.options.filter((item) => item.name.includes(key))) }, handleSelect(data) { console.log('handleEnterEvent', data) this.value = data.name }, }, } </script>"}