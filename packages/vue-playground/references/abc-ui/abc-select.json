{"name": "AbcSelect", "description": "选择器组件", "usage": "<template> <div> <div class=\"mdx-title\">✨ tiny</div> <p> <abc-select data-cy=\"storybook-test-base1\" v-model=\"patientSex\" placeholder=\"no icon\" size=\"tiny\" no-icon clearable > <abc-option value=\"男\" label=\"男\" disabled></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <br /> <div class=\"mdx-title\">✨ small</div> <p> <abc-select data-cy=\"storybook-test-base6\" v-model=\"patientSex2\" placeholder=\"聚焦展示选项\" size=\"small\" focusShowOptions width=\"60\" inner-width=\"200\" > <abc-option value=\"初诊\" label=\"初诊\"></abc-option> <abc-option value=\"自费门诊自费门诊\" label=\"自费门诊自费门诊\" ></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ default</div> <p> <abc-select data-cy=\"storybook-test-base7\" v-model=\"patientSex1\" placeholder=\"没有option\" show-empty > </abc-select> <br /> <br /> <abc-select v-model=\"patientSex\" placeholder=\"请选择性别\"> <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ medium</div> <p> <abc-select data-cy=\"storybook-test-base8\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"medium\" > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ large</div> <p> <abc-select data-cy=\"storybook-test-base9\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"large\" > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ onlyBottomBorder</div> <p> <abc-select data-cy=\"storybook-test-base10\" v-model=\"patientSex\" placeholder=\"请选择性别\" size=\"large\" only-bottom-border > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\"> ✨ 给定value，但是没匹配上option, 展示空。默认选中值：不男不女 </div> <p> <abc-select data-cy=\"storybook-test-base11\" v-model=\"patientSex4\" placeholder=\"没有匹配上\" show-empty > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ 面板最小宽度38</div> <p> <abc-select data-cy=\"storybook-test-base12\" v-model=\"patientSex\" width=\"30\" no-icon > <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> <div class=\"mdx-title\">✨ prepend + 换 icon</div> <div> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\">normal </abc-radio> <abc-radio label=\"medium\">medium </abc-radio> <abc-radio label=\"large\">large </abc-radio> </abc-radio-group> </abc-space> </div> <br /> <p> <abc-select data-cy=\"storybook-test-base12\" v-model=\"patientSex\" :size=\"size\" trigger-icon=\"s-ai-fill\" > <abc-icon icon=\"s-b-scan-line\" slot=\"prepend\"></abc-icon> <abc-option value=\"男\" label=\"男\"></abc-option> <abc-option value=\"女\" label=\"女\"></abc-option> </abc-select> </p> </div> </template> <script> export default { data() { return { patientSex: '', patientSex1: '', patientSex2: '', patientSex4: '不男不女', size: '', } }, } </script>"}