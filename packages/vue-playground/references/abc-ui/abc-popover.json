{"name": "AbcPopover", "description": "弹出层组件是其他弹窗类组件如气泡确认框实现的基础，当这些组件提供的能力不能满足定制需求时，可以在弹出层组件基础上封装。仅展示文本信息优先使用 AbcTooltip/AbcTooltipInfo", "usage": "<template> <div> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"hover\" theme=\"yellow\" data-cy=\"storybook-test-base1\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">hover</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" data-cy=\"storybook-test-base2\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">click</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"focus\" theme=\"yellow\" data-cy=\"storybook-test-base3\" > <span slot=\"reference\"> <abc-input placeholder=\"focus 激活\"></abc-input> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"manual\" theme=\"yellow\" v-model=\"visible\" data-cy=\"storybook-test-base-manual\" > <span slot=\"reference\"> <abc-button @click=\"visible = !visible\" variant=\"ghost\" >manual</abc-button > </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"large\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">large</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"huge\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">huge</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> </div> </template> <script> export default { data() { return { visible: false, } }, } </script>"}