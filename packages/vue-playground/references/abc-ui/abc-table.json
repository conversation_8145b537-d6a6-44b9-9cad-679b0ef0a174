{"name": "AbcTable", "description": "表格组件，用于展示数据，支持自定义表头、自定义列、自定义行、自定义单元格、自定义分页、自定义加载状态、自定义空状态、自定义选中状态、自定义排序、自定义筛选、自定义固定列、自定义虚拟列表、自定义树形表格、自定义滚动加载、自定义配置工具等。", "usage": "<template> <abc-form item-no-margin> <abc-table data-cy=\"storybook-test-default\" show-order :render-config=\"renderConfig\" :data-list=\"dataList\" need-min-height :show-hover-tr-bg=\"false\" > <template #describe=\"{ trData }\"> <abc-table-cell :theme=\"trData.describe\" clickable> {{ trData.describe }} </abc-table-cell> </template> <template #unitPrice=\"{ trData }\"> <abc-table-cell> <abc-form-item> <abc-input v-model=\"trData.unitPrice\"></abc-input> </abc-form-item> </abc-table-cell> </template> <template #count=\"{ trData }\"> <abc-table-cell :title=\"title()\"> <abc-form-item> <abc-input v-model=\"trData.count\"></abc-input> </abc-form-item> </abc-table-cell> </template> <template #totalPrice=\"{ trData }\"> <abc-table-cell> <abc-form-item> <abc-input v-model=\"trData.totalPrice\"></abc-input> </abc-form-item> </abc-table-cell> </template> </abc-table> </abc-form> </template> <script> export default { data() { return { unitPrice: '10', unitCount: '1', totalPrice: '10', sex: '男', renderConfig: { hasInnerBorder: true, list: [ { key: 'name', label: '诊疗项目', testValue: '自动计费项目', headerAppendRender: (h, config) => { return <span style=\"color: red\">headerAppendRender</span> }, style: { flex: '1', paddingLeft: '', paddingRight: '', textAlign: 'left', }, }, { key: 'describe', label: '介绍', description: '提示', style: { flex: 'none', width: '86px', paddingLeft: '', paddingRight: '', textAlign: 'right', }, }, { key: 'unitPrice', label: '单价', colType: 'money', style: { flex: 'none', width: '86px', paddingLeft: '', paddingRight: '', textAlign: 'right', }, }, { key: 'count', label: '数量', style: { flex: 'none', width: '86px', textAlign: 'right', }, }, { key: 'totalPrice', label: '金额', colType: 'money', style: { flex: 'none', width: '86px', textAlign: 'right', }, }, ], }, dataList: [], } }, created() { setTimeout(() => { this.dataList = [ { keyId: '1', name: '自动计费项目', unitPrice: '10', count: '1', describe: 'success', totalPrice: '10', }, { keyId: '2', name: '诊疗项目', unitPrice: '10', count: '1', describe: 'warning', totalPrice: '10', }, { keyId: '3', name: '诊疗项目3', unitPrice: '30', unitCount: '3', describe: 'danger', totalPrice: '90', }, { keyId: '4', name: '诊疗项目4', unitPrice: '30', unitCount: '3', describe: 'primary', totalPrice: '90', }, ] }, 1000) }, methods: { title() { console.log(3333) }, }, } </script>"}