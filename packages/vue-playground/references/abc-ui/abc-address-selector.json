{"name": "AbcAddressSelector", "description": "地址选择器，用于选择省市区地址", "usage": "<template> <div> <abc-radio-group v-model=\"size\" style=\"margin-bottom: 24px\"> size 支持的大小 <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\"> 默认 </abc-radio> <abc-radio label=\"medium\"> medium </abc-radio> <abc-radio label=\"large\"> large </abc-radio> </abc-radio-group> <abc-address-selector data-cy=\"storybook-test-default\" :size=\"size\" v-model=\"address\" :width=\"300\" clearable @change=\"handleChange\" ></abc-address-selector> <div style=\"margin-top: 16px\">address: {{ address }}</div> </div> </template> <script> export default { data() { return { address: { addressCityId: '510100', addressCityName: '成都市', addressProvinceId: '510000', addressProvinceName: '四川', addressDistrictId: '510108', addressDistrictName: '成华区', }, size: '', } }, methods: { handleChange(newVal, oldVal) { console.log('address change', newVal, oldVal) }, }, } </script>"}