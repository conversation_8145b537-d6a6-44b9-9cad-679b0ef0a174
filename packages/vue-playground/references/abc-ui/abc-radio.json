{"name": "AbcRadio", "description": "单选框组件，用于在多个备选项中选中一个，常与radio-group搭配使用", "usage": "<template> <div> <abc-space> <abc-checkbox v-model=\"isBlock\">纵向排版</abc-checkbox> <abc-checkbox v-model=\"isDisabled\">禁用</abc-checkbox> <abc-checkbox v-model=\"isCancel\">支持反选</abc-checkbox> </abc-space> <abc-radio-group data-cy=\"storybook-test-default\" v-model=\"value\" :item-block=\"isBlock\" style=\"margin-top: 16px\" > <AbcRadio label=\"男\" :disabled=\"isDisabled\" :enable-cancel=\"isCancel\" >男</AbcRadio > <AbcRadio label=\"女\" :disabled=\"isDisabled\" :enable-cancel=\"isCancel\" ></AbcRadio> </abc-radio-group> </div> </template> <script> export default { data() { return { value: 0, isBlock: false, isDisabled: false, isCancel: false, } }, } </script>"}