{"name": "AbcCheckbox", "description": "多选框，用于在多个选项中选择，可单独使用或配合abc-checkbox-group组合使用", "usage": "<template> <div> <abc-space> <abc-checkbox data-cy=\"storybook-test-number\" v-model=\"value\" type=\"number\" > checkbox组件类型为number </abc-checkbox> <abc-checkbox data-cy=\"storybook-test-default\" v-model=\"value2\"> checkbox组件 </abc-checkbox> <abc-checkbox v-model=\"value3\" shape=\"round\"> checkbox圆形组件 </abc-checkbox> <abc-checkbox v-model=\"value5\" shape=\"ring\"> checkbox圆形组件[仅图标显示颜色] </abc-checkbox> <abc-checkbox v-model=\"value4\" :no-border=\"true\"> checkbox圆形组件 </abc-checkbox> </abc-space> <abc-flex vertical gap=\"large\"> <h2>自带间距：</h2> <abc-checkbox-group direction=\"vertical\" v-model=\"value6\"> <abc-checkbox v-for=\"option in options\" :key=\"option.value\" :label=\"option.value\"> {{option.label}} </abc-checkbox> </abc-checkbox-group> <h2>使用 gap：</h2> <abc-checkbox-group direction=\"vertical\" v-model=\"value6\" gap=\"8px 16px\"> <abc-checkbox v-for=\"option in options\" :key=\"option.value\" :label=\"option.value\"> {{option.label}} </abc-checkbox> </abc-checkbox-group> </abc-flex> </div> </template> <script> export default { data() { return { value: 0, value2: true, value3: true, value4: false, value5: true, value6: [], options: [{ label: '周一', value: 1 }, { label: '周二', value: 2 }, { label: '周三', value: 3 }, { label: '周四', value: 4 }, { label: '周五', value: 5 }, { label: '周六', value: 6 }, { label: '周日', value: 7 }], checked: 1, } }, watch: { value(val) { console.log(val) }, value2(val) { console.log(val) }, }, } </script>"}