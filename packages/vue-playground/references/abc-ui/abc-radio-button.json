{"name": "AbcRadioButton", "description": "单选按钮组件，用于在多个选项中选择一个，常以卡片形式展示。", "usage": "<script>\nimport { AbcRadioButton, AbcRadioGroup } from '@abc/ui-pc'\n\nexport default {\n  components: {\n    AbcRadioButton,\n    AbcRadioGroup\n  },\n  data() {\n    return {\n      value: 2\n    };\n  }\n}\n</script>\n\n<template>\n  <abc-radio-group v-model=\"value\">\n    <abc-radio-button name=\"123\" :label=\"0\">单据</abc-radio-button>\n    <abc-radio-button :label=\"1\">分类</abc-radio-button>\n    <abc-radio-button disabled :label=\"2\">明细</abc-radio-button>\n  </abc-radio-group>\n</template>"}