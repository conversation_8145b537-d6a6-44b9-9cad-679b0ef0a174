{"name": "AbcTextarea", "description": "多行文本输入框", "usage": "<template> <div> <abc-flex> 禁用状态：<abc-switch v-model=\"isDisabled\"></abc-switch> </abc-flex> <abc-flex style=\"margin-top: 16px\"> 只读状态：<abc-switch v-model=\"isReadonly\"></abc-switch> </abc-flex> <abc-textarea style=\"margin-top: 16px\" v-model=\"value\" show-max-length-tips :disabled=\"isDisabled\" data-cy=\"storybook-test-default\" :readonly=\"isReadonly\" :maxlength=\"10\" placeholder=\"输入模板内容\" ></abc-textarea> </div> </template> <script> export default { data() { return { value: '基础用法', isDisabled: false, isReadonly: false, } }, } </script>"}