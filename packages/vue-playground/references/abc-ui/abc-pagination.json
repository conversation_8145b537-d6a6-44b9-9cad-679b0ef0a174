{"name": "AbcPagination", "description": "分页组件，用于分页展示数据，包含页码、上一页、下一页，指定分页条数等功能", "usage": "<template> <div> <h5 style=\"margin: 24px 0 0\">size:normal</h5> <abc-pagination data-cy=\"storybook-test-base1\" :show-total-page=\"true\" show-size :pagination-params=\"pageParams\" :count=\"pageParams.count\" @current-change=\"handlePageChange\" > </abc-pagination> <abc-pagination :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" :is-background=\"false\" prev-text=\"\" next-text=\"\" @current-change=\"handlePageChange\" > </abc-pagination> <h5 style=\"margin: 24px 0\">size:small</h5> <abc-pagination size=\"small\" :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" @current-change=\"handlePageChange\" > </abc-pagination> <abc-pagination size=\"small\" :show-total-page=\"true\" :pagination-params=\"pageParams\" :count=\"pageParams.count\" :is-background=\"false\" prev-text=\"\" next-text=\"\" @current-change=\"handlePageChange\" > </abc-pagination> </div> </template> <script> export default { data() { return { pageParams: { pageIndex: 0, pageSize: 10, count: 100, }, } }, methods: { handlePageChange(val) { console.log('val', val) }, }, } </script>"}