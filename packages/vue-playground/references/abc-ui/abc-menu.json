{"name": "AbcMenu", "description": "菜单组件，用于导航菜单的展示，可与AbcMenuItem和AbcSubMenu，AbcMenuGroup组件配合使用", "usage": "<template>\n  <div style=\"width: 100%; padding: 20px 0\">\n    <h5>常规用法</h5>\n    <div style=\"width: 260px; height: 400px; padding: 10px 10px\">\n      <abc-menu v-model=\"value\" @click=\"selectItem\">\n        <abc-menu-item icon=\"n-settings-line\" index=\"1\">菜单1</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"2\">菜单2</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"3\">菜单3</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"4\" :count=\"13\">菜单4</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"5\">菜单5</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"6\">菜单6</abc-menu-item>\n        <abc-sub-menu :index=\"7\" icon=\"n-settings-line\" value=\"菜单7\">\n          <abc-menu-item index=\"7-1\">菜单7-1</abc-menu-item>\n          <abc-menu-item index=\"7-2\">菜单7-2</abc-menu-item>\n        </abc-sub-menu>\n        <abc-menu-item icon=\"n-settings-line\" index=\"8\">菜单8</abc-menu-item>\n      </abc-menu>\n    </div>\n    <h5>分组用法</h5>\n    <div style=\"width: 260px;height: 400px;padding: 10px 10px;\">\n      <abc-menu badge-variant=\"dot\" v-model=\"value\" @click=\"selectItem\">\n        <abc-menu-group>\n          <abc-menu-item icon=\"n-settings-line\" :count=\"10\" index=\"1\">菜单1</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"2\">菜单2</abc-menu-item>\n        </abc-menu-group>\n        <abc-menu-group>\n          <abc-menu-item icon=\"n-settings-line\" index=\"3\">菜单3</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"4\">菜单4</abc-menu-item>\n        </abc-menu-group>\n      </abc-menu>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      value: '1',\n    }\n  },\n  methods: {\n    selectItem(item) {\n      console.log('item', item)\n    },\n  },\n}\n</script>"}