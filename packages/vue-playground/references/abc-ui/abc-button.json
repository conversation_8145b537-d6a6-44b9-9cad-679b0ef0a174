{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "按钮用于开启一个闭环的操作任务，如“收费”订单、“购买”商品等", "usage": "<template> <abc-flex class=\"button-wrapper\" vertical> <abc-flex vertical gap=\"large\"> <abc-flex> <abc-button data-cy=\"storybook-test-default\">完成检验</abc-button> <abc-button icon=\"n-print-line\" variant=\"ghost\">打印报告</abc-button> <abc-button icon=\"n-save-line\" variant=\"ghost\">保存</abc-button> </abc-flex> <abc-flex> <abc-button>确定</abc-button> <abc-button variant=\"ghost\">取消</abc-button> <abc-button variant=\"ghost\" theme=\"danger\">删除</abc-button> </abc-flex> <abc-flex> <abc-button icon=\"n-add-line-medium\" theme=\"success\" >新建档案</abc-button > <abc-button icon=\"n-download-line\" variant=\"ghost\">导出</abc-button> </abc-flex> <abc-flex> <abc-button size=\"small\" icon=\"n-edit-line\" variant=\"ghost\" theme=\"default\" icon-color=\"var(--abc-color-theme2)\" > 修改 </abc-button> <abc-button size=\"small\" icon=\"n-wechat-line\" variant=\"ghost\" theme=\"default\" icon-color=\"var(--abc-color-G2)\" > 微信 </abc-button> <abc-button size=\"small\" icon=\"n-Follow-up-line\" variant=\"ghost\" theme=\"default\" icon-color=\"var(--abc-color-theme2)\" > 随访 </abc-button> </abc-flex> </abc-flex> </abc-flex> </template> <script> export default { data() { return { size: 'normal', disabled: false, } }, } </script>"}