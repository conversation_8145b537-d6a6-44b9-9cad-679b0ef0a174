{"name": "AbcTooltipInfo", "description": "由于 AbcTooltip 组件结合 info_bold 的 icon 使用场景较多，提供了一个 AbcTooltipInfo 组件，便于使用", "usage": "<template> <abc-space direction=\"vertical\"> <div class=\"box\"> <div class=\"top\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"top-start\" content=\"Top Start 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"top\" content=\"Top 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"top-end\" content=\"Top End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"left\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"left-start\" content=\"Left Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"left\" content=\"Left 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"left-end\" content=\"Left End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"right\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"right-start\" content=\"Right Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"right\" content=\"Right 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"right-end\" content=\"Right End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"bottom\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom-start\" content=\"Bottom Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom\" content=\"Bottom 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom-end\" content=\"Bottom End 提示文字\" > </abc-tooltip-info> </div> </div> </div> <abc-tooltip-info> <div>通过 slot 自定义内容</div> <div> 没有<span style=\"color: cadetblue; font-weight: bold\">想不到</span >，只有<span style=\"color: rosybrown; font-weight: bold\">做不到</span> </div> </abc-tooltip-info> </abc-space> </template> <script> export default {} </script>"}