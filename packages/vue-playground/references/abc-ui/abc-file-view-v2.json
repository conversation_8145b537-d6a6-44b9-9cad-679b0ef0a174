{"name": "AbcFileViewV2", "description": "文件展示组件，用于展示不同类型的文件：图片、doc、pdf、ppt、xls等", "usage": "<template> <div> <div class=\"box\" style=\"display: flex\"> <span>28px</span> <div style=\"width: 44px; height: 44px\" v-for=\"(file, index) in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" size=\"mini\" :data-cy=\"!index ? 'storybook-test-default' : ''\" show-delete-icon @delete=\"onDelete\" > </abc-file-view-v2> </div> </div> <div class=\"box\" style=\"display: flex\"> <span>34px</span> <div style=\"width: 44px; height: 44px\" v-for=\"file in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" show-delete-icon size=\"small\"> </abc-file-view-v2> </div> </div> <div class=\"box\" style=\"display: flex\"> <span>44px</span> <div style=\"width: 44px; height: 44px\" v-for=\"file in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" show-delete-icon> </abc-file-view-v2> </div> </div> </div> </template> <script> export default { data() { return { fileList: [ { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JYx79zRKxSnpBqzZVQnbZBokKNrohlJc_1625724986292.png', fileName: 'qr.png', fileSize: 5089, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/U638oEgsWkWjJ5YdtIZZodxmziMDwtLM_1625725312770.pdf', fileName: '检验报告.pdf', fileSize: 1030, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JipS1TGa5slbx4Trk45zff4sXma3KuE7_1625725875621.xlsx', fileName: '财务报表.xlsx', fileSize: 8011, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JYKRE6coXwBmspJCbGsgkURywPa1dqzo_1625725878173.pptx', fileName: '演示文档.pptx', fileSize: 25985, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/2g1Tc5KnwkyNM5b7wL8ZdY66CC36JoXW_1625725880468.docx', fileName: '毕业论文.docx', fileSize: 11234, }, ], } }, methods: { onDelete(e) { console.log('onDelete', e) }, }, } </script>"}