@import "@abc/ui-pc/theme/common/var.scss";

// 用于放置 CSS 变量，之前放在 theme.scss 中，由于 theme.scss 会被多个模块引用，导致变量重复声明，所以把变量单独抽出来
:root {
    //--background: transparent;
    --headerBackgroundColor: transparent;
    --headerFontColor: #ffffff;
    --headerDropdownItemActiveBackgroundColor: rgba(255, 255, 255, 0.12);
    --headerClinicDropdownWidth: 239px;
    --headerBusinessDropdownWidth: 150px;
    // 头部门店下拉左边距
    --headerClinicDropdownLeftMargin: -58px;
    --headerClinicDropdownTopMargin: 0;
    --headerClinicDropdownArrowLeftMargin: 105px;
    --scrollBarBackgroundColor: rgba(255, 255, 255, 0.2);
    --scrollBarHoverBackgroundColor: rgba(255, 255, 255, 0.5);
}