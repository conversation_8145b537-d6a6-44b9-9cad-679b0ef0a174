@import "./var.scss";

html {
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0);
}

body {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: Helvetica Neue, Helvetica, Arial, PingFang SC, MyHeiTi, Hiragino Sans GB, Heiti SC, WenQuanYi Micro Hei, sans-serif !important;
    font-size: 14px;
    color: var(--abc-color-T1);
    cursor: auto;
    background-color: var(--background, #f5f5f5);
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    -webkit-overflow-scrolling: touch;
}

.page-scroll-wrapper {
    height: calc(100% + 15px);
}

body.hidden-scroll {
    overflow: hidden;
}

.hidden-scroll {
    overflow: hidden !important;
}

#app {
    width: 100%;
    height: 100%;
    overflow-y: hidden;
}

ul {
    padding: 0;
    margin: 0;
    list-style: none;
}

p {
    padding: 0;
    margin: 0;
}

h5 {
    padding: 0;
    margin: 0;
    font-size: 14px;
}

*,
*::before,
*::after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    color: inherit;
    text-decoration: none;
    cursor: pointer;
}

.fade-enter-active,
.fade-leave-active {
    transition: all 500ms ease;
}

.fade-enter,
.fade-leave-active {
    opacity: 0;
}

.link-type,
.link-type:focus {
    color: var(--abc-color-theme1);
    cursor: pointer;

    &:hover {
        color: var(--abc-color-theme2);
    }
}

.ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.clearfix::after {
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
    content: '';
}

.max2line {
    display: -webkit-box;
    overflow: hidden;
    word-break: break-all;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.abc-sortable-ghost {
    background-color: var(--abc-color-P6) !important;
    opacity: 0.6 !important;
}

@keyframes octocat-wave {

    0%,
    100% {
        transform: rotate(0);
    }

    20%,
    60% {
        transform: rotate(-25deg);
    }

    40%,
    80% {
        transform: rotate(10deg);
    }
}

input::-webkit-input-placeholder {
    color: var(--abc-color-T3);
}

input:-ms-input-placeholder {
    color: var(--abc-color-T3);
}

input::placeholder {
    color: var(--abc-color-T3);
}

::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    /* 滚动条里面轨道 */
    background: transparent;
    opacity: 0;
}

::-webkit-scrollbar-thumb {
    /* 滚动条里面小方块 */
    cursor: pointer;
    background: #e6eaed;
    background-clip: content-box;
    border: 1px solid transparent;
    border-radius: 6px;
}

::-webkit-scrollbar-thumb:hover {
    /* 滚动条里面小方块 */
    cursor: pointer;
    background: #dee2e6;
    background-clip: content-box;
    border: 1px solid var(--abc-color-T3);
    border-radius: 6px;
}

.page-loading {
    width: 100%;
    height: 100%;
    padding-bottom: 100px;
    text-align: center;

    .circular {
        width: 42px;
        height: 42px;
        margin-top: 100px;
        animation: loading-rotate 2s linear infinite;
    }

    svg:not(:root) {
        overflow: hidden;
    }

    .path {
        stroke: var(--abc-color-Y2);
        stroke-dasharray: 90, 150;
        stroke-dashoffset: 0;
        stroke-linecap: round;
        stroke-width: 2;
        animation: loading-dash 1.5s ease-in-out infinite;
    }
}

i.cis-icon-jinggao {
    width: 14px;
    height: 16px;
    font-size: 14px;
    line-height: 1;
    color: var(--abc-color-Y2);
}

i.cis-icon-Attention {
    width: 14px;
    height: 16px;
    font-size: 12px;
    line-height: 1;
    color: var(--abc-color-Y2);
}

.blank-index {
    position: relative;
    width: 100%;
    height: 100%;

    .blank-title {
        position: relative;
        height: 47px;
        margin: 0 auto 24px;
        font-size: 20px;
        font-weight: 500;
        color: var(--abc-color-T1);
        border-bottom: 1px solid var(--abc-color-P1);
    }

    .img-wrapper {
        width: 160px;
        height: 48px;
        margin: 0 auto;
        line-height: 48px;
        text-align: center;

        .empty-icon {
            font-size: 48px;
            color: #ccd3d9;
        }

        img {
            width: 100%;
        }
    }

    p {
        margin-top: 8px;
        color: var(--abc-color-T2);
        text-align: center;

        span {
            color: var(--abc-color-theme2);
            cursor: pointer;

            &:hover {
                color: var(--abc-color-theme1);
            }

            &:active {
                color: var(--abc-color-theme1);
            }
        }
    }
}

.remove-box {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 9999;
    min-width: 174px;
    max-width: 250px;
    padding: 16px;
    line-height: 20px;
    color: var(--abc-color-T1);
    background: #ffffff;
    border: 1px solid var(--abc-color-P3);
    border-radius: 4px;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);

    &::before {
        position: absolute;
        top: 33px;
        right: -8px;
        z-index: -1;
        width: 14px;
        height: 14px;
        content: "";
        background: #ffffff;
        border: 1px solid var(--abc-color-P3);
        border-bottom: 0;
        border-left: 0;
        border-radius: 0;
        transform: rotate(45deg);
    }

    .warning-msg {
        color: var(--abc-color-T1);
    }

    .btn-group {
        margin-top: 10px;
        font-size: 0;
        line-height: 14px;
        text-align: left;

        .abc-button+.abc-button {
            margin-left: 10px;
        }
    }

    &.placement-right {
        &::before {
            left: -8px;
            border: 1px solid var(--abc-color-P3);
            border-top: 0;
            border-right: 0;
        }
    }

    &.placement-top {
        &::before {
            top: -8px;
            border: 1px solid var(--abc-color-P3);
            border-right: 0;
            border-bottom: 0;
        }
    }

    &.placement-bottom {
        &::before {
            bottom: -8px;
            border: 1px solid var(--abc-color-P3);
            border-top: 0;
            border-left: 0;
        }
    }
}

*.unselectable {
    -moz-user-select: -moz-none;
    -khtml-user-select: none;
    -webkit-user-select: none;

    /*
    Introduced in IE 10.
    See http://ie.microsoft.com/testdrive/HTML5/msUserSelect/
  */
    -ms-user-select: none;
    user-select: none;
}


.suggestions-wrapper {
    .abc-scrollbar-wrapper {
        border-radius: 4px;
    }
}

.input-adjust-price-triangle {
    position: absolute;
    right: 2px;
    bottom: 2px;
    z-index: 2;
    display: inline-block;
    width: 0;
    height: 0;
    cursor: pointer;
    border-top: 10px solid transparent;
    border-right: 10px solid var(--abc-color-B2);
    border-bottom: 0;
    border-left: 0;

    &+#adjustment-popover-wrapper {
        position: absolute;
        right: 0;
        z-index: 3;
        width: 228px;
        margin-top: 4px !important;
        color: var(--abc-color-T1) !important;

        .prepend-input {
            z-index: 3;

            &+.abc-input__inner {
                padding-left: 32px !important;
                color: var(--abc-color-T1) !important;
                border: 1px solid var(--abc-color-P1) !important;
                border-radius: 4px !important;

                &:focus {
                    border-color: #0270c9 !important;
                }
            }
        }

        .change-fee-box {
            .prepend-input+.abc-input__inner {
                padding-left: 8px !important;
            }

            .abc-input__inner {
                height: 32px !important;
                color: var(--abc-color-T1) !important;
                text-align: left !important;
            }
        }
    }
}