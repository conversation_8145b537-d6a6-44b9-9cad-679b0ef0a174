<template>
  <abc-dialog v-model="dialogVisible" size="xlarge" :show-header-border-bottom="true" :fullscreen="false"
              :responsive="false" :show-dialog-cover="true" title="长护登记">
    <template #footer>
      <abc-flex justify="space-between" style="width: 100%; padding: 0">
        <abc-flex :two-button="false" :one-button="false" :checkbox="false" justify="flex-start" align="center"
                  gap="middle" style="width: 100%"></abc-flex>
        <abc-flex :one-button="false" :two-button="true" :checkbox="false" :three-button="false" justify="flex-end"
                  align="center" gap="middle" style="width: 100%">
          <abc-space direction="horizontal" size="small">
            <abc-button shape="square" variant="fill" theme="primary" size="normal" :disabled="false">登记</abc-button>
            <abc-button shape="square" variant="ghost" theme="primary" size="normal" :disabled="false"
                        @click="dialogVisible = false">取消
            </abc-button>
          </abc-space>
        </abc-flex>
      </abc-flex>
    </template>
    <abc-flex vertical justify="flex-start" align="flex-start" gap="large" style="width: 100%">
      <abc-flex vertical justify="flex-start" align="flex-start" gap="middle" style="width: 100%">
        <abc-descriptions :bordered="true" :label-style="true" :content-style="true" :column="3" border-style="solid"
                          style="width: 100%" :label-width="70">
          <abc-descriptions-item label="持卡人">
            <abc-text size="normal" theme="black">李秀村 男 79</abc-text>
          </abc-descriptions-item>
          <abc-descriptions-item label="认证码">
            <abc-text size="normal" theme="black">123223212121</abc-text>
          </abc-descriptions-item>
          <abc-descriptions-item label="证件号">
            <abc-text size="normal" theme="black">510221312323232</abc-text>
          </abc-descriptions-item>
          <abc-descriptions-item label="类   别">
            <abc-text size="normal" theme="black">-</abc-text>
          </abc-descriptions-item>
          <abc-descriptions-item label="单   位">
            <abc-text size="normal" theme="black">-</abc-text>
          </abc-descriptions-item>
          <abc-descriptions-item label="区   划">
            <abc-text size="normal" theme="black">-</abc-text>
          </abc-descriptions-item>
        </abc-descriptions>
      </abc-flex>
      <abc-tabs-v2 size="middle" :option="tabOptions" style="width: 100%"></abc-tabs-v2>
      <abc-form label-position="top" label-align="left" margin-size="large" :item-no-margin="false" :is-detail="false"
                :item-block="true">
        <abc-form-item-group grid-column-count="3" :grid="true" :is-excel="false">
          <abc-form-item label-position="top" type="input" label="状态" grid-column="span 1">
            <abc-input placeholder="未登记" :append="false" :clearable="false" :appendlabel="false" :disabled="false"
                       :width="228"></abc-input>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="申请类型" grid-column="span 1">
            <abc-select :statistics-number="false" placeholder="请选择" states="default" :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="服务类型" grid-column="span 1">
            <abc-select :statistics-number="false" placeholder="请选择" states="default" :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="结算类型" grid-column="span 1">
            <abc-select :statistics-number="false" placeholder="请选择" states="default" :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="radioButton" label="气管切开" grid-column="span 1">
            <abc-radio-group :item="2" :flex="false" theme="default" :item-block="false">
              <abc-radio-button :badge="false" states="checked" :width="114" :check-icon="false" label="否">否
              </abc-radio-button>
              <abc-radio-button :badge="false" states="default" :width="114" :check-icon="false" label="是">是
              </abc-radio-button>
            </abc-radio-group>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="评估等级" grid-column="span 1">
            <abc-select :statistics-number="false" placeholder="请选择" states="default" :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="业绩医生" grid-column="span 1">
            <abc-select placeholder="请选择业绩医生" :statistics-number="false" states="default"
                        :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="责任医生" grid-column="span 1">
            <abc-select placeholder="请选择责任医生" :statistics-number="false" states="default"
                        :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="责任护士" grid-column="span 1">
            <abc-select placeholder="请选择责任护士" :statistics-number="false" states="default"
                        :width="228"></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="select" label="责任护理员" grid-column="span 1">
            <abc-select></abc-select>
          </abc-form-item>
          <abc-form-item label-position="top" type="input" label="诊断" grid-column="span 2">
            <abc-input :appendlabel="false" :clearable="false" :append="false" placeholder="请输入" :disabled="false"
                       :width="480"></abc-input>
          </abc-form-item>
        </abc-form-item-group>
      </abc-form>
    </abc-flex>
  </abc-dialog>
</template>
<script>

export default {
  components: {}, data() {
    return {
      dialogVisible: true,
      tabOptions: [{label: '登记信息', value: 0}, {label: '院内费用', value: 1}, {label: '外诊费用', value: 2}]
    };
  }
};</script>