import Vue from 'vue'
// import VueI18n from 'vue-i18n';
// Vue.use(VueI18n);
import './styles/index.scss'
import './abc-ui-regist.js'
import {i18n} from "@abc/pc-components";

import App from './App.vue'

Vue.config.productionTip = false


// const i18n = new VueI18n({
//   locale: 'zh-MO',
//   messages: {
//     'zh-CN': {currencySymbol:"¥",registrationFeeName:"挂号费"},
//     'zh-MO': {currencySymbol:"$",registrationFeeName:"挂号费"},
//   },
// });
//
console.log('i18n',i18n)
i18n.setLocaleMessage('zh-MO', Object.assign({}, i18n.messages['zh-MO']));

new Vue({
  i18n,
  render: h => h(App),
}).$mount('#app')
