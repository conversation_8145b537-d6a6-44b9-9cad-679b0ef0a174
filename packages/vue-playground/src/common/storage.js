const setItem = (key, value) => {
    localStorage.setItem(key, JSON.stringify(value))
}
const getItem = (key) => {
    return JSON.parse(localStorage.getItem(key))
}
const removeItem = (key) => {
    localStorage.removeItem(key)
}
export function useStorage(scope) {
    return {
        setItem: (value) => {
            setItem(`${scope}`, value)
        },
        getItem: () => {
            return getItem(`${scope}`)
        },
        removeItem: () => {
            removeItem(`${scope}`)
        }
    }
}
