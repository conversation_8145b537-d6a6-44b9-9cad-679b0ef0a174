## 工程介绍
基于 Vue 2.7 开发的组件调试工程，代码的编写都放在 src/ui-playground.vue 中，方便快速调试

- 组件库基于私有组件库 ABC UI，在 references/abc-ui/ 目录下有对应组件的用法
- npm包管理器是 pnpm

## 文件结构
- references: 存放的参考内容，可以从里面查找私有组件库的使用方法，不要修改这里面的文件，仅做用法参考
- references/abc-ui: 私有组件库的用法，可以从里面查找私有组件库的使用方法
- src/ui-playground: 用于在里面生成代码，方便快速调试，查看效果

## 组件库

### ABC UI
ABC UI 是公司内部封装的组件库，对应的 npm 包：@abc/ui-pc，具体用法在 references/abc-ui/ 中参考，例如 references/abc-ui/abc-table.json 是 abc-table 的用法，提供组件如下：

abc-ui
├── abc-address-selector.json
├── abc-autocomplete.json
├── abc-badge.json
├── abc-button-group.json
├── abc-button-pagination.json
├── abc-button.json
├── abc-card.json
├── abc-cascader.json
├── abc-checkbox-button.json
├── abc-checkbox.json
├── abc-content-empty.json
├── abc-date-pagination.json
├── abc-date-picker-bar.json
├── abc-date-picker.json
├── abc-date-time-picker.json
├── abc-delete-icon.json
├── abc-descriptions.json
├── abc-dialog.json
├── abc-divider.json
├── abc-dropdown.json
├── abc-edit-div.json
├── abc-file-view-v2.json
├── abc-flex.json
├── abc-form.json
├── abc-grid.json
├── abc-icon.json
├── abc-image.json
├── abc-input-mobile.json
├── abc-input-number.json
├── abc-input-password.json
├── abc-input-style.json
├── abc-input-tag.json
├── abc-input.json
├── abc-layout.json
├── abc-link.json
├── abc-list.json
├── abc-loading.json
├── abc-modal.json
├── abc-notice.json
├── abc-option-card.json
├── abc-oss-img.json
├── abc-pagination.json
├── abc-popover.json
├── abc-preview.json
├── abc-progress.json
├── abc-qr-code.json
├── abc-radio-button.json
├── abc-radio.json
├── abc-result.json
├── abc-scrollbar.json
├── abc-search-icon.json
├── abc-select-input.json
├── abc-select.json
├── abc-space.json
├── abc-statistic.json
├── abc-step.json
├── abc-steps.json
├── abc-suggestions-panel.json
├── abc-switch.json
├── abc-table.json
├── abc-tabs-v2.json
├── abc-tag-v2.json
├── abc-text.json
├── abc-textarea.json
├── abc-time-picker.json
├── abc-time-range-picker.json
├── abc-tips-card-v2.json
├── abc-tips.json
├── abc-toast.json
├── abc-tooltip-info.json
├── abc-tooltip.json
├── abc-transfer-v2.json
├── abc-tree-v2.json
└── abc-week-pagination.json


#### 重要说明
始终使用 ABC UI 组件库中提供的组件完成代码编写，当你不知道组件用法时，索引references/abc-ui/中的文档，或者其他文件中的类似用法，禁止使用开源组件库例如 element-ui 等！

对应的模块有各自的开发规范，具体如下：
### 设置模块
当开发设置页面时，参考文档 [[references/docs/设置界面开发规范.md]]

## 编码风格及规范

- [强制] css 类取名原则上要包含 业务属性 || 自身特色属性，例如门诊西药处方： outpatient-western-prescription-[warpper|container|table]，弹窗类挂载到body上的组件，一定要定义唯一的class名字，防止全局污染
- [强制] 文件名使用 kebab-case 命名，template 中使用 vue 组件，也使用 kebab-case 命名，例如: <abc-button></abc-button>
- [强制] get请求不能使用参数拼接的形式，需要序列化参数
- [强制] 变量 使用 Camel命名法。（驼峰）
- [强制] 常量 使用 全部字母大写，单词间下划线分隔 的命名方式。
- [强制] 函数 使用 Camel命名法。
- [强制] 函数的 参数 使用 Camel命名法。
- [强制] 类 使用 Pascal命名法。（首字母大写）
- [强制] 类的 方法 / 属性 使用 Camel命名法。
- [强制] 枚举变量 使用 Pascal命名法，枚举的属性 使用 全部字母大写，单词间下划线分隔 的命名方式。
- [强制] 由多个单词组成的缩写词，在命名中，根据当前命名法和出现的位置，所有字母的大小写与首字母的大小写保持一致。
- [强制] 类名 使用 名词。
- [建议] 函数名 使用 动宾短语。
- [建议] boolean 类型的变量使用 is 或 has 开头。

## 开发流程
- 根据用户需求，分析需要使用的 abc-ui 组件
- 当你不知道组件的用法时，依次索引 references/abc-ui/ 中的文档，直到你了解所需组件的用法
- 根据需求，在 src/ui-playground.vue 中生成代码

## 约束
- 始终使用 ABC UI 组件库中提供的组件完成代码编写，当你不知道组件用法时，索引其他文件中的类似用法，禁止使用开源组件库例如 element-ui 等！
- 需要使用色值时，优先使用 src/styles/var.scss 以及 @abc/ui-pc/theme/common/var.scss 中定义的 css 变量
- 始终用中文回答
