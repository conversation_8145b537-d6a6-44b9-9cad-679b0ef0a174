{"name": "@abc-figma/vue-playground", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@abc/constants": "1.22.7", "@abc/pc-components": "^1.0.7", "@abc/sortable": "^1.3.170", "@abc/ui-pc": "^1.432.6", "@abc/utils": "^1.2.997", "@abc/utils-date": "^1.2.998", "@abc/utils-dom": "^1.7.351", "core-js": "^3.8.3", "jquery": "^3.7.1", "sass": "^1.77.6", "sass-loader": "^14.2.1", "vue": "^2.7.14", "vue-i18n": "^8.28.2", "vuedraggable": "^2.24.3"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.6", "@vue/cli-plugin-eslint": "~5.0.6", "@vue/cli-service": "~5.0.6", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}