import fs from 'fs';
import path from 'path';
import babelParser from '@babel/parser';
import babelTraverse from '@babel/traverse';
import { unified } from 'unified';
import remarkParse from 'remark-parse';
import remarkMdx from 'remark-mdx';
import { visitParents } from 'unist-util-visit-parents';

// 解析 MDX 文件为 MDAST
async function parseMdxToMdast(mdxFilePath) {
    const mdxContent = fs.readFileSync(mdxFilePath, 'utf-8');

    try {
        const mdast = await unified()
            .use(remarkParse)
            .use(remarkMdx)
            .parse(mdxContent);

        return mdast;
    } catch (error) {
        console.error('Error parsing MDX to MDAST:', error);
    }
}

// 解析 MDAST，提取所有的 Story 引用和对应的 import 模块
function extractStoriesAndImportsFromMdast(mdast) {
    const storyReferenceList = [];
    const imports = [];
    let description = '';

    visitParents(mdast, (node, ancestors) => {
        // 提取 mdxjsEsm 节点对应的 import 语句
        if (node.type === 'mdxjsEsm') {
            imports.push(node.value);
        }

        if (node.type === 'blockquote' && node.children.length > 0 && !description && node.children[0].type === 'paragraph' && node.children[0].children.length > 0 && node.children[0].children[0].type === 'text' && node.children[0].children[0].value) {
            description = node.children[0].children[0].value;
        }

        // 提取 <Story of={...}> 中的 `of` 属性
        if (node.type === 'mdxJsxFlowElement' && (node.name === 'Story' || node.name === 'Canvas')) {
            const ofAttribute = node.attributes.find(attr => attr.name === 'of');
            if (ofAttribute && ofAttribute.value) {
                // 检查 ofAttribute.value 的类型
                if (ofAttribute.value.type === 'mdxJsxAttributeValueExpression') {
                    // 获取表达式内容
                    const expressionBody = ofAttribute.value.data.estree.body[0].expression;

                    if (expressionBody.type === 'MemberExpression') {
                        const objectName = expressionBody.object.name; // TagStories
                        const propertyName = expressionBody.property.name; // $应用场景
                        storyReferenceList.push({ objectName, propertyName });
                    }
                }
            }
        }
    });

    return { storyReferenceList, imports, description };
}

// 解析 import 语句，找到组件文件的路径
function extractJsFilePathFromImports(imports, componentImportName, baseDir) {
    for (const importStatement of imports) {
        const match = new RegExp(`import \\* as ${componentImportName} from ['"](.*)['"];`).exec(importStatement);
        if (match && match[1]) {
            let fileName = match[1];
            if (!fileName.endsWith('.js')) {
                fileName += '.js';
            }
            const jsFilePath = path.resolve(baseDir, fileName);
            return jsFilePath;
        }
    }
    return null;
}

// 解析 JS 文件，提取组件的 template 内容
function extractExampleCodeFromJsFile(jsFilePath, componentName) {
    const jsCode = fs.readFileSync(jsFilePath, 'utf-8');

    const ast = babelParser.parse(jsCode, {
        sourceType: 'module',
        plugins: ['jsx', 'classProperties'],
    });

    let templateCode = null;

    babelTraverse.default(ast, {
        ExportNamedDeclaration(path) {
            const declaration = path.node.declaration;

            if (declaration && declaration.declarations) {
                declaration.declarations.forEach((declarator) => {
                    if (declarator.id.name === componentName) {
                        const init = declarator.init;

                        if (init) {
                            let componentObj = null;
                            if (init.type === 'ObjectExpression') {
                                // 找到 render 方法
                                const renderMethod = init.properties.find(prop =>
                                    prop.key.name === 'render'
                                );

                                if (renderMethod && renderMethod.value && renderMethod.value.type === 'ArrowFunctionExpression') {
                                    componentObj = renderMethod.value;
                                }
                            } else if (init.type === 'ArrowFunctionExpression') {
                                // 直接遍历 ArrowFunctionExpression.body 中的 ObjectExpression
                                if (init.body.type === 'ObjectExpression') {
                                    componentObj = init;
                                }
                            }

                            if (componentObj) {
                                // 直接遍历 componentObj.body 中的 ObjectExpression
                                if (componentObj.body.type === 'ObjectExpression') {
                                    componentObj.body.properties.forEach(property => {
                                        if (property.key.name === 'template') {
                                            // 检查 property.value 的类型
                                            if (property.value.type === 'TemplateLiteral') {
                                                // 提取 TemplateLiteral 的内容
                                                templateCode = property.value.quasis.map(quasi => quasi.value.cooked).join('');
                                            } else if (property.value.type === 'Literal') {
                                                templateCode = property.value.value; // 获取 Literal 内容
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    }
                });
            }
        }
    });
    return templateCode;
}

// 解析 MDX 文件，提取组件名、描述和对应的 Story
async function parseMdxToJson(mdxFilePath, baseDir) {
    try {
        // 读取 MDX 文件内容并转换为 MDAST
        const mdast = await parseMdxToMdast(mdxFilePath);
        const { storyReferenceList, imports, description } = extractStoriesAndImportsFromMdast(mdast);

        if (!storyReferenceList.length) {
            throw new Error('No story reference found in MDX', mdxFilePath);
        }

        const examples = [];

        // 遍历每个 Story 引用
        for (const storyReference of storyReferenceList) {
            const { objectName, propertyName } = storyReference;
            // 查找对应的 JS 文件路径
            const jsFilePath = extractJsFilePathFromImports(imports, objectName, baseDir);
            if (!jsFilePath) {
                console.error(`JS file for ${objectName} not found.`, mdxFilePath);
                continue;
            }

            // 提取 JS 文件中的组件代码
            const templateCode = extractExampleCodeFromJsFile(jsFilePath, propertyName);
            if (!templateCode) {
                console.error(`No template code found for ${propertyName}`, mdxFilePath);
                continue;
            }

            // 构建 JSON 对象
            examples.push({
                example: propertyName,
                code: templateCode.trim().replace(/ {4}/g, '  '),
                // jsFilePath
            });
        }

        return {
            description,
            examples,
        };
    } catch (error) {
        console.error('Error parsing MDX to JSON:', error, mdxFilePath);
    }
}

const baseDir = '/Users/<USER>/Dev/code/abc/abc-fed-common/packages/ui-pc/src';
// {
//   "address-selector":  "./src/address-selector/index.js",
//   "autocomplete":  "./src/autocomplete/index.js",
//   "badge":  "./src/badge/index.js",
//   "button":  "./src/button/index.js",
//   "button-group":  "./src/button-group/index.js",
//   "button-pagination":  "./src/button-pagination/index.js",
//   "cascader":  "./src/cascader/index.js",
//   "chart":  "./src/chart/index.js",
//   "checkbox":  "./src/checkbox/index.js",
//   "checkbox-button":  "./src/checkbox-button/index.js",
//   "checkbox-group":  "./src/checkbox-group/index.js",
//   "content-empty":  "./src/content-empty/index.js",
//   "date-pagination":  "./src/date-pagination/index.js",
//   "date-picker":  "./src/date-picker/index.js",
//   "date-picker-bar":  "./src/date-picker-bar/index.js",
//   "delete-icon":  "./src/delete-icon/index.js",
//   "descriptions":  "./src/descriptions/index.js",
//   "dialog":  "./src/dialog/index.js",
//   "divider":  "./src/divider/index.js",
//   "dropdown":  "./src/dropdown/index.js",
//   "dropdown-item":  "./src/dropdown-item/index.js",
//   "edit-div":  "./src/edit-div/index.js",
//   "file-view":  "./src/file-view/index.js",
//   "file-view-v2":  "./src/file-view-v2/index.js",
//   "flex": "./src/flex/index.js",
//   "form":  "./src/form/index.js",
//   "form-item":  "./src/form-item/index.js",
//   "grid":  "./src/grid/index.js",
//   "icon":  "./src/icon/index.js",
//   "image":  "./src/image/index.js",
//   "input":  "./src/input/index.js",
//   "input-mobile":  "./src/input-mobile",
//   "input-number":  "./src/input-number",
//   "input-password":  "./src/input-password",
//   "input-style":  "./src/input-style",
//   "input-tag":  "./src/input-tag",
//   "layout":  "./src/layout/index.js",
//   "link": "./src/link/index.js",
//   "list": "./src/list",
//   "loading":  "./src/loading/index.js",
//   "modal":  "./src/modal/index.js",
//   "notice":  "./src/notice/index.js",
//   "option":  "./src/option/index.js",
//   "option-card":  "./src/option-card/index.js",
//   "option-group":  "./src/option-group",
//   "oss-img":  "./src/oss-img",
//   "pagination":  "./src/pagination/index.js",
//   "popover":  "./src/popover/index.js",
//   "preview":  "./src/preview/index.js",
//   "progress":  "./src/progress/index.js",
//   "qr-code":  "./src/qr-code",
//   "radio":  "./src/radio/index.js",
//   "radio-button":  "./src/radio-button",
//   "radio-group":  "./src/radio-group",
//   "result": "./src/result",
//   "scrollbar": "./src/scrollbar",
//   "search-icon":  "./src/search-icon",
//   "select":  "./src/select/index.js",
//   "select-input": "./src/select-input/index.js",
//   "space":  "./src/space/index.js",
//   "statistic":  "./src/statistic/index.js",
//   "step":  "./src/step/index.js",
//   "steps":  "./src/steps/index.js",
//   "suggestions-panel": "./src/suggestions-panel/index.js",
//   "switch":  "./src/switch/index.js",
//   "table":  "./src/table/index.js",
//   "table-common":  "./src/table-common/index.js",
//   "table-fixed2":  "./src/table-fixed2/index.js",
//   "tabs":  "./src/tabs/index.js",
//   "tabs-v2":  "./src/tabs-v2/index.js",
//   "tag":  "./src/tag/index.js",
//   "tag-v2":  "./src/tag-v2/index.js",
//   "text":  "./src/text/index.js",
//   "textarea":  "./src/textarea/index.js",
//   "tips":  "./src/tips/index.js",
//   "tips-card":  "./src/tips-card/index.js",
//   "tips-card-v2":  "./src/tips-card-v2/index.js",
//   "toast":  "./src/toast/index.js",
//   "tooltip":  "./src/tooltip/index.js",
//   "tooltip-info": "./src/tooltip-info/index.js",
//   "transfer":  "./src/transfer/index.js",
//   "transfer-v2":  "./src/transfer-v2/index.js",
//   "tree":  "./src/tree/index.js",
//   "tree-v2": "./src/tree-v2/index.js"
// }
import components from './components.json' assert { type: "json" };

// 调用转换函数并输出 JSON
(async () => {
    let resultJson = [];
    for (const [componentName, componentPath] of Object.entries(components)) {

        const mdxFilePath = path.join(baseDir, componentName, `./${componentName}.mdx`);

        if (!fs.existsSync(mdxFilePath)) {
            console.warn(`MDX file for ${componentName} not found.`);
            continue;
        }

        const componentBaseDir = path.dirname(mdxFilePath);

        const {description, examples} = await parseMdxToJson(mdxFilePath, componentBaseDir);
        resultJson.push({
            name: componentName,
            description,
            examples,
        });
    }


    // 输出 JSON 文件
    if (resultJson) {
        fs.writeFileSync('metadata.json', JSON.stringify(resultJson, null, 2));
        console.log('Conversion complete, check component.json for output.');
    }
})();
