Index: index.js
===================================================================
diff --git a/index.js b/index.js
deleted file mode 100644
--- a/index.js	
+++ /dev/null	
@@ -1,137 +0,0 @@
-import fs from 'fs';
-import path from 'path';
-import { compile } from '@mdx-js/mdx';
-import babelParser from '@babel/parser';
-import babelTraverse from '@babel/traverse';
-
-// 解析编译后的 MDX 代码，提取 <Story> 中的 "of" 属性
-function extractStoryFromMdx(mdxCompiledCode) {
-    // 解析 MDX 编译后的代码为 AST
-    const ast = babelParser.parse(mdxCompiledCode, {
-        sourceType: 'module',
-        plugins: ['jsx', 'objectRestSpread'],
-    });
-
-    let storyReference = '';
-
-    // 遍历 AST，查找 Canvas 中的 Story
-    babelTraverse.default(ast, {
-        CallExpression(path) {
-            const callee = path.get('callee');
-            // 确保当前调用的是 _jsx 并且是 Canvas 组件
-            if (callee.node.name === '_jsx' && path.node.arguments.length > 0) {
-                const canvasComponent = path.node.arguments[0];
-
-                if (canvasComponent.type === 'Identifier' && canvasComponent.name === 'Canvas') {
-                    // 遍历 Canvas 中的子元素
-                    const children = path.get('arguments.1'); // 第二个参数是 children
-
-                    if (children && children.node.type === 'ArrayExpression') {
-                        children.get('elements').forEach(element => {
-                            // 找到 Story 组件
-                            if (element.node.type === 'CallExpression') {
-                                const storyCallee = element.get('callee');
-                                if (storyCallee.node.name === '_jsx' && element.node.arguments.length > 0) {
-                                    const storyComponent = element.node.arguments[0];
-                                    if (storyComponent.type === 'Identifier' && storyComponent.name === 'Story') {
-                                        // 查找 of 属性
-                                        const storyProps = element.get('arguments.1').node;
-                                        if (storyProps && storyProps.type === 'ObjectExpression') {
-                                            const ofProperty = storyProps.properties.find(prop => prop.key.name === 'of');
-                                            if (ofProperty && ofProperty.value && ofProperty.value.type === 'MemberExpression') {
-                                                storyReference = ofProperty.value.property.name; // 提取 $应用场景
-                                            }
-                                        }
-                                    }
-                                }
-                            }
-                        });
-                    }
-                }
-            }
-        },
-    });
-
-    return storyReference;
-}
-// 解析 JS 文件以提取组件实例代码
-function extractTemplateFromJs(jsCode, storyName) {
-    const ast = babelParser.parse(jsCode, { sourceType: 'module', plugins: ['jsx', 'vue'] });
-    let templateCode = '';
-
-    // 遍历 AST 找到和 storyName 对应的对象
-    babelTraverse.default(ast, {
-        ExportNamedDeclaration(path) {
-            const declaration = path.node.declaration.declarations?.[0];
-            if (declaration && declaration.id.name === storyName) {
-                const properties = declaration.init.properties;
-                properties.forEach((property) => {
-                    if (property.key.name === 'render') {
-                        const templateProperty = property.value.body.properties.find(
-                            (prop) => prop.key.name === 'template'
-                        );
-                        if (templateProperty) {
-                            templateCode = templateProperty.value.quasis?.[0].value.raw;
-                        }
-                    }
-                });
-            }
-        },
-    });
-
-    return templateCode;
-}
-
-// 解析 MDX 文件，提取组件名、描述和对应的 Story
-async function parseMdxToJson(mdxFilePath, jsFilePath) {
-    try {
-        // 读取 MDX 文件内容
-        const mdxContent = fs.readFileSync(mdxFilePath, 'utf-8');
-
-        // 编译 MDX 内容
-        const compiled = await compile(mdxContent);
-        const compiledText = compiled.toString();
-
-        // 提取组件名和描述
-        const componentName = /#\s*(\w+)/.exec(mdxContent)?.[1];
-        const description = /定义：(.*?)\n/.exec(mdxContent)?.[1];
-
-        // 提取 Story 引用
-        const storyName = extractStoryFromMdx(compiledText);
-        if (!storyName) {
-            throw new Error('No story reference found in MDX');
-        }
-
-        // 读取 JS 文件并提取相应的 <template> 部分代码
-        const jsContent = fs.readFileSync(jsFilePath, 'utf-8');
-        const templateCode = extractTemplateFromJs(jsContent, storyName);
-
-        // 构建 JSON 对象
-        const componentJson = {
-            componentName,
-            description,
-            storyName,
-            exampleCode: templateCode,
-        };
-
-        return componentJson;
-    } catch (error) {
-        console.error('Error parsing MDX to JSON:', error);
-    }
-}
-
-const baseDir = '/Users/<USER>/Dev/code/abc/abc-fed-common/packages/ui-pc/src/tag-v2';
-
-// 调用转换函数并输出 JSON
-(async () => {
-    const mdxFilePath = path.join(baseDir, 'tag.mdx');
-    const jsFilePath = path.join(baseDir, 'tag.stories.js');
-
-    const resultJson = await parseMdxToJson(mdxFilePath, jsFilePath);
-
-    // 输出 JSON 文件
-    if (resultJson) {
-        fs.writeFileSync('component.json', JSON.stringify(resultJson, null, 2));
-        console.log('Conversion complete, check component.json for output.');
-    }
-})();
Index: .gitignore
===================================================================
diff --git a/.gitignore b/.gitignore
deleted file mode 100644
--- a/.gitignore	
+++ /dev/null	
@@ -1,26 +0,0 @@
-.DS_Store
-node_modules/
-static/ckeditor
-
-npm-debug.log
-test/unit/coverage
-test/e2e/reports
-selenium-debug.log
-.idea/
-/.cache/
-/yarn-error.log
-
-.vscode/
-.vs/
-
-local
-local/**/dist
-**/node_modules
-
-**/package-lock.json
-
-./nodemon.json
-/dist
-storybook-static
-
-./lerna-debug.log
