{"name": "parse-storybook", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@babel/parser": "^7.25.6", "@babel/traverse": "^7.25.6", "@mdx-js/mdx": "^3.0.1", "fs": "0.0.1-security", "path": "^0.12.7", "recast": "^0.23.9", "remark": "^15.0.1", "remark-mdx": "^3.0.1", "remark-parse": "^11.0.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "unist-util-visit-parents": "^6.0.1"}}