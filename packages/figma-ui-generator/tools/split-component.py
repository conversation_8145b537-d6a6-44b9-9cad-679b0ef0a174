import json
import os

# 读取 JSON 文件
with open('web-types.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 创建输出目录（如果不存在）
output_dir = '../web-types'
os.makedirs(output_dir, exist_ok=True)

# 获取 contributions 中的 html 标签部分
tags = data.get('contributions', {}).get('html', {}).get('tags', [])

# 遍历每个组件，并将其拆分成单独的文件
for tag in tags:
    # 获取组件名
    component_name = tag.get('name')
    
    # 组件的详细信息
    component_content = {
        "name": component_name,
        "description": tag.get('description', ''),
        "attributes": tag.get('attributes', []),
        "events": tag.get('events', []),
        "slots": tag.get('slots', []),
        "source": tag.get('source', {})
    }

    # 生成文件路径，使用组件名作为文件名
    file_path = os.path.join(output_dir, f'{component_name}.json')
    
    # 将组件内容写入独立的 JSON 文件
    with open(file_path, 'w', encoding='utf-8') as out_file:
        json.dump(component_content, out_file, indent=4, ensure_ascii=False)

    print(f'已将组件 {component_name} 写入 {file_path}')
