/* Colors
-------------------------- */
:root {
/*主题色*/
--abc-color-theme1: #005ed9;
    --abc-color-theme2: #0090ff;
    --abc-color-theme3: #459eff;
    --abc-color-theme4: #c6e2ff;
    --abc-color-theme5: #a2cfff;
    --abc-color-theme6: #80bdff;
    --abc-color-theme7: #f4f7ff;
    --abc-color-theme8: #e0efff;
    --abc-color-B1: #005ed9;
    --abc-color-B2: #2680f7;
    --abc-color-B3: #5199f8;
    --abc-color-B4: #e9f2fe;
    --abc-color-B5: #d4edfd;
    --abc-color-B6: #85baff;
    --abc-color-B7: #f1f6fe;
    --abc-color-B8: #497bb7;

/*卡片及辅助*/
--abc-color-S1: #000000;
    --abc-color-S2: #ffffff;
    --abc-color-S3: #2680f7;
    --abc-color-S4: #385068;

/* 文字色*/
--abc-color-T1: #000000;
    --abc-color-T2: #7a8794;
    --abc-color-T3: #aab4bf;
    --abc-color-T4: #ffffff;
    --abc-color-T5: rgba(255, 255, 255, 0.8);
    --abc-color-T6: rgba(255, 255, 255, 0.2);

/* 界面色*/
--abc-color-P1: #e0e2eb;
    --abc-color-P2: #e6eaee;
    --abc-color-P6: #e6eaee;
    --abc-color-P3: #dadbe0;
    --abc-color-P4: #eff3f6;
    --abc-color-P5: #f5f7fb;
    --abc-color-P7: #e0e5ee;
    --abc-color-P8: #eaedf1;
    --abc-color-P9: #e0e2eb;
    --abc-color-P10: #ced0da;

/* 装饰色*/
--abc-color-R1: #e52d5b;
    --abc-color-R2: #ff3366;
    --abc-color-R3: #ff5b84;
    --abc-color-R4: #ffeaef;
    --abc-color-R5: #ffd6e0;
    --abc-color-R6: #f04a3e;
    --abc-color-R7: #D72E22;
    --abc-color-Y1: #e5892d;
    --abc-color-Y2: #ff9933;
    --abc-color-Y3: #ffad5b;
    --abc-color-Y4: #fff4ea;
    --abc-color-Y5: #ffebd6;
    --abc-color-Y6: #6e5a46;
    --abc-color-G1: #08a446;
    --abc-color-G2: #1ec761;
    --abc-color-G3: #23cf67;
    --abc-color-G4: #e3fced;
    --abc-color-G5: #bbf2d1;
    --abc-color-O1: #e65f20;
    --abc-color-O2: #ff793b;
    --abc-color-O3: #ff9563;
    --abc-color-OR1: #f3661f;

/*报表配色 -- chart 配色参照*/
--abc-color-C1: #fd9800;
    --abc-color-C2: #0a8cea;
    --abc-color-C3: #67ce0e;
    --abc-color-C4: #ff6464;
    --abc-color-C5: #fec166;
    --abc-color-C6: #6cbaf2;

/* 柠檬黄色，用以定义，门诊文书背景黄、医嘱表格选中黄、医嘱表格选中悬停黄、popover 背景黄、popover 描边黄*/
--abc-color-LY1: #eeecd6;
    --abc-color-LY2: #fffab9;
    --abc-color-LY3: #fffde0;
    --abc-color-LY4: #fffdec;
    --abc-color-LY5: rgba(120, 108, 0, 0.1);
    --abc-color-AbcDivGrey: #f9fafc;

/*标签配色*/
--abc-color-PU1: #722ed1;
    --abc-color-PU2: #ddbef6;
    --abc-color-PU3: #f5e8ff;
    --abc-color-CY1: #40c6c2;
    --abc-color-CY2: #c1f4eb;
    --abc-color-CY3: #e8fffb;
    --abc-color-MA1: #f5319d;
    --abc-color-MA2: #fdc2db;
    --abc-color-MA3: #ffe8f1;
    --abc-color-LI1: #9fdb1d;
    --abc-color-LI2: #edf8bb;
    --abc-color-LI3: #fcffe8;
    --abc-color-GO1: #f7ba1e;
    --abc-color-GO2: #fdf4bf;
    --abc-color-GO3: #fffce8;
    --abc-color-PI1: #d91ad9;
    --abc-color-PI2: #f7baef;
    --abc-color-PI3: #ffe8fb;
    --abc-color-YE1: #fadc19;
    --abc-color-YE2: #fcf26b;
    --abc-color-YE3: #feffe8;
    --abc-color-BR1: #006286;
    --abc-color-BR2: #c4e8ff;
    --abc-color-BR3: #e8f5ff;
    --abc-color-DP1: #7b0074;
    --abc-color-DP2: #f7baef;
    --abc-color-DP3: #ffe8fb;
    --abc-color-DB1: #00118f;
    --abc-color-DB2: #bbc2fd;
    --abc-color-DB3: #f0f1ff;

    /* z-index
    -------------------------- */
    --abc-z-index-index1: 1;
    --abc-z-index-index2: 10;
    --abc-z-index-index3: 100;
    --abc-z-index-index4: 1000;
    --abc-z-index-index5: 10000;

/* 设计定的背景色*/
--abc-color-cp-white: #ffffff;
    --abc-color-cp-grey1: #fcfdfe; /*组件背景浅灰*/
--abc-color-cp-grey2: #f9fafc; /*组件禁用&部分组件卡片背景*/
--abc-color-cp-grey3: #f5f7fb; /*组件背景深灰*/
--abc-color-cp-grey4: #f2f4f7; /*组件悬停灰*/
--abc-color-div-white: #ffffff; /*容器背景白*/
--abc-color-div-grey: #f9fafc; /*容器背景灰*/

/* 前端使用*/
--abc-color-bg-main: #f5f5f5; /*主背景色*/
--abc-color-bg-section: var(--abc-color-cp-white); /* 区域背景色*/
--abc-color-bg-disabled: #f9fafc; /* 禁用背景色*/
--abc-color-layout-divider-color: var(--abc-color-P1); /* 框架分割线颜色*/
--abc-color-card-border-color: var(--abc-color-P7); /* 卡片类封边颜色*/
--abc-color-card-divider-color: var(--abc-color-P6); /* 内部分割线颜色*/

/* 皮肤色*/
--abc-color-skin-firmament: #e8f1ff; /* 苍穹蓝*/
--abc-color-skin-lakeLight: #56739c; /* 湖光蓝*/
--abc-color-skin-obsidian: #545e70; /* 曜石灰*/

/* 表格*/
--abc-color-bg-table-header: var(--abc-color-cp-white);
    --abc-table-cell-height-small: 32px;
    --abc-table-cell-height-default: 40px;
    --abc-table-cell-height-large: 48px;
    --abc-table-cell-height-xlarge: 56px;
    --abc-table-cell-height-xxlarge: 64px;
    --abc-table-cell-height-xxxlarge: 72px;
    --abc-table-header-height-small: 26px;
    --abc-table-header-height-default: 36px;
    --abc-table-header-height-large: 40px;
    --abc-table-cell-padding-default: 8px;
    --abc-table-cell-padding-large: 12px;
    --abc-color-bg-table-tr-hover: var(--abc-color-cp-grey4);
    --abc-color-bg-table-tr-selected: var(--abc-color-B7);
    --abc-color-bg-table-tr-selected-hover: var(--abc-color-B4);
    --abc-color-bg-table-tr-checked: var(--abc-color-LY3);
    --abc-color-bg-table-tr-checked-hover: var(--abc-color-LY2);

/* 字号*/
--abc-font-size-tiny: 10px;
    --abc-font-size-mini: 12px;
    --abc-font-size-small: 13px;
    --abc-font-size-normal: 14px;
    --abc-font-size-large: 16px;
    --abc-font-size-xlarge: 24px;
    --abc-font-size-xxlarge: 28px;
    --abc-font-size-xxxlarge: 32px;

/* 行高*/
--abc-line-height-mini: 16px;
    --abc-line-height-tiny: 16px;
    --abc-line-height-small: 16px;
    --abc-line-height-normal: 22px;
    --abc-line-height-large: 24px;
    --abc-line-height-xlarge: 32px;
    --abc-line-height-xxlarge: 40px;
    --abc-line-height-xxxlarge: 44px;

/* 高度*/
--abc-height-tiny: 18px;
    --abc-height-small: 20px;
    --abc-height-normal: 20px;
    --abc-height-large: 22px;

/* padding*/
--abc-padding-tiny: 4px 6px;
    --abc-padding-small: 4px 8px;
    --abc-padding-normal: 6px 8px;
    --abc-padding-large: 9px 10px;

/* Padding Top&Bottom 组件上下边距*/
--abc-paddingTB-xs: 2px;
    --abc-paddingTB-s: 4px;
    --abc-paddingTB-sm: 6px;
    --abc-paddingTB-m: 8px;
    --abc-paddingTB-ml: 10px;
    --abc-paddingTB-l: 12px;
    --abc-paddingTB-xl: 16px;
    --abc-paddingTB-xll: 20px;
    --abc-paddingTB-xxl: 24px;
    --abc-paddingTB-xxxl: 32px;
    --abc-paddingTB-xxxxl: 40px;
    --abc-paddingTB-xxxxxl: 56px;

/* Padding Left&Right 组件左右边距*/
--abc-paddingLR-xs: 2px;
    --abc-paddingLR-s: 4px;
    --abc-paddingLR-sm: 6px;
    --abc-paddingLR-m: 8px;
    --abc-paddingLR-ml: 10px;
    --abc-paddingLR-l: 12px;
    --abc-paddingLR-xl: 16px;
    --abc-paddingLR-xll: 20px;
    --abc-paddingLR-xxl: 24px;
    --abc-paddingLR-xxxl: 32px;
    --abc-paddingLR-xxxxl: 40px;
    --abc-paddingLR-xxxxxl: 56px;

/* Pop Padding 弹出层上下左右边距*/
--abc-pop-padding-xs: 2px;
    --abc-pop-padding-s: 4px;
    --abc-pop-padding-m: 8px;
    --abc-pop-padding-ml: 10px;
    --abc-pop-padding-l: 12px;
    --abc-pop-padding-xl: 16px;
    --abc-pop-padding-xxl: 24px;
    --abc-pop-padding-xxxl: 32px;

/* Space 组件间距*/
--abc-space-xs: 2px;
    --abc-space-s: 4px;
    --abc-space-sm: 6px;
    --abc-space-m: 8px;
    --abc-space-ml: 10px;
    --abc-space-l: 12px;
    --abc-space-xl: 16px;
    --abc-space-xll: 20px;
    --abc-space-xxl: 24px;
    --abc-space-xxxl: 32px;
    --abc-space-xxxxl: 40px;
    --abc-space-xxxxxl: 56px;

/* Size 尺寸*/
--abc-size-xxxs: 16px;
    --abc-size-xxs: 20px;
    --abc-size-xs: 24px;
    --abc-size-s: 28px;
    --abc-size-m: 32px;
    --abc-size-l: 40px;
    --abc-size-xl: 44px;
    --abc-size-xxl: 48px;
    --abc-size-xxxl: 56px;
    --abc-size-xxxxl: 64px;
    --abc-size-xxxxxl: 72px;

    /* Box-shadow
    -------------------------- */
    --abc-shadow-1: 0 3px 12px 2px rgba(0, 0, 0, 0.1);
    --abc-shadow-2: 0 1px 3px 0 rgba(0, 0, 0, 0.03);
    --abc-box-shadow-header: 0 0 4px 0 rgba(0, 0, 0, 0.2);
    --abc-box-shadow-card: 0 2px 8px 0 rgba(0, 0, 0, 0.15);
    --abc-box-shadow-dark: 0 4px 8px 0 rgba(0, 0, 0, 0.05);
    --abc-box-shadow-content: 0 2px 8px 0 rgba(0, 0, 0, 0.15);

    /* Transition
    -------------------------- */
    --abc-transition-fade: opacity 300ms cubic-bezier(0.23, 1, 0.32, 1);
    --abc-transition-fade-linear: opacity 200ms linear;
    --abc-transition-md-fade: transform 300ms cubic-bezier(0.23, 1, 0.32, 1) 100ms, opacity 300ms cubic-bezier(0.23, 1, 0.32, 1) 100ms;
    --abc-transition-border-base: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    --abc-transition-color-base: color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    /* Gradient
    -------------------------- */
    --abc-gradient-input-base: linear-gradient(180deg, rgba(255, 255, 255, 1), rgba(242, 244, 247, 1));

/* 边框圆角*/
--abc-border-radius-none: 0;
    --abc-border-radius-mini: 4px;
    --abc-border-radius-small: 6px;
    --abc-border-radius-medium: 8px;
    --abc-border-radius-large: 12px;
    --abc-border-radius-huge: 50px;
    --abc-container-border-radius: 4px;
    --abc-dialog-border-radius: 5px;

/* dialog 弹窗*/
--abc-dialog-header-min-height: 41px;
    --abc-dialog-footer-min-height: 56px;
    --abc-dialog-header-bg: #eff1f5;
    --abc-dialog-header-close-hover-bg: var(--abc-color-P8);
    --abc-dialog-header-close-active-bg: var(--abc-color-P7);

/* popover 弹出层*/
--abc-popover-box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15);

/* abc-layout 布局*/
--abc-layout-header-padding: 0 24px;
    --abc-layout-content-padding: 16px 24px;

/* input prepend宽度*/
--abc-input-prepend-width: 30px;
    --abc-input-prepend-width-tiny: 30px;
    --abc-input-prepend-width-small: 30px;
    --abc-input-prepend-width-medium: 40px;
    --abc-input-prepend-width-large: 40px;

/* select prepend宽度*/
--abc-select-prepend-width: 30px;
    --abc-select-prepend-width-tiny: 30px;
    --abc-select-prepend-width-small: 30px;
    --abc-select-prepend-width-medium: 40px;
    --abc-select-prepend-width-large: 40px;
}

:root[data-abc-theme="default"] {
    --abc-border-radius-small: 4px;
}

:root[data-abc-theme="pharmacy"] {
    --abc-border-radius-small: 6px;
    --abc-container-border-radius: 8px;
    --abc-color-layout-divider-color: var(--abc-color-P8);
    --abc-color-bg-table-header: var(--abc-color-cp-grey3);
    --abc-table-disabled-input-bg: transparent;
    --abc-dialog-border-radius: 8px;
    --abc-dialog-header-bg: var(--abc-color-cp-white);
    --abc-dialog-header-min-height: 56px;
    --abc-dialog-header-close-hover-bg: var(--abc-color-cp-white);
    --abc-dialog-header-close-active-bg: var(--abc-color-cp-white);
    --abc-popover-box-shadow: var(--abc-shadow-1);
    --abc-layout-header-padding: 0 20px;
    --abc-layout-content-padding: 20px;
    --abc-input-prepend-width: 32px;
    --abc-input-prepend-width-tiny: 26px;
    --abc-input-prepend-width-small: 28px;
    --abc-input-prepend-width-medium: 40px;
    --abc-input-prepend-width-large: 40px;
}
