<!DOCTYPE html>
<html>

<head>
    <link href="https://cdn.jsdelivr.net/npm/monaco-editor@0.36.0/min/vs/editor/editor.main.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./ui-pc.var.css">
    <link rel="stylesheet" href="./ui-pc.min.css">
    <style>
        body,
        html {
            margin: 0;
            padding: 0;
            height: 100vh;
        }

        #app {
            height: 100vh;
            display: flex;
        }

        #editor-container,
        #preview {
            height: 100%;
        }

        #preview {
            padding: 20px;
            overflow: auto;
        }

        .loading-indicator {
            color: #666;
            padding: 20px;
        }
    </style>
</head>

<body>
    <div id="app">
        <div id="editor-container"></div>
        <div id="preview">
            <div class="loading-indicator">初始化中...</div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/vue@2.7.14/dist/vue.min.js"></script>
    <!-- 关键修正：使用正确的编译器链接和全局变量 -->
    <script src="https://cdn.jsdelivr.net/npm/vue-template-compiler@2.7.14/browser.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://unpkg.com/@babel/standalone@7.22.15/babel.min.js"></script>
    <script src="./ui-pc.umd.js"></script>
<!--    <script src="./pc-components.umd.js"></script>-->
    <script src="https://static-common-cdn.abcyun.cn/iconfont/pc/font_4448419_ik71xl7tokr/iconfont.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/split.js@1.6.5/dist/split.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.36.0/min/vs/loader.js"></script>


    <script>
        // 全局注册组件库
        Vue.use(UIPc.default);
        // Vue.use(PcComponents.default);
        UIPc.setAbcTheme('pharmacy')

        // 声明编译器全局变量
        const VueTemplateCompiler = window.VueTemplateCompiler;

        // 编辑器初始化状态管理
        let editor = null;
        let isEditorReady = false;
        let updateTimeout = null;

        // 初始化Monaco编辑器
        require.config({
            paths: {
                vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.36.0/min/vs'
            },
            ignoreDuplicateModules: ['vs/editor/editor.main']
        });

        require(['vs/editor/editor.main'], function () {
            editor = monaco.editor.create(document.getElementById('editor-container'), {
                value: defaultSFCContent,
                language: 'html',
                theme: 'vs-dark',
                minimap: { enabled: false },
                automaticLayout: true
            });

            isEditorReady = true;
            safeUpdatePreview();

            editor.onDidChangeModelContent(() => {
                clearTimeout(updateTimeout);
                updateTimeout = setTimeout(safeUpdatePreview, 500);
            });
        });

        // 初始化分栏布局
        Split(['#editor-container', '#preview'], {
            sizes: [50, 50],
            minSize: 200,
            gutterSize: 8
        });

        // 默认示例代码
        const defaultSFCContent = `
<template>
  <div class="demo">
    <abc-button @click="count++">点击计数: {{ count }}</abc-button>
    <div>当前时间: {{ time }}</div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      count: 0,
      timer: null,
      time: ''
    }
  },
  mounted() {
    this.timer = setInterval(() => {
      this.time = new Date().toLocaleTimeString()
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(this.timer)
  }
}
<\/script>

<style>
.demo {
  padding: 20px;
  background: #f0f2f5;
  min-height: 100%;
}
</style>
`;

        // 预览处理相关变量
        let currentVueInstance = null;
        let styleElement = null;

        // 安全更新预览
        function safeUpdatePreview() {
            if (!isEditorReady) {
                showLoadingState();
                return;
            }
            updatePreview().catch(error => {
                showCompileError(error);
            });
        }

        // 核心更新逻辑
        async function updatePreview() {
            const sfcCode = editor.getValue();

            if (!sfcCode) {
                showEmptyState();
                return;
            }
            try {
                // 解析SFC组件
                const res = VueTemplateCompiler.parseComponent(sfcCode);
                const template = res.template.content;
                const script = res.script.content;

                // 编译模板
                const { render } = VueTemplateCompiler.compile(template, {
                    preserveWhitespace: false
                });

                // 处理Script部分
                const scriptContent = script
                    .replace(/export\s+default/, 'var __script = ')
                    .replace(/import\s+.*?\s+from\s+['"].*?['"];?/g, '');

                // Babel转换
                const { code } = Babel.transform(scriptContent, {
                    presets: ['es2015'],
                    plugins: ['transform-modules-commonjs']
                });

                // 执行脚本获取配置
                const scriptResult = new Function(`
                ${code}
                return typeof __script === 'undefined' ? {} : __script;
            `)();

                // 合并组件选项
                const componentOptions = {
                    ...scriptResult,
                    render: new Function(render)
                };

                // 清理旧实例
                if (currentVueInstance) {
                    currentVueInstance.$destroy();
                    currentVueInstance = null;
                }

                // 创建新实例
                currentVueInstance = new Vue(componentOptions);
                const previewContainer = document.getElementById('preview');
                previewContainer.innerHTML = '';
                currentVueInstance.$mount();
                previewContainer.appendChild(currentVueInstance.$el);

            } catch (error) {
                console.error('编译错误:', error);
                throw error;
            }
        }

        function showEmptyState() {
            const preview = document.getElementById('preview');
            preview.innerHTML = '<div class="loading-indicator">请输入SFC代码...</div>';
        }

        function showLoadingState() {
            const preview = document.getElementById('preview');
            preview.innerHTML = '<div class="loading-indicator">编辑器初始化中...</div>';
        }

        function showCompileError(error) {
            const preview = document.getElementById('preview');
            preview.innerHTML = `
            <div style="color: red; padding: 20px;">
                <h4>🚨 编译错误</h4>
                <pre>${error.stack || error.message}</pre>
            </div>
        `;
        }
    </script>
</body>

</html>
