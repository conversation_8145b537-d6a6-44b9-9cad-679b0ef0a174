const path = require('path');
const fs = require('fs');
// const csf2sfc = require('./csf2sfc');
const csf2sfc = require('./csf2sfc-v2');
const parseMdx = require('./parse-mdx');
const components = require('./components.json');
const dotenv = require('dotenv');
const envPath = path.join(__dirname, '../../../../.env');
dotenv.config({ path:  envPath});

const abcFedCommonDir = path.resolve(path.dirname(envPath), process.env.ABC_FED_COMMON_DIR);
const baseDir = path.join(abcFedCommonDir, 'packages/ui-pc/src');
const webTypeFile = require(path.join(abcFedCommonDir, 'packages/ui-pc/web-types.json'));

const abcPcCommonDir = path.resolve(path.dirname(envPath), process.env.ABC_PC_DIR);
const bizBaseDir = path.join(abcPcCommonDir, 'src/components-composite');
const bizWebTypeFile = require(path.join(abcPcCommonDir, 'src/components-composite/web-types.json'));

const resolvedWebTypes = webTypeFile.contributions.html.tags.reduce((acc, tag) => {
    const info = {
        attributes: tag.attributes?.map((attr) => {
            return {
                name: attr.name,
                description: attr.description,
                values: attr.values,
                default: attr.default,
            };
        }),
        slots: tag.slots?.map((slot) => {
            if (slot.description) {
                return {
                    name: slot.name,
                    description: slot.description,
                };
            }
            return slot.name;
        }),
        events: tag.events?.map((event) => {
            return event.name;
        }),
    };
    acc[tag.name] = info;
    return acc;
}, {});

const bizResolvedWebTypes = bizWebTypeFile.contributions.html.tags.reduce((acc, tag) => {
    const info = {
        attributes: tag.attributes?.map((attr) => {
            return {
                name: attr.name,
                description: attr.description,
                values: attr.values,
                default: attr.default,
            };
        }),
        slots: tag.slots?.map((slot) => {
            if (slot.description) {
                return {
                    name: slot.name,
                    description: slot.description,
                };
            }
            return slot.name;
        }),
        events: tag.events?.map((event) => {
            return event.name;
        }),
    };
    acc[tag.name] = info;
    return acc;
}, {});

async function main() {
    const allComponents = []; // 用于存储所有组件的信息

    for (const [componentName, componentPath] of Object.entries(components)) {
        let componentBaseDir;
        let mdxFilePath;
        let componentStoriesFilePath;
        let storyName;
        const isBizComponent = componentName.startsWith('biz-');

        if (isBizComponent) {
            componentBaseDir = path.join(bizBaseDir, componentName);
            mdxFilePath = path.join(componentBaseDir, `./index.mdx`);
            componentStoriesFilePath = path.join(componentBaseDir, `./index.stories.js`);
            storyName = '';
        }else{
            componentBaseDir = path.join(baseDir, componentName);
            mdxFilePath = path.join(componentBaseDir, `./${componentName}.mdx`);
            componentStoriesFilePath = path.join(componentBaseDir, `${componentName}.stories.js`);
            storyName = '';
            // console.log(`[${componentName}] -> ${componentStoriesFilePath}`);

            // 如果是子组件
            const isChildComponent = Object.prototype.toString.call(componentPath) === "[object Object]";
            if (isChildComponent) {
                mdxFilePath = path.join(baseDir, `./${componentPath.mdxPath}`);
                componentStoriesFilePath = path.join(baseDir, `${componentPath.storyPath}`);
                storyName = componentPath.storyName;
            }
        }



        let description = '';
        // 处理 mdx
        if (fs.existsSync(mdxFilePath)) {
            const { description: mdxDescription } = await parseMdx.parseMdxToJson(mdxFilePath);
            description = mdxDescription;
        }

        let code = '';
        try {
            code = fs.readFileSync(componentStoriesFilePath, 'utf-8');
        } catch (e) {
            console.warn(`[${componentName}] read failed`, componentStoriesFilePath);
            continue;
        }

        try {
            const outDir = path.join(__dirname, 'output');
            const outDirJson = path.join(__dirname, '../../../../references/abc-ui');
            if (!fs.existsSync(outDirJson)) {
                fs.mkdirSync(outDirJson, { recursive: true });
            }

            // 转换 CSF 为 Vue SFC
            const vueSFC = await csf2sfc.convertCSFToVueSFC(code, storyName);

            // 写入 Vue SFC 文件
            fs.writeFileSync(path.join(outDir, `${componentName}.vue`), vueSFC);

            const abcComponentName = isBizComponent ? `${componentName}` : `abc-${componentName}`;
            // kabab-case to 大驼峰命名
            const pascalCaseName = abcComponentName
                .split('-')
                .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
                .join('');

            const webTypes = isBizComponent ? bizResolvedWebTypes : resolvedWebTypes;
            const webTypeInfo = webTypes[abcComponentName];

            console.log(`[${componentName}] web type info:`, webTypeInfo);

            // 创建组件信息对象
            const componentInfo = {
                name: pascalCaseName,
                description: description,
                usage: vueSFC.replace(/\s+/g, ' ').trim(), // 去掉多余空格和换行符
                props: webTypeInfo?.attributes,
                slots: webTypeInfo?.slots,
                events: webTypeInfo?.events,
            };

            // 将组件信息添加到汇总数组中
            allComponents.push(componentInfo);

            // 写入单个组件的 JSON 文件
            const jsonFilePath = path.join(outDirJson, `${abcComponentName}.json`);
            fs.writeFileSync(jsonFilePath, JSON.stringify(componentInfo, null, 2));

            console.log(`[${componentName}] JSON file created: ${jsonFilePath}`);
        } catch (e) {
            console.warn(`[${componentName}] convert failed`, e);
            continue;
        }
    }

    // 写入汇总的 components.json 文件
    const summaryFilePath = path.join(__dirname, 'output', 'components.json');
    fs.writeFileSync(summaryFilePath, JSON.stringify(allComponents, null, 2));
    console.log(`Summary JSON file created: ${summaryFilePath}`);
}

main();