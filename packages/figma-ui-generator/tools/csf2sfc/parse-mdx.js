const fs = require('fs');
const path = require('path');
const babelParser = require('@babel/parser');
const babelTraverse = require('@babel/traverse').default;

// 动态导入 ESM 模块
let unified, remarkParse, remarkMdx, visitParents;

// 解析 MDX 文件为 MDAST
async function parseMdxToMdast(mdxFilePath) {
    const mdxContent = fs.readFileSync(mdxFilePath, 'utf-8');

    try {
        const mdast = unified()
            .use(remarkParse)
            .use(remarkMdx)
            .parse(mdxContent);

        return mdast;
    } catch (error) {
        console.error('Error parsing MDX to MDAST:', error);
    }
}

// 解析 MDAST，提取所有的 Story 引用和对应的 import 模块
function extractStoriesAndImportsFromMdast(mdast) {
    const storyReferenceList = [];
    const imports = [];
    let description = '';

    visitParents(mdast, (node, ancestors) => {
        // 提取 mdxjsEsm 节点对应的 import 语句
        if (node.type === 'mdxjsEsm') {
            imports.push(node.value);
        }

        if (node.type === 'blockquote' && node.children.length > 0 && !description && node.children[0].type === 'paragraph' && node.children[0].children.length > 0 && node.children[0].children[0].type === 'text' && node.children[0].children[0].value) {
            // description = node.children[0].children[0].value;
            node.children[0].children.filter(child => child.type === 'text' || child.type === 'inlineCode').map(child => {
                if (child.type === 'text') {
                    description += child.value;
                } else if (child.type === 'inlineCode') {
                    description += child.value;
                }
            })
        }

        // 提取 <Story of={...}> 中的 `of` 属性
        if (node.type === 'mdxJsxFlowElement' && (node.name === 'Story' || node.name === 'Canvas')) {
            const ofAttribute = node.attributes.find(attr => attr.name === 'of');
            if (ofAttribute && ofAttribute.value) {
                // 检查 ofAttribute.value 的类型
                if (ofAttribute.value.type === 'mdxJsxAttributeValueExpression') {
                    // 获取表达式内容
                    const expressionBody = ofAttribute.value.data.estree.body[0].expression;

                    if (expressionBody.type === 'MemberExpression') {
                        const objectName = expressionBody.object.name; // TagStories
                        const propertyName = expressionBody.property.name; // $应用场景
                        storyReferenceList.push({ objectName, propertyName });
                    }
                }
            }
        }
    });

    return { storyReferenceList, imports, description };
}

// 解析 import 语句，找到组件文件的路径
function extractJsFilePathFromImports(imports, componentImportName, baseDir) {
    for (const importStatement of imports) {
        const match = new RegExp(`import \\* as ${componentImportName} from ['"](.*)['"];`).exec(importStatement);
        if (match && match[1]) {
            let fileName = match[1];
            if (!fileName.endsWith('.js')) {
                fileName += '.js';
            }
            const jsFilePath = path.resolve(baseDir, fileName);
            return jsFilePath;
        }
    }
    return null;
}

// 解析 JS 文件，提取组件的 template 内容
function extractExampleCodeFromJsFile(jsFilePath, componentName) {
    const jsCode = fs.readFileSync(jsFilePath, 'utf-8');

    const ast = babelParser.parse(jsCode, {
        sourceType: 'module',
        plugins: ['jsx', 'classProperties'],
    });

    let templateCode = null;

    babelTraverse(ast, {
        ExportNamedDeclaration(path) {
            const declaration = path.node.declaration;

            if (declaration && declaration.declarations) {
                declaration.declarations.forEach((declarator) => {
                    if (declarator.id.name === componentName) {
                        const init = declarator.init;

                        if (init) {
                            let componentObj = null;
                            if (init.type === 'ObjectExpression') {
                                // 找到 render 方法
                                const renderMethod = init.properties.find(prop =>
                                    prop.key.name === 'render'
                                );

                                if (renderMethod && renderMethod.value && renderMethod.value.type === 'ArrowFunctionExpression') {
                                    componentObj = renderMethod.value;
                                }
                            } else if (init.type === 'ArrowFunctionExpression') {
                                // 直接遍历 ArrowFunctionExpression.body 中的 ObjectExpression
                                if (init.body.type === 'ObjectExpression') {
                                    componentObj = init;
                                }
                            }

                            if (componentObj) {
                                // 直接遍历 componentObj.body 中的 ObjectExpression
                                if (componentObj.body.type === 'ObjectExpression') {
                                    componentObj.body.properties.forEach(property => {
                                        if (property.key.name === 'template') {
                                            // 检查 property.value 的类型
                                            if (property.value.type === 'TemplateLiteral') {
                                                // 提取 TemplateLiteral 的内容
                                                templateCode = property.value.quasis.map(quasi => quasi.value.cooked).join('');
                                            } else if (property.value.type === 'Literal') {
                                                templateCode = property.value.value; // 获取 Literal 内容
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    }
                });
            }
        }
    });
    return templateCode;
}

// 解析 MDX 文件，提取组件名、描述和对应的 Story
async function parseMdxToJson(mdxFilePath, baseDir) {
    try {
        unified = (await import('unified')).unified;
        remarkParse = (await import('remark-parse')).default;
        remarkMdx = (await import('remark-mdx')).default;
        visitParents = (await import('unist-util-visit-parents')).visitParents;
        try {
            // 读取 MDX 文件内容并转换为 MDAST
            const mdast = await parseMdxToMdast(mdxFilePath);
            const { storyReferenceList, imports, description } = extractStoriesAndImportsFromMdast(mdast);

            if (!storyReferenceList.length) {
                throw new Error('No story reference found in MDX', mdxFilePath);
            }

            return {
                description,
            };
        } catch (error) {
            console.error('Error parsing MDX to JSON:', error, mdxFilePath);
        }
    } catch (err) {
        console.error('Failed to load ESM modules:', err);
    }
}

module.exports = {
    parseMdxToJson,
};