const parser = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const t = require('@babel/types');
const generate = require('@babel/generator').default;
const fs = require('fs');
const prettier = require('prettier'); // 使用 prettier 格式化代码

async function convertCSFToVueSFC(sourceCode, storyName = '') {
    // 解析源代码为 AST
    const ast = parser.parse(sourceCode, {
        sourceType: 'module',
        plugins: ['jsx'], // 支持 JSX 语法
    });

    // 提取的内容
    let templateContent = '';
    let scriptContent = '';

    /**
     * 提取 template 和其他内容
     * @param {Object} node - AST 节点
     */
    function extractTemplateAndScript(node) {
        if (t.isObjectExpression(node)) {
            // 提取 template
            const templateProp = node.properties.find(
                (prop) => prop.key.name === 'template'
            );
            if (templateProp) {
                if (t.isTemplateLiteral(templateProp.value)) {
                    templateContent = templateProp.value.quasis[0].value.raw.trim();
                } else if (t.isStringLiteral(templateProp.value)) {
                    templateContent = templateProp.value.value.trim();
                }
                if (templateContent) {
                    // 去除 <br/>
                    templateContent = templateContent.replace(/<br\/>/g, '\n');
                    // 去除换行
                    templateContent = templateContent.replace(/\n/g, '');
                }
            }

            const ignorePropKeys = ['template', 'name', 'components'];
            // 提取其他内容（components、data、methods 等）
            const scriptProps = node.properties.filter(
                (prop) => !ignorePropKeys.includes(prop.key.name)
            );
            scriptContent = generate(t.objectExpression(scriptProps)).code;
        }
    }

    // 标志是否已经处理第一个具名导出
    let isFirstExportProcessed = false;

    // 遍历 AST
    traverse(ast, {
        // 找到 export const
        ExportNamedDeclaration(path) {
            // 如果已经处理过第一个具名导出，直接返回
            if (isFirstExportProcessed) return;

            const declaration = path.node.declaration;
            if (t.isVariableDeclaration(declaration)) {
                const varName = declaration.declarations[0].id.name;

                // 如果是子组件，则当名称不符合时跳过处理
                if (storyName && varName !== storyName) {
                    return;
                }

                const exportValue = declaration.declarations[0].init;

                // 处理有 render 方法的情况
                if (t.isObjectExpression(exportValue)) {
                    const renderFunction = exportValue.properties.find(
                        (prop) => prop.key.name === 'render'
                    );

                    if (renderFunction && t.isArrowFunctionExpression(renderFunction.value)) {
                        const renderBody = renderFunction.value.body; // render 方法的函数体
                        extractTemplateAndScript(renderBody);
                    }
                }

                // 处理没有 render 方法的情况
                if (t.isArrowFunctionExpression(exportValue)) {
                    const returnValue = exportValue.body;
                    extractTemplateAndScript(returnValue);
                }

                // 如果是子组件，则当名称符合时标记已处理
                if (storyName && varName === storyName) {
                    isFirstExportProcessed = true;
                } else {
                    // 标记第一个具名导出已处理
                    isFirstExportProcessed = true;
                }
            }
        },
    });


    // 生成 Vue SFC
    const vueSFC = `
        <template>
        ${templateContent}
        </template>
        <script>
        export default ${scriptContent};
        </script>
    `;

    const res = await prettier.format(vueSFC, { parser: 'vue', singleQuote: true, semi: false, tabWidth: 2 });
    return res;
}

module.exports = {
    convertCSFToVueSFC
}