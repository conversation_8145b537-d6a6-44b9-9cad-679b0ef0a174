const babel = require('@babel/parser');
const traverse = require('@babel/traverse').default;
const generate = require('@babel/generator').default;
const t = require('@babel/types');

// 获取属性键名
function getPropKeyName(prop) {
    if (prop.key.type === 'Identifier') {
        return prop.key.name;
    } else if (prop.key.type === 'StringLiteral') {
        return prop.key.value;
    } else {
        return null;
    }
}

// 获取属性值类型
function getPropValueType(prop) {
    return prop.value && prop.value.type;
}

// 将CSF代码转换为Vue单文件组件
function convertCSFToVueSFC(csfCode) {
    // 将CSF代码解析为AST
    const ast = babel.parse(csfCode, {
        sourceType: 'module',
        plugins: ['dynamicImport', 'objectRestSpread', 'arrowFunctions', 'templateStrings', 'jsx', 'classProperties'],
        tokens: true,
        comments: true,
        ranges: true
    });

    let imports = []; // 存储导入语句
    let templateString = ''; // 存储模板字符串
    let components = {}; // 存储组件
    let dataFunc = null; // 存储data函数
    let methods = {}; // 存储methods
    let watch = {}; // 存储watch
    let computed = {}; // 存储computed
    let lifecycleHooks = {}; // 存储生命周期钩子

    // 遍历AST，收集导入语句和组件信息
    traverse(ast, {
        ImportDeclaration(path) {
            // 处理导入语句
            path.node.specifiers.forEach(specifier => {
                if (specifier.type === 'ImportDefaultSpecifier' || specifier.type === 'ImportSpecifier') {
                    components[specifier.local.name] = specifier.local.name;
                } else if (specifier.type === 'ImportNamespaceSpecifier') {
                    components[specifier.local.name] = specifier.local.name;
                }
            });
            imports.push(path.node);
        },
        ExportNamedDeclaration(path) {
            // 处理导出语句
            if (path.node.declaration && path.node.declaration.type === 'VariableDeclaration') {
                path.node.declaration.declarations.forEach(declarator => {
                    if (declarator.init && declarator.init.type === 'ObjectExpression') {
                        // 查找render属性
                        const renderProperty = declarator.init.properties.find(prop => getPropKeyName(prop) === 'render');
                        if (renderProperty && renderProperty.value.type === 'ArrowFunctionExpression') {
                            const returnedObject = renderProperty.value.body;
                            if (returnedObject.type === 'ObjectExpression') {
                                // 提取模板
                                const templateProperty = returnedObject.properties.find(prop => getPropKeyName(prop) === 'template');
                                if (templateProperty && templateProperty.value.type === 'TemplateLiteral') {
                                    templateString = templateProperty.value.quasis.map(quasi => quasi.value.raw).join('');
                                }
                                // 提取data、methods、watch、computed和生命周期钩子
                                returnedObject.properties.forEach(prop => {
                                    const keyName = getPropKeyName(prop);
                                    const valueType = getPropValueType(prop);
                                    if (keyName === 'data' && valueType === 'FunctionExpression') {
                                        dataFunc = prop.value;
                                    } else if (keyName === 'methods' && valueType === 'ObjectExpression') {
                                        methods = prop.value.properties.reduce((acc, p) => {
                                            if (p.type === 'ObjectMethod') {
                                                // 处理 ObjectMethod 节点
                                                acc[p.key.name] = t.functionExpression(
                                                    null, // 函数名（匿名函数）
                                                    p.params, // 参数
                                                    p.body, // 函数体
                                                    p.generator, // 是否是生成器函数
                                                    p.async // 是否是异步函数
                                                );
                                            } else if (p.type === 'ObjectProperty' && p.value) {
                                                // 处理 ObjectProperty 节点
                                                acc[p.key.name] = p.value;
                                            } else {
                                                console.error(`方法'${p.key.name}'的值无效:`, p);
                                            }
                                            return acc;
                                        }, {});
                                    } else if (keyName === 'watch' && valueType === 'ObjectExpression') {
                                        watch = prop.value.properties.reduce((acc, p) => {
                                            if (p.type === 'ObjectMethod') {
                                                // 处理 ObjectMethod 节点
                                                acc[p.key.name] = t.functionExpression(
                                                    null, // 函数名（匿名函数）
                                                    p.params, // 参数
                                                    p.body, // 函数体
                                                    p.generator, // 是否是生成器函数
                                                    p.async // 是否是异步函数
                                                );
                                            } else if (p.type === 'ObjectProperty' && p.value) {
                                                // 处理 ObjectProperty 节点
                                                acc[p.key.name] = p.value;
                                            } else {
                                                console.error(`watch属性'${p.key.name}'的值无效:`, p);
                                            }
                                            return acc;
                                        }, {});
                                    } else if (keyName === 'computed' && valueType === 'ObjectExpression') {
                                        computed = prop.value.properties.reduce((acc, p) => {
                                            if (p.type === 'ObjectMethod') {
                                                // 处理 ObjectMethod 节点
                                                acc[p.key.name] = t.functionExpression(
                                                    null, // 函数名（匿名函数）
                                                    p.params, // 参数
                                                    p.body, // 函数体
                                                    p.generator, // 是否是生成器函数
                                                    p.async // 是否是异步函数
                                                );
                                            } else if (p.type === 'ObjectProperty' && p.value) {
                                                // 处理 ObjectProperty 节点
                                                acc[p.key.name] = p.value;
                                            } else {
                                                console.error(`computed属性'${p.key.name}'的值无效:`, p);
                                            }
                                            return acc;
                                        }, {});
                                    } else if (['created', 'mounted', 'destroyed'].includes(keyName) && valueType === 'FunctionExpression') {
                                        lifecycleHooks[keyName] = prop.value;
                                    }
                                });
                            }
                        }
                    }
                });
            }
        }
    });

    // 创建脚本部分的AST
    function createScriptAST(components, data, methods, watch, computed, lifecycleHooks) {
        const properties = [];
        // 添加components属性
        if (components && Object.keys(components).length > 0) {
            const componentsProperty = t.objectProperty(
                t.identifier('components'),
                t.objectExpression(
                    Object.keys(components).map(comp => {
                        if (typeof components[comp] === 'string') {
                            return t.objectProperty(
                                t.identifier(comp),
                                t.identifier(components[comp])
                            );
                        } else {
                            console.error(`组件'${comp}'的值无效:`, components[comp]);
                            return null;
                        }
                    }).filter(Boolean) // 过滤掉无效的组件
                )
            );
            properties.push(componentsProperty);
        }
        // 添加data属性
        if (data) {
            const dataProperty = t.objectProperty(
                t.identifier('data'),
                data
            );
            properties.push(dataProperty);
        }
        // 添加methods属性
        if (methods && Object.keys(methods).length > 0) {
            const methodsProperty = t.objectProperty(
                t.identifier('methods'),
                t.objectExpression(
                    Object.keys(methods).map(method => {
                        if (methods[method]) {
                            return t.objectProperty(
                                t.identifier(method),
                                methods[method]
                            );
                        } else {
                            console.error(`方法'${method}'的值无效:`, methods[method]);
                            return null;
                        }
                    }).filter(Boolean) // 过滤掉无效的方法
                )
            );
            properties.push(methodsProperty);
        }
        // 添加watch属性
        if (watch && Object.keys(watch).length > 0) {
            const watchProperty = t.objectProperty(
                t.identifier('watch'),
                t.objectExpression(
                    Object.keys(watch).map(w => {
                        if (watch[w]) {
                            return t.objectProperty(
                                t.identifier(w),
                                watch[w]
                            );
                        } else {
                            console.error(`watch属性'${w}'的值无效:`, watch[w]);
                            return null;
                        }
                    }).filter(Boolean) // 过滤掉无效的watch属性
                )
            );
            properties.push(watchProperty);
        }
        // 添加computed属性
        if (computed && Object.keys(computed).length > 0) {
            const computedProperty = t.objectProperty(
                t.identifier('computed'),
                t.objectExpression(
                    Object.keys(computed).map(c => {
                        if (computed[c]) {
                            return t.objectProperty(
                                t.identifier(c),
                                computed[c]
                            );
                        } else {
                            console.error(`computed属性'${c}'的值无效:`, computed[c]);
                            return null;
                        }
                    }).filter(Boolean) // 过滤掉无效的computed属性
                )
            );
            properties.push(computedProperty);
        }
        // 添加生命周期钩子
        if (lifecycleHooks && Object.keys(lifecycleHooks).length > 0) {
            Object.keys(lifecycleHooks).forEach(hook => {
                if (lifecycleHooks[hook]) {
                    const hookProperty = t.objectProperty(
                        t.identifier(hook),
                        lifecycleHooks[hook]
                    );
                    properties.push(hookProperty);
                } else {
                    console.error(`生命周期钩子'${hook}'的值无效:`, lifecycleHooks[hook]);
                }
            });
        }

        // 返回默认导出的对象表达式
        return t.exportDefaultDeclaration(
            t.objectExpression(properties)
        );
    }

    // 生成脚本部分代码
    const importStatements = imports.map(generateImportStatement).join('\n');
    const scriptDeclaration = createScriptAST(
        components,
        dataFunc,
        methods,
        watch,
        computed,
        lifecycleHooks
    );
    const scriptProgram = t.file(t.program([scriptDeclaration]));
    const { code: scriptCode } = generate(scriptProgram);
    const scriptSection = `
<script>
${importStatements}
${scriptCode}
</script>
    `.trim();

    // 生成模板部分代码
    const templateSection = `<template>${templateString}</template>`;

    // 组合成最终的Vue单文件组件
    const vueSFC = `${templateSection}\n${scriptSection}`;

    return vueSFC;
}

// 生成导入语句
function generateImportStatement(importNode) {
    if (importNode.specifiers.length === 1) {
        const specifier = importNode.specifiers[0];
        if (specifier.type === 'ImportDefaultSpecifier') {
            return `import ${specifier.local.name} from '${importNode.source.value}';`;
        } else if (specifier.type === 'ImportSpecifier') {
            return `import { ${specifier.local.name} } from '${importNode.source.value}';`;
        } else if (specifier.type === 'ImportNamespaceSpecifier') {
            return `import * as ${specifier.local.name} from '${importNode.source.value}';`;
        }
    } else {
        const specifiers = importNode.specifiers.map(specifier => {
            if (specifier.type === 'ImportSpecifier') {
                return specifier.local.name;
            } else if (specifier.type === 'ImportDefaultSpecifier') {
                return specifier.local.name;
            } else if (specifier.type === 'ImportNamespaceSpecifier') {
                return `* as ${specifier.local.name}`;
            }
            return '';
        }).join(', ');
        return `import { ${specifiers} } from '${importNode.source.value}';`;
    }
    return '';
}

module.exports = {
    convertCSFToVueSFC
}