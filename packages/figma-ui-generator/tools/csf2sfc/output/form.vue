<template>
  <div>
    <div style="margin-bottom: 24px">
      可以设置itemNoMargin<input
        style="margin: 0 8px"
        type="checkbox"
        v-model="itemNoMargin"
      />去掉右和下margin
    </div>
    <div style="margin-bottom: 24px">
      <abc-select v-model="marginSize" :width="100">
        <abc-option value="default" label="default"></abc-option>
        <abc-option value="large" label="large"></abc-option>
      </abc-select>
      {{ marginSize }}:
      {{
        marginSize === 'default'
          ? '右margin 10px;下margin 24px'
          : '右margin 24px;下margin 16px'
      }}
    </div>
    <div style="margin-bottom: 24px">
      <abc-select v-model="labelPosition" :width="100">
        <abc-option value="top" label="top"></abc-option>
        <abc-option value="left" label="left"></abc-option>
        <abc-option value="left-top" label="left-top"></abc-option>
        <abc-option value="inner" label="inner"></abc-option>
      </abc-select>
      可以通过 label-position 设置label具体位置
    </div>
    <abc-form
      ref="form"
      data-cy="storybook-test-default"
      :label-position="labelPosition"
      :label-width="110"
      :item-no-margin="itemNoMargin"
      :marginSize="marginSize"
    >
      <abc-form-item label="居中错误提示" required placement="top">
        <abc-input-number
          v-model="postData.taxNum"
          fixed-button
          button-placement="left"
        ></abc-input-number>
      </abc-form-item>
      <abc-form-item label="纳税人识别号" help="这里是help信息">
        <template slot="labelTips">
          <div>提示信息1</div>
          <div>提示信息2</div>
          <div>提示信息3</div>
          <div>提示信息4</div>
        </template>
        <abc-input
          v-model="postData.taxNum"
          :width="300"
          readonly
          disabled
        ></abc-input>
      </abc-form-item>
      <abc-form-item label="企业名称" :validate-event="validateNo">
        <template slot="labelTips"> <div>提示信息</div> </template>
        <abc-input v-model="postData.taxName" :width="300"></abc-input>
      </abc-form-item>
      <abc-form-item required label="地址" error-theme="inner">
        <abc-input
          v-model="postData.address"
          :width="300"
          placeholder="当设置error-theme为inner时，错误标记只是占位文字变黄"
        ></abc-input>
      </abc-form-item>
      <abc-form-item required label="电话" :validate-event="validateMobile">
        <abc-input v-model="postData.telephone" :width="300"> </abc-input>
      </abc-form-item>
      <abc-form-item label="开户行名称">
        <abc-input v-model="postData.bankAgent" :width="300"></abc-input>
      </abc-form-item>
      <abc-form-item label="开户行账号">
        <abc-input v-model="postData.bankAccount" :width="300"></abc-input>
      </abc-form-item>
      <abc-form-item required label="复核人">
        <abc-textarea
          v-model="postData.invoiceChecker"
          :rows="3"
          :width="300"
        ></abc-textarea>
      </abc-form-item>
      <abc-form-item required label="复核人标签" :validate-event="validateTags">
        <AbcInputTag v-model="postData.tags" :width="300"></AbcInputTag>
      </abc-form-item>
      <abc-button class="confirm-btn" @click="submit">确定</abc-button>
    </abc-form>
  </div>
</template>
<script>
export default {
  data() {
    return {
      postData: {
        taxNum: '',
        taxName: '',
        address: '',
        telephone: '',
        bankAgent: '',
        bankAccount: '',
        invoiceChecker: '',
        tags: [],
      },
      money: 0,
      itemNoMargin: false,
      marginSize: 'default',
      labelPosition: 'left-top',
    }
  },
  methods: {
    validateNo(value, callback) {
      setTimeout(() => {
        if (value === '111') {
          return callback({
            validate: false,
            message: '不能输入111',
          })
        }
        callback({
          validate: !!value,
        })
      }, 1000)
    },
    validateTags(value, callback) {
      if (value.split(',').length > 2) {
        callback({
          validate: false,
          message: '最多只能选择2个',
        })
      } else {
        callback({
          validate: true,
        })
      }
    },
    validateMobile(value, callback) {
      if (!value) {
        callback({
          validate: true,
        })
        return
      }
      if (
        value.length !== 11 &&
        value.length !== 8 &&
        !((value.length === 9 || value.length === 12) && value.includes('-'))
      ) {
        callback({
          validate: false,
          message: '输入11位的手机号或正确格式的座机号',
        })
      } else if (
        !/^1[3|4|5|6|7|8|9]\d{9}$/.test(value) &&
        !/^(?:0[1-9][0-9]{1,2}-?)?[2-8][0-9]{6,7}$/.test(value)
      ) {
        callback({
          validate: false,
          message: '手机/座机号格式不正确',
        })
      } else {
        callback({
          validate: true,
        })
      }
    },
    submit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          console.log('校验成功')
        } else {
          console.log('校验失败')
        }
      }, null)
    },
  },
}
</script>
