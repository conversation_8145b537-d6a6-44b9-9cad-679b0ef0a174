<template>
  <AbcRow class="abc-grid-wrapper" :gutter="[0, 'large']" :wrap="true">
    <AbcCol :span="24">
      <AbcRow gutter="middle">
        <AbcCol :span="24"> <div class="abc-col-cell b1">col-24</div> </AbcCol>
      </AbcRow>
    </AbcCol>
    <AbcCol :span="24">
      <AbcRow gutter="middle">
        <AbcCol :span="12"> <div class="abc-col-cell b1">col-12</div> </AbcCol>
        <AbcCol :span="12"> <div class="abc-col-cell b2">col-12</div> </AbcCol>
      </AbcRow>
    </AbcCol>
    <AbcCol :span="24">
      <AbcRow gutter="middle">
        <AbcCol :span="8"> <div class="abc-col-cell b1">col-8</div> </AbcCol>
        <AbcCol :span="8"> <div class="abc-col-cell b2">col-8</div> </AbcCol>
        <AbcCol :span="8"> <div class="abc-col-cell b1">col-8</div> </AbcCol>
      </AbcRow>
    </AbcCol>
    <AbcCol :span="24">
      <AbcRow gutter="middle">
        <AbcCol :span="6"> <div class="abc-col-cell b1">col-6</div> </AbcCol>
        <AbcCol :span="6"> <div class="abc-col-cell b2">col-6</div> </AbcCol>
        <AbcCol :span="6"> <div class="abc-col-cell b1">col-6</div> </AbcCol>
        <AbcCol :span="6"> <div class="abc-col-cell b2">col-6</div> </AbcCol>
      </AbcRow>
    </AbcCol></AbcRow
  >
</template>
<script>
export default {}
</script>
