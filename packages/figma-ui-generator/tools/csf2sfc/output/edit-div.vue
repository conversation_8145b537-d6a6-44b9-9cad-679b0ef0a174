<template>
  <div>
    <ul>
      <li>通过 size 设置默认的大小</li>
      <li>支持 disabled</li>
      <li>支持响应式，自动撑开内容, 需要不指定size</li>
    </ul>
    <div>
      <abc-space size="large" style="margin-top: 16px">
        <abc-title>不同大小</abc-title>
        <abc-radio-group v-model="size">
          <abc-radio label="tiny">tiny</abc-radio>
          <abc-radio label="small">small</abc-radio>
          <abc-radio label="">default</abc-radio>
          <abc-radio label="medium">medium</abc-radio>
          <abc-radio label="large">large</abc-radio>
          <abc-radio label="huge">huge</abc-radio>
        </abc-radio-group>
      </abc-space>
    </div>
    <div>
      <abc-space size="large" style="margin-top: 16px">
        <abc-title>支持响应式</abc-title>
        <abc-checkbox v-model="responsive"> </abc-checkbox>
      </abc-space>
    </div>
    <div>
      <abc-space size="large" style="margin-top: 16px">
        <abc-title>高度跟随内容变化，无限撑高</abc-title>
        <abc-checkbox v-model="isAutoHeight"> </abc-checkbox>
      </abc-space>
    </div>
    <div>
      <abc-space style="margin-top: 16px" size="large">
        <abc-title>设置状态为disabled</abc-title>
        <abc-checkbox v-model="isDisabled">disabled</abc-checkbox>
      </abc-space>
    </div>
    <div>
      <abc-space style="margin-top: 16px" size="large">
        <abc-title>设置状态为readonly</abc-title>
        <abc-checkbox v-model="isReadonly">readonly</abc-checkbox>
      </abc-space>
    </div>
    <div>
      <abc-space style="margin-top: 16px" size="large">
        <abc-title>内容是否支持编辑</abc-title>
        <abc-checkbox v-model="isContentEditable">contentEditable</abc-checkbox>
      </abc-space>
    </div>
    <div>
      <abc-space style="margin-top: 16px" size="large">
        <abc-title>isOrder</abc-title>
        <abc-checkbox v-model="isOrder">isOrder</abc-checkbox>
      </abc-space>
    </div>
    <abc-edit-div
      data-cy="storybook-test-default"
      style="margin-top: 16px"
      v-model="comment"
      :responsive="responsive"
      :auto-height="isAutoHeight"
      :size="size"
      :readonly="isReadonly"
      :disabled="isDisabled"
      :content-editable="isContentEditable"
      :is-order="isOrder"
      :maxlength="100"
      placeholder="请输入取消入库的原因"
    ></abc-edit-div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      comment: '',
      size: 'huge',
      isDisabled: false,
      isReadonly: false,
      isContentEditable: true,
      isOrder: false,
      responsive: false,
      isAutoHeight: true,
    }
  },
}
</script>
