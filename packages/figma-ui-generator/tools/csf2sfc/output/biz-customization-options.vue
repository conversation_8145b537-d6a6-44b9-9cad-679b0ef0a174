<template>
  <div>
    <abc-button @click="visible = true"> 打开设置弹窗 </abc-button>
    <abc-flex> 当前数据： </abc-flex>
    <ul>
      <li v-for="item in dataList" :key="item.id">
        <span>排序：{{ item.sort }}</span>
        <span style="margin-left: 8px">内容：{{ item.content }}</span>
      </li>
    </ul>
    <biz-customization-options
      v-if="visible"
      v-model="visible"
      :data-list="dataList"
      title="设置弹窗"
      show-icon
      :on-submit="onSubmit"
    >
    </biz-customization-options>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
      dataList: [
        {
          id: 11111,
          content: '选项1',
          sort: 0,
        },
        {
          id: 22222,
          content: '选项2',
          sort: 1,
        },
        {
          id: 33333,
          content: '选项3',
          sort: 2,
        },
      ],
    }
  },
  methods: {
    onSubmit(dataList, deleteDataList) {
      console.log(
        '%c dataList\n',
        'background: green; padding: 0 5px',
        dataList,
      )
      console.log(
        '%c deleteDataList\n',
        'background: green; padding: 0 5px',
        deleteDataList,
      )
      this.dataList = dataList
    },
  },
}
</script>
