<template>
  <div>
    <abc-form label-position="left" :label-width="110">
      <abc-form-item label="展示固定列">
        <abc-radio-group v-model="showFixed">
          <abc-radio :label="1">是</abc-radio>
          <abc-radio :label="0">否</abc-radio>
        </abc-radio-group>
      </abc-form-item>
      <abc-form-item label="设置列表模式">
        <abc-radio-group v-model="mode">
          <abc-radio label="fixed">固定</abc-radio>
          <abc-radio label="draggle">拖动</abc-radio>
        </abc-radio-group>
      </abc-form-item>
      <abc-form-item>
        <abc-button @click="showDialog = true">打开弹窗</abc-button>
      </abc-form-item>
    </abc-form>
    <biz-custom-header
      v-if="showDialog"
      v-model="showDialog"
      titleName="财务报表 - 门店"
      :showFixed="!!showFixed"
      :mode="mode"
      :tableFields="tableFields"
      :handleReset="handleReset"
      :handleSave="handleSave"
      :onClose="onClose"
    ></biz-custom-header>
  </div>
</template>
<script>
export default {
  data() {
    return {
      showDialog: false,
      showFixed: 1,
      mode: 'fixed',
      tableFields: [
        {
          key: 'goodsBasicInfo',
          parentKey: '',
          label: '药品/物资基础信息',
          settingDisplayLabel: '药品/物资基础信息',
          env: 'dev',
          isHidden: 0,
          isFixed: null,
          position: null,
          enableIndependentModifyColumnChildren: 0,
          enableEmployeeModify: 1,
          settingSort: null,
          children: [
            {
              key: 'goodsBasicInfo.barCode',
              parentKey: 'goodsBasicInfo',
              label: '条码',
              settingDisplayLabel: '条码',
              env: 'dev',
              isHidden: 1,
              isFixed: 1,
              position: 2,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 10,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.drugIdentificationCode',
              parentKey: 'goodsBasicInfo',
              label: '标识码',
              settingDisplayLabel: '标识码',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 3,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 10,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.goodsTag',
              parentKey: 'goodsBasicInfo',
              label: '标签',
              settingDisplayLabel: '标签',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 4,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 10,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.pharmacologicName',
              parentKey: 'goodsBasicInfo',
              label: '功效分类',
              settingDisplayLabel: '功效分类',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 5,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 11,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.otcTypeName',
              parentKey: 'goodsBasicInfo',
              label: '处方药/OTC',
              settingDisplayLabel: '处方药/OTC',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 6,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 12,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.baseMedicineTypeName',
              parentKey: 'goodsBasicInfo',
              label: '基药',
              settingDisplayLabel: '基药',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 7,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 13,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.maintainTypeName',
              parentKey: 'goodsBasicInfo',
              label: '养护分类',
              settingDisplayLabel: '养护分类',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 8,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 14,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.remark',
              parentKey: 'goodsBasicInfo',
              label: '备注',
              settingDisplayLabel: '备注',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 9,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 15,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.storage',
              parentKey: 'goodsBasicInfo',
              label: '存储条件',
              settingDisplayLabel: '存储条件',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 10,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 15,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.displaySpec',
              parentKey: 'goodsBasicInfo',
              label: '规格',
              settingDisplayLabel: '规格',
              env: 'dev',
              isHidden: 1,
              isFixed: 1,
              position: 11,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.goodsName',
              parentKey: 'goodsBasicInfo',
              label: '药名',
              settingDisplayLabel: '药名',
              env: 'dev',
              isHidden: 0,
              isFixed: 1,
              position: 1,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 0,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.manufacture',
              parentKey: 'goodsBasicInfo',
              label: '厂家',
              settingDisplayLabel: '厂家',
              env: 'dev',
              isHidden: 1,
              isFixed: 1,
              position: 12,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.medcineNpmn',
              parentKey: 'goodsBasicInfo',
              label: '国药准字',
              settingDisplayLabel: '国药准字',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 13,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.position',
              parentKey: 'goodsBasicInfo',
              label: '柜号',
              settingDisplayLabel: '柜号',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 14,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.priceType',
              parentKey: 'goodsBasicInfo',
              label: '定价类型',
              settingDisplayLabel: '定价类型',
              env: 'dev',
              isHidden: 1,
              isFixed: 1,
              position: 15,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.shortId',
              parentKey: 'goodsBasicInfo',
              label: '药品编码',
              settingDisplayLabel: '药品编码',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 16,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsBasicInfo.typeName',
              parentKey: 'goodsBasicInfo',
              label: '类型',
              settingDisplayLabel: '类型',
              env: 'dev',
              isHidden: 1,
              isFixed: 1,
              position: 17,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
          ],
          columnChildren: null,
          isSettingHidden: 0,
        },
        {
          key: 'goodsPriceInfo',
          parentKey: '',
          label: '价格信息',
          settingDisplayLabel: '价格信息',
          env: 'dev',
          isHidden: 0,
          isFixed: null,
          position: null,
          enableIndependentModifyColumnChildren: 0,
          enableEmployeeModify: 1,
          settingSort: null,
          children: [
            {
              key: 'goodsPriceInfo.chainPackagePrice',
              parentKey: 'goodsPriceInfo',
              label: '总部定价',
              settingDisplayLabel: '总部定价',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 18,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 1,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.chainPiecePrice',
              parentKey: 'goodsPriceInfo',
              label: '总部定价(拆零)',
              settingDisplayLabel: '总部定价(拆零)',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 19,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 2,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.inTaxRat',
              parentKey: 'goodsPriceInfo',
              label: '进项税',
              settingDisplayLabel: '进项税',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 20,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 3,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.lastPackageCostPrice',
              parentKey: 'goodsPriceInfo',
              label: '最近进价',
              settingDisplayLabel: '最近进价',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 21,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 4,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.lastSupplierName',
              parentKey: 'goodsPriceInfo',
              label: '最近供应商',
              settingDisplayLabel: '最近供应商',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 22,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 5,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.outTaxRat',
              parentKey: 'goodsPriceInfo',
              label: '销项税',
              settingDisplayLabel: '销项税',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 23,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 6,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.packagePrice',
              parentKey: 'goodsPriceInfo',
              label: '门店售价',
              settingDisplayLabel: '门店售价',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 24,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 7,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.piecePrice',
              parentKey: 'goodsPriceInfo',
              label: '门店售价(拆零)',
              settingDisplayLabel: '门店售价(拆零)',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 25,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 8,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsPriceInfo.profitRat',
              parentKey: 'goodsPriceInfo',
              label: '毛利率',
              settingDisplayLabel: '毛利率',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 26,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 12,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
          ],
          columnChildren: null,
          isSettingHidden: 0,
        },
        {
          key: 'goodsShebaoInfo',
          parentKey: '',
          label: '医保信息',
          settingDisplayLabel: '医保信息',
          env: 'dev',
          isHidden: 0,
          isFixed: null,
          position: null,
          enableIndependentModifyColumnChildren: 0,
          enableEmployeeModify: 1,
          settingSort: null,
          children: [
            {
              key: 'goodsShebaoInfo.shebaoCode',
              parentKey: 'goodsShebaoInfo',
              label: '医保码',
              settingDisplayLabel: '医保码',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 27,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 1,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.standardCode',
              parentKey: 'goodsShebaoInfo',
              label: '本位码',
              settingDisplayLabel: '本位码',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 28,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 2,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.medicalFeeGradeName',
              parentKey: 'goodsShebaoInfo',
              label: '类别',
              settingDisplayLabel: '类别',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 29,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 3,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.listingPrice',
              parentKey: 'goodsShebaoInfo',
              label: '挂网价',
              settingDisplayLabel: '挂网价',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 30,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 4,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.shebaoCodePriceLimited',
              parentKey: 'goodsShebaoInfo',
              label: '限价',
              settingDisplayLabel: '限价',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 31,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 5,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.shebaoCodePayMode',
              parentKey: 'goodsShebaoInfo',
              label: '医保支付',
              settingDisplayLabel: '医保支付',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 32,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 6,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.purchasePrice',
              parentKey: 'goodsShebaoInfo',
              label: '进价金额',
              settingDisplayLabel: '进价金额',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 33,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 7,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.shebaoCodeEndDate',
              parentKey: 'goodsShebaoInfo',
              label: '到期时间',
              settingDisplayLabel: '过期时间',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 34,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 7,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsShebaoInfo.restriction',
              parentKey: 'goodsShebaoInfo',
              label: '限制说明',
              settingDisplayLabel: '限制说明',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 35,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: 9,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
          ],
          columnChildren: null,
          isSettingHidden: 0,
        },
        {
          key: 'goodsStockInfo',
          parentKey: '',
          label: '库存信息',
          settingDisplayLabel: '库存信息',
          env: 'dev',
          isHidden: 0,
          isFixed: null,
          position: null,
          enableIndependentModifyColumnChildren: 0,
          enableEmployeeModify: 1,
          settingSort: null,
          children: [
            {
              key: 'goodsStockInfo.avgSell',
              parentKey: 'goodsStockInfo',
              label: '日均消耗量',
              settingDisplayLabel: '日均消耗量',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 36,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.dispGoodsCount',
              parentKey: 'goodsStockInfo',
              label: '当前库存',
              settingDisplayLabel: '当前库存',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 37,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.dispStockGoodsCount',
              parentKey: 'goodsStockInfo',
              label: '可售库存',
              settingDisplayLabel: '可售库存',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 38,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.lastSellDate',
              parentKey: 'goodsStockInfo',
              label: '最近销售时间',
              settingDisplayLabel: '最近销售时间',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 39,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.minExpiryDate',
              parentKey: 'goodsStockInfo',
              label: '最近效期',
              settingDisplayLabel: '最近效期',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 40,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.totalCost',
              parentKey: 'goodsStockInfo',
              label: '药品成本',
              settingDisplayLabel: '药品成本',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 41,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
            {
              key: 'goodsStockInfo.turnoverDays',
              parentKey: 'goodsStockInfo',
              label: '周转天数',
              settingDisplayLabel: '周转天数',
              env: 'dev',
              isHidden: 1,
              isFixed: 0,
              position: 42,
              enableIndependentModifyColumnChildren: 0,
              enableEmployeeModify: 1,
              settingSort: null,
              children: null,
              columnChildren: null,
              isSettingHidden: 0,
            },
          ],
          columnChildren: null,
          isSettingHidden: 0,
        },
      ],
    }
  },
  methods: {
    handleReset() {
      this.showDialog = false
    },
    handleSave() {
      this.showDialog = false
    },
    onClose() {
      this.showDialog = false
    },
  },
}
</script>
