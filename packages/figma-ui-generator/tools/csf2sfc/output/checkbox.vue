<template>
  <abc-flex vertical :gap="16">
    <abc-form is-excel item-no-margin>
      <abc-descriptions :column="2" grid bordered :label-width="200">
        <abc-descriptions-item label="type" content-padding="0">
          <abc-select v-model="type">
            <abc-option label="boolean" value="boolean"></abc-option>
            <abc-option label="number" value="number"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="shape" content-padding="0">
          <abc-select v-model="shape">
            <abc-option label="square" value="square"></abc-option>
            <abc-option label="round" value="round"></abc-option>
            <abc-option label="ring" value="ring"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="disabled">
          <abc-switch v-model="disabled"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="control">
          <abc-switch v-model="control"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="indeterminate">
          <abc-switch v-model="indeterminate"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="noBorder">
          <abc-switch v-model="noBorder"></abc-switch>
        </abc-descriptions-item>
      </abc-descriptions>
    </abc-form>
    <abc-card padding-size="large">
      <abc-checkbox
        v-model="value"
        :disabled="disabled"
        :control="control"
        :type="type"
        :indeterminate="indeterminate"
        :no-border="noBorder"
        :shape="shape"
        @click="handleClick"
      ></abc-checkbox>
    </abc-card>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      value: false,
      disabled: false,
      control: false,
      type: 'boolean',
      indeterminate: false,
      noBorder: false,
      shape: 'square',
    }
  },
  methods: {
    handleClick() {
      if (this.control) {
        window.alert('通过click事件控制')
        this.value = !this.value
      }
    },
  },
}
</script>
