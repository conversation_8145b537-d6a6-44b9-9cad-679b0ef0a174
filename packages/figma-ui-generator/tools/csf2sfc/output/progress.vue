<template>
  <div>
    <abc-progress :percentage="percentage"></abc-progress>
    <abc-progress :percentage="percentage" variant="line"> </abc-progress>
    <abc-progress :percentage="percentage" variant="line" size="large">
    </abc-progress>
    <abc-progress :percentage="percentage" variant="circle"> </abc-progress>
    <abc-progress :percentage="percentage" variant="circle" theme="green">
    </abc-progress>
    <abc-progress
      :percentage="percentage2"
      :circle-total="percentage2"
      variant="circle"
      theme="green"
    >
    </abc-progress>
  </div>
</template>
<script>
export default {
  data() {
    return {
      percentage: 0,
      percentage2: 0,
    }
  },
  created() {
    this.timer = setInterval(() => {
      if (this.percentage >= 100) {
        this.percentage = 0
      }
      this.percentage += 4
      if (this.percentage >= 100) {
        clearInterval(this.timer)
      }
    }, 200)
    setTimeout(() => {
      this.percentage2 = 1
    }, 2000)
    this.$on('hook:beforeDestroy', () => {
      clearInterval(this.timer)
    })
  },
}
</script>
