<template>
  <div
    style="
      width: 360px;
      height: 600px;
      overflow-y: scroll;
      padding: 12px 12px;
      border: 1px solid var(--abc-color-layout-divider-color);
    "
  >
    <biz-crm-track
      :data-list="testData"
      :loading="loading"
      :last="last"
      @openAppointmentCardFromTrack="openAppointmentCardFromTrack"
      @handleOpenDetail="handleOpenDetail"
      @fetchRevisitQuickList="fetchRevisitQuickList"
    ></biz-crm-track>
  </div>
</template>
<script>
export default {
  data() {
    return {
      testData: [
        {
          id: '3271665971969916930',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '随访',
          actionId: '3810476669687840768',
          actionAbstract: {
            result: '112312444',
            target: '1221',
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-29 18:04:46.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            revisitId: '3810476669687840768',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            executorId: '6e45706922a74966ab51e4ed1e604641',
            revisitMode: 0,
            executorName: '丁柱11',
            outpatientDiagnosis: '急性上呼吸道感染，急性上呼吸道感染',
          },
          recordCreated: '2024-11-30T13:06:53.000+00:00',
          recordHappened: '2024-11-30T13:06:54.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271665971969916929',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '随访',
          actionId: '3810521850596311040',
          actionAbstract: {
            result: '122132',
            target: '1221',
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-30 17:27:22.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            revisitId: '3810521850596311040',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            executorId: '6e45706922a74966ab51e4ed1e604641',
            revisitMode: 0,
            executorName: '丁柱11',
            outpatientDiagnosis: '急性上呼吸道感染，急性上呼吸道感染',
          },
          recordCreated: '2024-11-30T13:04:20.000+00:00',
          recordHappened: '2024-11-30T13:04:20.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271661413566775423',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '随访',
          actionId: '3810383564997296129',
          actionAbstract: {
            result: '212312312321312321\n123123\n12312\n2131\n123\n123\n123',
            target: '13123',
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-27 17:54:25.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            revisitId: '3810383564997296129',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            executorId: '6e45706922a74966ab51e4ed1e604641',
            revisitMode: 0,
            executorName: '丁柱11',
            outpatientDiagnosis: null,
          },
          recordCreated: '2024-11-27T09:54:47.000+00:00',
          recordHappened: '2024-11-30T12:21:20.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314763',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e1b16044ec8004',
          actionAbstract: {
            id: 'ffffffff0000000034e1b16044ec8004',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e0321854f84000',
                name: '血常规(云检)+CRP',
              },
            ],
          },
          recordCreated: '2024-11-30T09:26:38.000+00:00',
          recordHappened: '2024-11-30T09:26:39.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314764',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e1b169c4ec8000',
          actionAbstract: {
            id: 'ffffffff0000000034e1b169c4ec8000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000022ad386011624000',
                name: '模板单项21',
              },
            ],
          },
          recordCreated: '2024-11-30T09:26:38.000+00:00',
          recordHappened: '2024-11-30T09:26:39.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314761',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e1b16044ec8000',
          actionAbstract: {
            id: 'ffffffff0000000034e1b16044ec8000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e0321894f84001',
                name: '血常规(云检)+CRP+SAA',
              },
            ],
          },
          recordCreated: '2024-11-30T09:26:38.000+00:00',
          recordHappened: '2024-11-30T09:26:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314762',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e1b16044ec8002',
          actionAbstract: {
            id: 'ffffffff0000000034e1b16044ec8002',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e03217d4f84000',
                name: '血常规(云检)',
              },
            ],
          },
          recordCreated: '2024-11-30T09:26:38.000+00:00',
          recordHappened: '2024-11-30T09:26:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314755',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e1b15fe28c0002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            reserveEnd: '18:00',
            reserveDate: '2024-11-30',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '12:00',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e1b15fe28c0000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-30T09:25:20.000+00:00',
          recordHappened: '2024-11-30T09:25:20.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664609559314756',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e1b10184500000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-30 17:25:20.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染，急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            receivedFee: 52.0,
            outpatientId: 'ffffffff0000000034e1b10184500000',
            diagnosedDate: '2024-11-30 17:25:20.000000',
            chiefComplaint: '干咳<br>咳嗽<br>咳嗽',
            patientOrderId: 'ffffffff0000000034e1b15fe229c000',
          },
          recordCreated: '2024-11-30T09:25:20.000+00:00',
          recordHappened: '2024-11-30T09:25:20.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664164174561524',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e18848a4ec8000',
          actionAbstract: {
            id: 'ffffffff0000000034e18848a4ec8000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e03217d4f84000',
                name: '血常规(云检)',
              },
            ],
          },
          recordCreated: '2024-11-29T10:04:07.000+00:00',
          recordHappened: '2024-11-29T10:04:07.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664164174561525',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e18848a4ec8002',
          actionAbstract: {
            id: 'ffffffff0000000034e18848a4ec8002',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e0321894f84001',
                name: '血常规(云检)+CRP+SAA',
              },
            ],
          },
          recordCreated: '2024-11-29T10:04:07.000+00:00',
          recordHappened: '2024-11-29T10:04:07.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664164174561526',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e18848a4ec8004',
          actionAbstract: {
            id: 'ffffffff0000000034e18848a4ec8004',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034e0321854f84000',
                name: '血常规(云检)+CRP',
              },
            ],
          },
          recordCreated: '2024-11-29T10:04:07.000+00:00',
          recordHappened: '2024-11-29T10:04:07.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664164174561518',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e18848628c0002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'c9f05d5756454a04bb279a2e3f4effca',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '吴成芳',
            reserveEnd: '23:50',
            reserveDate: '2024-11-29',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '18:02',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e18848628c0000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-29T10:02:43.000+00:00',
          recordHappened: '2024-11-29T10:02:43.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271664164174561519',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e1884424500000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-29 18:02:43.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'c9f05d5756454a04bb279a2e3f4effca',
            diagnosis: '急性上呼吸道感染，急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '吴成芳',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034e1884424500000',
            diagnosedDate: '2024-11-29 18:02:43.000000',
            chiefComplaint: '干咳<br>咳嗽<br>咳嗽',
            patientOrderId: 'ffffffff0000000034e188484229c000',
          },
          recordCreated: '2024-11-29T10:02:43.000+00:00',
          recordHappened: '2024-11-29T10:02:43.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271662674475221091',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e15701828b4002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            reserveEnd: '18:00',
            reserveDate: '2024-11-28',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '12:00',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e15701828b4000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-28T06:00:44.000+00:00',
          recordHappened: '2024-11-28T06:00:44.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271662674475221093',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e15171a448c000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-28 14:00:44.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034e15171a448c000',
            diagnosedDate: '2024-11-28 14:00:44.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034e1570162298000',
          },
          recordCreated: '2024-11-28T06:00:44.000+00:00',
          recordHappened: '2024-11-28T06:00:44.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271661413566775424',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '随访',
          actionId: '3810381199544057856',
          actionAbstract: {
            result: '123123',
            target: '1221',
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-27 16:40:59.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            revisitId: '3810381199544057856',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            executorId: '6e45706922a74966ab51e4ed1e604641',
            revisitMode: 0,
            executorName: '丁柱',
            outpatientDiagnosis: '急性上呼吸道感染',
          },
          recordCreated: '2024-11-27T09:59:48.000+00:00',
          recordHappened: '2024-11-27T09:59:48.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271661413566775398',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e13174228b0002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '16:53',
            reserveDate: '2024-11-27',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '16:38',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e13174228b0000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-27T08:38:57.000+00:00',
          recordHappened: '2024-11-27T08:38:57.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271661413566775399',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e13166c448c000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-27 16:38:57.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034e13166c448c000',
            diagnosedDate: '2024-11-27 16:38:57.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034e1317422298000',
          },
          recordCreated: '2024-11-27T08:38:57.000+00:00',
          recordHappened: '2024-11-27T08:38:57.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271661413566775389',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e130f2228b0002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 30,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '16:36',
            reserveDate: '2024-11-27',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '16:21',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e130f2228b0000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-27T08:21:38.000+00:00',
          recordHappened: '2024-11-27T08:21:37.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271658863799042494',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e0fd8c02870002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 21,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '12:00',
            reserveDate: '2024-11-26',
            departmentId: 'ffffffff0000000034db191ee2ce8000',
            reserveStart: '11:07',
            departmentName: 'Bubble-CT',
            registrationSheetId: 'ffffffff0000000034e0fd8c02870000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-26T03:07:12.000+00:00',
          recordHappened: '2024-11-26T03:07:12.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271652455842054426',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e03276a4eb4000',
          actionAbstract: {
            id: 'ffffffff0000000034e03276a4eb4000',
            status: 0,
            testerId: '6e45706922a74966ab51e4ed1e604641',
            testerName: '丁柱11',
            productInfos: [
              {
                id: 'ffffffff0000000034e03217d4f84000',
                name: '血常规(云检)',
              },
            ],
          },
          recordCreated: '2024-11-21T07:35:55.000+00:00',
          recordHappened: '2024-11-21T08:21:08.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271652455842054427',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e03276a4eb4002',
          actionAbstract: {
            id: 'ffffffff0000000034e03276a4eb4002',
            status: 0,
            testerId: '6e45706922a74966ab51e4ed1e604641',
            testerName: '丁柱11',
            productInfos: [
              {
                id: 'ffffffff0000000034e0321854f84000',
                name: '血常规(云检)+CRP',
              },
            ],
          },
          recordCreated: '2024-11-21T07:35:55.000+00:00',
          recordHappened: '2024-11-21T08:21:08.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271652455842054428',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034e03276a4eb4004',
          actionAbstract: {
            id: 'ffffffff0000000034e03276a4eb4004',
            status: 0,
            testerId: '6e45706922a74966ab51e4ed1e604641',
            testerName: '丁柱11',
            productInfos: [
              {
                id: 'ffffffff0000000034e0321894f84001',
                name: '血常规(云检)+CRP+SAA',
              },
            ],
          },
          recordCreated: '2024-11-21T07:35:55.000+00:00',
          recordHappened: '2024-11-21T08:21:08.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271652455842054420',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e0327682870002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            reserveEnd: '18:00',
            reserveDate: '2024-11-21',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '12:00',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e0327682870000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-21T07:35:16.000+00:00',
          recordHappened: '2024-11-21T07:35:16.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271652455842054421',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e0327324444000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-21 15:35:16.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱11',
            receivedFee: 100.0,
            outpatientId: 'ffffffff0000000034e0327324444000',
            diagnosedDate: '2024-11-21 15:35:16.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034e032768227c000',
          },
          recordCreated: '2024-11-21T07:35:16.000+00:00',
          recordHappened: '2024-11-21T07:35:16.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271651489340195173',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '执行',
          actionId: 'ffffffff0000000034e027df2818c006',
          actionAbstract: {
            treatmentItems: [
              {
                id: 'ffffffff0000000034e027df2818c008',
                productId: '395d239da474c3690a124a866615e8b8',
                productName: '骨伤、颈腰整脊手法',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
              {
                id: 'ffffffff0000000034e027df2818c009',
                productId: '245d7404769c016581350b9e23258a10',
                productName: '针灸减肥',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
              {
                id: 'ffffffff0000000034e027df2818c00a',
                productId: '3f8dbe8858221e0d6b0fac95d05544b4',
                productName: '妇科检查（普通）',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
              {
                id: 'ffffffff0000000034e027df2818c00b',
                productId: '0dd6658b57d39435a66d1590c7ea9a34',
                productName: '理疗1',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
            ],
          },
          recordCreated: '2024-11-21T01:34:01.000+00:00',
          recordHappened: '2024-11-21T06:10:28.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271651489340195168',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034e027df22870002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '胡传明',
            reserveEnd: '12:00',
            reserveDate: '2024-11-21',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '09:33',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034e027df22870000',
            registrationCategory: 0,
          },
          recordCreated: '2024-11-21T01:33:45.000+00:00',
          recordHappened: '2024-11-21T01:33:45.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271651489340195170',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034e027dc44444000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-21 09:33:45.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '胡传明',
            receivedFee: 169.0,
            outpatientId: 'ffffffff0000000034e027dc44444000',
            diagnosedDate: '2024-11-21 09:33:45.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034e027df0227c000',
          },
          recordCreated: '2024-11-21T01:33:45.000+00:00',
          recordHappened: '2024-11-21T01:33:45.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271619752115574950',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034dd84f864d90000',
          actionAbstract: {
            id: 'ffffffff0000000034dd84f864d90000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034a58bddab798000',
                name: '血常规+CRP',
              },
            ],
          },
          recordCreated: '2024-11-05T01:37:07.000+00:00',
          recordHappened: '2024-11-05T01:37:08.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271619752115574949',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '零售',
          actionId: 'ffffffff0000000034dd84f848060000',
          actionAbstract: {
            type: 3,
            items: 2,
            amount: 69.2,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-11-05 09:37:07.000000',
            clinicId: '2780a41d23364bc29e31e5a353464ad1',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '河北新禾发票测试',
            receivedFee: 69.2,
            chargeSheetId: 'ffffffff0000000034dd84f848060000',
            receivableFee: 69.2,
            patientOrderId: 'ffffffff0000000034dd84f842274000',
          },
          recordCreated: '2024-11-05T01:37:07.000+00:00',
          recordHappened: '2024-11-05T01:37:07.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271619752115570804',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034dcb9ac02758002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-31',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:57',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034dcb9ac02758000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-31T05:57:52.000+00:00',
          recordHappened: '2024-10-31T05:57:52.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049204700',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034dc45f7e4d5c000',
          actionAbstract: {
            id: 'ffffffff0000000034dc45f7e4d5c000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034a58bddab798000',
                name: '血常规+CRP',
              },
            ],
          },
          recordCreated: '2024-10-28T12:08:31.000+00:00',
          recordHappened: '2024-10-28T12:08:32.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049204699',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034dc45f7c7ff8000',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 66.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-28 20:08:31.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 66.0,
            chargeSheetId: 'ffffffff0000000034dc45f7c7ff8000',
            receivableFee: 66.0,
            patientOrderId: 'ffffffff0000000034dc45f7c2268000',
          },
          recordCreated: '2024-10-28T12:08:31.000+00:00',
          recordHappened: '2024-10-28T12:08:31.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202334',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: 'ffffffff0000000034db9f6be4d08000',
          actionAbstract: {
            id: 'ffffffff0000000034db9f6be4d08000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000022ad386011624000',
                name: '模板单项21',
              },
            ],
          },
          recordCreated: '2024-10-24T13:23:43.000+00:00',
          recordHappened: '2024-10-24T13:23:44.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202333',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034db9f6bc7fc4000',
          actionAbstract: {
            type: 3,
            items: 2,
            amount: 222.75,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-24 21:23:43.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 222.75,
            chargeSheetId: 'ffffffff0000000034db9f6bc7fc4000',
            receivableFee: 222.75,
            patientOrderId: 'ffffffff0000000034db9f6ba2268000',
          },
          recordCreated: '2024-10-24T13:23:43.000+00:00',
          recordHappened: '2024-10-24T13:23:43.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202096',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034db973964350000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-24 16:45:01.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '1',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '赵振',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034db973964350000',
            diagnosedDate: '2024-10-24 16:45:01.000000',
            chiefComplaint: '1',
            patientOrderId: 'ffffffff0000000034db974182268000',
          },
          recordCreated: '2024-10-24T08:45:00.000+00:00',
          recordHappened: '2024-10-24T08:45:01.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202094',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db97418272c002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '赵振',
            reserveEnd: '18:00',
            reserveDate: '2024-10-24',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '16:45',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db97418272c000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-24T08:45:00.000+00:00',
          recordHappened: '2024-10-24T08:45:00.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202083',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034db971347fc4010',
          actionAbstract: {
            type: 3,
            items: 2,
            amount: 6.0,
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-24 16:38:51.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 6.0,
            chargeSheetId: 'ffffffff0000000034db971347fc4010',
            receivableFee: 69.0,
            patientOrderId: 'ffffffff0000000034db971342268000',
          },
          recordCreated: '2024-10-24T08:38:51.000+00:00',
          recordHappened: '2024-10-24T08:39:09.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202085',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: '3808804019423363076',
          actionAbstract: {
            id: '3808804019423363076',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-10-24T08:38:51.000+00:00',
          recordHappened: '2024-10-24T08:38:57.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202084',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: '3808804019423363075',
          actionAbstract: {
            id: '3808804019423363075',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034a58bddab798000',
                name: '血常规+CRP',
              },
            ],
          },
          recordCreated: '2024-10-24T08:38:51.000+00:00',
          recordHappened: '2024-10-24T08:38:56.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202009',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检查',
          actionId: 'ffffffff0000000034db941f04cf4000',
          actionAbstract: {
            id: 'ffffffff0000000034db941f04cf4000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034b67f612e7f8000',
                name: '彩超检查',
              },
            ],
          },
          recordCreated: '2024-10-24T06:58:00.000+00:00',
          recordHappened: '2024-10-24T06:58:01.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202004',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '预约',
          actionId: 'ffffffff0000000034db941d8272c002',
          actionAbstract: {
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            doctorId: '5f04343577a24017a9813376f7cdf779',
            statusV2: 40,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            doctorName: '向妍玫',
            reserveEnd: '18:00',
            reserveDate: '2024-10-24',
            departmentId: '810ec8d53189e695f7afd5259be1d0da',
            reserveStart: '14:57',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034db941d8272c000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-24T06:57:48.000+00:00',
          recordHappened: '2024-10-24T06:57:48.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049202005',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '门诊',
          actionId: 'ffffffff0000000034db941b44350000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-24 14:57:48.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            doctorId: '5f04343577a24017a9813376f7cdf779',
            diagnosis: '急性上呼吸道感染',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            doctorName: '向妍玫',
            receivedFee: 109.0,
            outpatientId: 'ffffffff0000000034db941b44350000',
            diagnosedDate: '2024-10-24 14:57:48.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034db941d82268000',
          },
          recordCreated: '2024-10-24T06:57:48.000+00:00',
          recordHappened: '2024-10-24T06:57:48.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049199315',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '随访',
          actionId: '3808752290245951488',
          actionAbstract: {
            result: '人的人人才',
            target: '131',
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-23 13:52:58.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            revisitId: '3808752290245951488',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            executorId: '10a861eb5b4d43b59721301073112029',
            revisitMode: 0,
            executorName: '杨明昆-改2',
            outpatientDiagnosis: '胃炎',
          },
          recordCreated: '2024-10-23T09:54:13.000+00:00',
          recordHappened: '2024-10-23T09:54:13.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049198065',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: '3808752239297478658',
          actionAbstract: {
            id: '3808752239297478658',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000022ad386011624000',
                name: '模板单项21',
              },
            ],
          },
          recordCreated: '2024-10-23T05:51:23.000+00:00',
          recordHappened: '2024-10-23T05:51:23.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049198059',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db67f822724002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-23',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:50',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db67f822724000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-23T05:50:57.000+00:00',
          recordHappened: '2024-10-23T05:50:57.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049198061',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034db67f344350000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-23 13:50:57.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '胃炎',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            receivedFee: 116.0,
            outpatientId: 'ffffffff0000000034db67f344350000',
            diagnosedDate: '2024-10-23 13:50:57.000000',
            chiefComplaint: '胃胀，胃痛',
            patientOrderId: 'ffffffff0000000034db67f802268000',
          },
          recordCreated: '2024-10-23T05:50:57.000+00:00',
          recordHappened: '2024-10-23T05:50:57.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049195754',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db3d6202720002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-22',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:37',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db3d6202720000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-22T05:37:20.000+00:00',
          recordHappened: '2024-10-22T05:37:20.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049195753',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db3d6102720002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-22',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:37',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db3d6102720000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-22T05:37:12.000+00:00',
          recordHappened: '2024-10-22T05:37:12.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049195752',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db3d5fe2720002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-22',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:37',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db3d5fe2720000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-22T05:37:04.000+00:00',
          recordHappened: '2024-10-22T05:37:03.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049195751',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db3d5f42720002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '18:00',
            reserveDate: '2024-10-22',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '13:36',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db3d5f42720000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-22T05:36:58.000+00:00',
          recordHappened: '2024-10-22T05:36:58.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049181511',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034db19700271c002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000213b38b8010ba000',
            statusV2: 30,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '冉毅',
            reserveEnd: '18:00',
            reserveDate: '2024-10-21',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '17:10',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034db19700271c000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-21T09:10:25.000+00:00',
          recordHappened: '2024-10-21T09:10:24.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049179381',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: '3808479421900931075',
          actionAbstract: {
            id: '3808479421900931075',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bfa69270009',
                name: '血小板计数',
              },
            ],
          },
          recordCreated: '2024-10-17T08:42:02.000+00:00',
          recordHappened: '2024-10-17T08:42:02.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049179382',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '检验',
          actionId: '3808479421900931076',
          actionAbstract: {
            id: '3808479421900931076',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034a58bddab798000',
                name: '血常规+CRP',
              },
            ],
          },
          recordCreated: '2024-10-17T08:42:02.000+00:00',
          recordHappened: '2024-10-17T08:42:02.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049179380',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034da6fdb07f5400f',
          actionAbstract: {
            type: 3,
            items: 2,
            amount: 67.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-17 16:42:02.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 67.0,
            chargeSheetId: 'ffffffff0000000034da6fdb07f5400f',
            receivableFee: 67.0,
            patientOrderId: 'ffffffff0000000034da6fdb02264000',
          },
          recordCreated: '2024-10-17T08:42:01.000+00:00',
          recordHappened: '2024-10-17T08:42:01.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049173930',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da64ea62708002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 21,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '12:00',
            reserveDate: '2024-10-17',
            departmentId: 'ffffffff0000000034d5f7e9fdad0000',
            reserveStart: '10:28',
            departmentName: '心电图科室',
            registrationSheetId: 'ffffffff0000000034da64ea62708000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-17T02:28:35.000+00:00',
          recordHappened: '2024-10-17T02:28:35.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049173925',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da64ea42708002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            reserveEnd: '12:00',
            reserveDate: '2024-10-17',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '10:28',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034da64ea42708000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-17T02:28:34.000+00:00',
          recordHappened: '2024-10-17T02:28:34.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049173926',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034da64e804344000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-17 10:28:34.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '急性上呼吸道感染',
            disposals: [],
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '丁柱',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034da64e804344000',
            diagnosedDate: '2024-10-17 10:28:34.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034da64ea22264000',
          },
          recordCreated: '2024-10-17T02:28:34.000+00:00',
          recordHappened: '2024-10-17T02:28:34.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049172378',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da1d1402700002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000349788bdffbd4000',
            statusV2: 30,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: 'haoxinkun',
            reserveEnd: '18:00',
            reserveDate: '2024-10-15',
            departmentId: '02dd875836d30576409f97000062ce50',
            reserveStart: '12:00',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034da1d1402700000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-15T09:36:33.000+00:00',
          recordHappened: '2024-10-15T09:36:32.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049172377',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da1d07e2700002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 30,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '18:00',
            reserveDate: '2024-10-15',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '17:34',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034da1d07e2700000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-15T09:34:56.000+00:00',
          recordHappened: '2024-10-15T09:34:55.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049171964',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '执行',
          actionId: 'ffffffff0000000034da18c467ed4003',
          actionAbstract: {
            treatmentItems: [
              {
                id: 'ffffffff0000000034da18c467ed4005',
                productId: '395d239da474c3690a124a866615e8b8',
                productName: '骨伤、颈腰整脊手法',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
            ],
          },
          recordCreated: '2024-10-15T07:09:50.000+00:00',
          recordHappened: '2024-10-15T07:09:50.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049171959',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da18c462700002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000028a8ad800a0a000',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '邓毅111',
            reserveEnd: '18:00',
            reserveDate: '2024-10-15',
            departmentId: '02dd875836d30576409f97000062ce50',
            reserveStart: '15:09',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034da18c462700000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-15T07:09:24.000+00:00',
          recordHappened: '2024-10-15T07:09:23.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049171960',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034da18c0e4314000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-10-15 15:09:24.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000028a8ad800a0a000',
            diagnosis: '1',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '邓毅111',
            receivedFee: 51.0,
            outpatientId: 'ffffffff0000000034da18c0e4314000',
            diagnosedDate: '2024-10-15 15:09:23.000000',
            chiefComplaint: '1',
            patientOrderId: 'ffffffff0000000034da18c462264000',
          },
          recordCreated: '2024-10-15T07:09:24.000+00:00',
          recordHappened: '2024-10-15T07:09:23.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271593305049165183',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034da10fa826f8002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000349788bdffbd4000',
            statusV2: 20,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: 'haoxinkun',
            reserveEnd: '12:00',
            reserveDate: '2024-10-15',
            departmentId: '02dd875836d30576409f97000062ce50',
            reserveStart: '06:00',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034da10fa826f8000',
            registrationCategory: 0,
          },
          recordCreated: '2024-10-15T02:43:33.000+00:00',
          recordHappened: '2024-10-15T02:43:32.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970792939',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034d6a0e1626d4002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 21,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '18:00',
            reserveDate: '2024-09-24',
            departmentId: 'ffffffff0000000034c03935cbaa4000',
            reserveStart: '14:02',
            departmentName: '检查科室',
            registrationSheetId: 'ffffffff0000000034d6a0e1626d4000',
            registrationCategory: 0,
          },
          recordCreated: '2024-09-24T06:03:11.000+00:00',
          recordHappened: '2024-09-24T06:02:51.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784312',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检查',
          actionId: 'ffffffff0000000034d5fe5ba4bf4000',
          actionAbstract: {
            id: 'ffffffff0000000034d5fe5ba4bf4000',
            status: 1,
            testerId: '6e45706922a74966ab51e4ed1e604641',
            testerName: '彭磊',
            productInfos: [
              {
                id: 'ffffffff0000000034b67f612e7f8000',
                name: '彩超检查',
              },
            ],
          },
          recordCreated: '2024-09-20T09:35:25.000+00:00',
          recordHappened: '2024-09-20T09:36:00.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784314',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034d5fe5fe26c8002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 21,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '23:50',
            reserveDate: '2024-09-20',
            departmentId: '34608ebb10a141c1889364d506a56cb1',
            reserveStart: '18:00',
            departmentName: '儿科急诊科室',
            registrationSheetId: 'ffffffff0000000034d5fe5fe26c8000',
            registrationCategory: 0,
          },
          recordCreated: '2024-09-20T09:36:00.000+00:00',
          recordHappened: '2024-09-20T09:35:59.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784311',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '零售',
          actionId: 'ffffffff0000000034d5fe5b87dd8000',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 9.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 17:35:25.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            receivedFee: 9.0,
            chargeSheetId: 'ffffffff0000000034d5fe5b87dd8000',
            receivableFee: 9.0,
            patientOrderId: 'ffffffff0000000034d5fe5b82260000',
          },
          recordCreated: '2024-09-20T09:35:24.000+00:00',
          recordHappened: '2024-09-20T09:35:24.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784305',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检查',
          actionId: 'ffffffff0000000034d5fe4984bf4000',
          actionAbstract: {
            id: 'ffffffff0000000034d5fe4984bf4000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034b67f612e7f8000',
                name: '彩超检查',
              },
            ],
          },
          recordCreated: '2024-09-20T09:33:00.000+00:00',
          recordHappened: '2024-09-20T09:33:01.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784304',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '零售',
          actionId: 'ffffffff0000000034d5fe4967dd8000',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 9.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 17:33:00.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            receivedFee: 9.0,
            chargeSheetId: 'ffffffff0000000034d5fe4967dd8000',
            receivableFee: 9.0,
            patientOrderId: 'ffffffff0000000034d5fe4962260000',
          },
          recordCreated: '2024-09-20T09:33:00.000+00:00',
          recordHappened: '2024-09-20T09:33:00.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784223',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检验',
          actionId: 'ffffffff0000000034d5fd44e4bf0000',
          actionAbstract: {
            id: 'ffffffff0000000034d5fd44e4bf0000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034d039ff92530000',
                name: '测试2232112',
              },
            ],
          },
          recordCreated: '2024-09-20T08:58:15.000+00:00',
          recordHappened: '2024-09-20T08:58:16.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784222',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '零售',
          actionId: 'ffffffff0000000034d5fd44c7dd0000',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 1.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 16:58:15.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            receivedFee: 1.0,
            chargeSheetId: 'ffffffff0000000034d5fd44c7dd0000',
            receivableFee: 1.0,
            patientOrderId: 'ffffffff0000000034d5fd44c2260000',
          },
          recordCreated: '2024-09-20T08:58:15.000+00:00',
          recordHappened: '2024-09-20T08:58:15.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784212',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检验',
          actionId: 'ffffffff0000000034d5fd3c44bf0000',
          actionAbstract: {
            id: 'ffffffff0000000034d5fd3c44bf0000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034c2000a90878000',
                name: '组合-合并-A',
              },
            ],
          },
          recordCreated: '2024-09-20T08:57:06.000+00:00',
          recordHappened: '2024-09-20T08:57:07.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970784211',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '零售',
          actionId: 'ffffffff0000000034d5fd3c27dd0010',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 1.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 16:57:06.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            receivedFee: 1.0,
            chargeSheetId: 'ffffffff0000000034d5fd3c27dd0010',
            receivableFee: 1.0,
            patientOrderId: 'ffffffff0000000034d5fd3c22260000',
          },
          recordCreated: '2024-09-20T08:57:06.000+00:00',
          recordHappened: '2024-09-20T08:57:06.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970783778',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034d5f9ec226c8002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '',
            statusV2: 21,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: null,
            reserveEnd: '12:00',
            reserveDate: '2024-09-20',
            departmentId: '34608ebb10a141c1889364d506a56cb1',
            reserveStart: '11:59',
            departmentName: '儿科急诊科室',
            registrationSheetId: 'ffffffff0000000034d5f9ec226c8000',
            registrationCategory: 0,
          },
          recordCreated: '2024-09-20T07:04:01.000+00:00',
          recordHappened: '2024-09-20T07:04:01.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970783775',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '检查',
          actionId: 'ffffffff0000000034d5f9ea04bec000',
          actionAbstract: {
            id: 'ffffffff0000000034d5f9ea04bec000',
            status: 1,
            testerId: 'ffffffff0000000024dce4d801e9a000',
            testerName: '许赵飞',
            productInfos: [
              {
                id: 'ffffffff0000000034b67f612e7f8000',
                name: '彩超检查',
              },
            ],
          },
          recordCreated: '2024-09-20T07:03:44.000+00:00',
          recordHappened: '2024-09-20T07:04:00.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970783776',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
          action: '零售',
          actionId: 'ffffffff0000000034d5f9e9e7dc0000',
          actionAbstract: {
            type: 3,
            items: 1,
            amount: 9.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 15:03:44.000000',
            clinicId: 'b0aef05be62b4ef186bafb694c5bae3a',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '杭州亦启科技有限公司萧山鱼浦中医诊所诊所',
            receivedFee: 9.0,
            chargeSheetId: 'ffffffff0000000034d5f9e9e7dc0000',
            receivableFee: 9.0,
            patientOrderId: 'ffffffff0000000034d5f9e9e2260000',
          },
          recordCreated: '2024-09-20T07:03:44.000+00:00',
          recordHappened: '2024-09-20T07:03:44.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970783338',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '预约',
          actionId: 'ffffffff0000000034d5f2acc26c8002',
          actionAbstract: {
            clinicId: '2780a41d23364bc29e31e5a353464ad1',
            doctorId: '566fdc8b20c64d6d953e9c36991c4830',
            statusV2: 40,
            clinicName: '河北新禾发票测试',
            doctorName: 'wxd',
            reserveEnd: '12:00',
            reserveDate: '2024-09-20',
            departmentId: 'fd2ce61aa6ca5d6c877a5b172aec1cae',
            reserveStart: '10:56',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034d5f2acc26c8000',
            registrationCategory: 0,
          },
          recordCreated: '2024-09-20T02:56:38.000+00:00',
          recordHappened: '2024-09-20T02:56:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271561154970783339',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '门诊',
          actionId: 'ffffffff0000000034d5f2a4a4268000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-20 10:56:38.000000',
            clinicId: '2780a41d23364bc29e31e5a353464ad1',
            doctorId: '566fdc8b20c64d6d953e9c36991c4830',
            diagnosis: '急性上呼吸道感染',
            disposals: [],
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '河北新禾发票测试',
            doctorName: 'wxd',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034d5f2a4a4268000',
            diagnosedDate: '2024-09-20 10:56:38.000000',
            chiefComplaint: '咳嗽',
            patientOrderId: 'ffffffff0000000034d5f2acc2258000',
          },
          recordCreated: '2024-09-20T02:56:38.000+00:00',
          recordHappened: '2024-09-20T02:56:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271550337172900764',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034d4d5cba7d2800d',
          actionAbstract: {
            type: 3,
            items: 3,
            amount: 10.0,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-13 16:52:46.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 10.0,
            chargeSheetId: 'ffffffff0000000034d4d5cba7d2800d',
            receivableFee: 10.0,
            patientOrderId: 'ffffffff0000000034d4d5cba2258000',
          },
          recordCreated: '2024-09-13T08:52:47.000+00:00',
          recordHappened: '2024-09-13T08:52:46.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271528164723010366',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034d3355b4265c002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            statusV2: 40,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '李颖',
            reserveEnd: '20:13',
            reserveDate: '2024-09-03',
            departmentId: '59140f8ecdeb4553ab570f710274e0ab',
            reserveStart: '19:58',
            departmentName: '小儿外科诊室',
            registrationSheetId: 'ffffffff0000000034d3355b4265c000',
            registrationCategory: 0,
          },
          recordCreated: '2024-09-03T11:58:19.000+00:00',
          recordHappened: '2024-09-03T11:58:18.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271528164723010367',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '门诊',
          actionId: 'ffffffff0000000034d33559641ac000',
          actionAbstract: {
            status: 1,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-09-03 19:58:19.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: '6e45706922a74966ab51e4ed1e604641',
            diagnosis: '1',
            disposals: null,
            patientId: 'ffffffff0000000008eaac500470c000',
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: '李颖',
            receivedFee: 0.0,
            outpatientId: 'ffffffff0000000034d33559641ac000',
            diagnosedDate: '2024-09-03 19:58:18.000000',
            chiefComplaint: '1',
            patientOrderId: 'ffffffff0000000034d3355b42244000',
          },
          recordCreated: '2024-09-03T11:58:20.000+00:00',
          recordHappened: '2024-09-03T11:58:18.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452542551',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '零售',
          actionId: 'ffffffff0000000034d1e94e87898000',
          actionAbstract: {
            type: 3,
            items: 7,
            amount: 96.25,
            status: 2,
            chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
            created: '2024-08-26 23:04:22.000000',
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            patientId: 'ffffffff0000000008eaac500470c000',
            refundFee: 0.0,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            receivedFee: 96.25,
            chargeSheetId: 'ffffffff0000000034d1e94e87898000',
            receivableFee: 96.25,
            patientOrderId: 'ffffffff0000000034d1e94e82244000',
          },
          recordCreated: '2024-08-26T15:04:22.000+00:00',
          recordHappened: '2024-08-26T15:04:22.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452539341',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
          action: '预约',
          actionId: 'ffffffff0000000034d136b802550002',
          actionAbstract: {
            clinicId: 'fff730ccc5ee45d783d82a85b8a0e52d',
            doctorId: 'ffffffff00000000349788bdffbd4000',
            statusV2: 30,
            clinicName: '四川省成都市高新区交子大道高新大源店',
            doctorName: 'haoxinkun',
            reserveEnd: '17:45',
            reserveDate: '2024-08-22',
            departmentId: '02dd875836d30576409f97000062ce50',
            reserveStart: '17:30',
            departmentName: '',
            registrationSheetId: 'ffffffff0000000034d136b802550000',
            registrationCategory: 0,
          },
          recordCreated: '2024-08-22T09:28:33.000+00:00',
          recordHappened: '2024-08-22T09:28:32.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536685',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc000',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034a58bddab798000',
                name: '血常规+CRP',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536686',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc003',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc003',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c0029270010',
                name: '无机磷测定',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536687',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc004',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc004',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bfee9270011',
                name: '甲型肝炎抗体测定(Anti-HAV)',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536688',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc005',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc005',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536689',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc006',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc006',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536690',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629249fc007',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc007',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536691',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629449fc000',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629449fc000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536692',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d629449fc001',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629449fc001',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930c014927000e',
                name: '尿常规检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536693',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检查',
          actionId: 'ffffffff0000000034d0d629249fc001',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc001',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034c0105a30534000',
                name: 'DR检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536694',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检查',
          actionId: 'ffffffff0000000034d0d629249fc002',
          actionAbstract: {
            id: 'ffffffff0000000034d0d629249fc002',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034c0105710534000',
                name: 'CT检查',
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:43.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536714',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d646a49fc000',
          actionAbstract: {
            id: 'ffffffff0000000034d0d646a49fc000',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bfce9270008',
                name: 'M蛋白测定',
              },
            ],
          },
          recordCreated: '2024-08-20T02:36:38.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536715',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d646a49fc001',
          actionAbstract: {
            id: 'ffffffff0000000034d0d646a49fc001',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bff69270004',
                name: '尿液人类免疫缺陷病毒I型（HIV-I）抗体测定(定性)',
              },
            ],
          },
          recordCreated: '2024-08-20T02:36:38.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536716',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d646a49fc002',
          actionAbstract: {
            id: 'ffffffff0000000034d0d646a49fc002',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bfde927000e',
                name: 'B型钠尿肽（BNP）测定（酶免法等）',
              },
            ],
          },
          recordCreated: '2024-08-20T02:36:38.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536717',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '检验',
          actionId: 'ffffffff0000000034d0d646a49fc003',
          actionAbstract: {
            id: 'ffffffff0000000034d0d646a49fc003',
            status: 0,
            testerId: null,
            testerName: null,
            productInfos: [
              {
                id: 'ffffffff0000000034930bfde9270006',
                name: '尿微量白蛋白测定(免疫学法等)',
              },
            ],
          },
          recordCreated: '2024-08-20T02:36:38.000+00:00',
          recordHappened: '2024-08-20T02:36:38.000+00:00',
          isDeleted: 0,
        },
        {
          id: '3271516521452536684',
          patientId: 'ffffffff0000000008eaac500470c000',
          chainId: '6a869c22abee4ffbaef3e527bbb70aeb',
          clinicId: '2780a41d23364bc29e31e5a353464ad1',
          action: '执行',
          actionId: 'ffffffff0000000034d0d6290773c000',
          actionAbstract: {
            treatmentItems: [
              {
                id: 'ffffffff0000000034d0d6290773c00e',
                productId: 'fa6493cc66be90af4639c535d283500e',
                productName: '超导给药',
                executeStatus: 1,
                executedUnitCount: 0.0,
                executeUnitTotalCount: 1.0,
              },
            ],
          },
          recordCreated: '2024-08-20T02:32:42.000+00:00',
          recordHappened: '2024-08-20T02:36:37.000+00:00',
          isDeleted: 0,
        },
      ],
      last: true,
      loading: false,
    }
  },
  methods: {
    fetchRevisitQuickList() {
      console.log('拉取数据')
    },
    handleOpenDetail(type, item) {
      console.log('打开弹窗', type, item)
    },
    openAppointmentCardFromTrack(event) {
      console.log('挂载预约弹窗', event)
    },
  },
}
</script>
