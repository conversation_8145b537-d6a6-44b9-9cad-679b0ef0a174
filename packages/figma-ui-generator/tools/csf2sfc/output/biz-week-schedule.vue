<template>
  <BizWeekSchedule :data="list" :content-max-height="300">
    <template #headerPrepend>
      <BizWeekScheduleCell excel>
        <abc-select v-model="size" adaptive-width size="large">
          <abc-option value="default" label="default"></abc-option>
          <abc-option value="large" label="large"></abc-option>
        </abc-select>
      </BizWeekScheduleCell>
    </template>
    <template #bodyPrepend="{ data }">
      <BizWeekScheduleCell>
        <div style="height: 100%; display: flex; align-items: center">
          {{ data.label }}
        </div>
      </BizWeekScheduleCell>
    </template>
    <template #default="{ data, config }">
      <BizWeekScheduleCell
        :background="data.scheduleShifts.length > 0"
        :date="config.date"
      >
        <div v-for="(o, key) in data.scheduleShifts" :key="key">{{ o }}</div>
      </BizWeekScheduleCell>
    </template>
  </BizWeekSchedule>
</template>
<script>
export default {
  data() {
    return {
      size: 'large',
      list: [
        {
          label: '不指定医生',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
        {
          label: '不指定医生0',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
        {
          label: '不指定医生1',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['11', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
        {
          label: '不指定医生2',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['1'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
          ],
        },
        {
          label: '不指定医生3',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
        {
          label: '不指定医生',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
        {
          label: '不指定医生',
          schedules: [
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: [],
            },
            {
              scheduleShifts: ['10', '12'],
            },
            {
              scheduleShifts: ['10', '12'],
            },
          ],
        },
      ],
    }
  },
}
</script>
