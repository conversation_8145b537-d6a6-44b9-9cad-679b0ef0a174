<template>
  <abc-flex vertical style="width: 200px" gap="large">
    <abc-select-input
      v-model="currentSelected"
      :is-empty="false"
      :visible-popper.sync="visiblePanel"
      clearable
      placeholder="单选"
      data-cy="storybook-test-base1"
    >
      <abc-flex vertical style="padding: 12px">
        <abc-text>这是一个自定义的面板</abc-text>
        <abc-text theme="gray">可以自定义任意样式</abc-text>
        <ul>
          <li v-for="option in options" @click="handleOptionClick(option)">
            {{ option.name }}
          </li>
        </ul>
      </abc-flex>
    </abc-select-input>
    <abc-select-input
      v-model="multipleSelected"
      :is-empty="false"
      :visible-popper.sync="visibleMultiplePanel"
      clearable
      placeholder="多选"
      multiple
    >
      <abc-flex vertical style="padding: 12px">
        <abc-text>这是一个自定义的面板</abc-text>
        <abc-text theme="gray">可以自定义任意样式</abc-text>
        <ul>
          <li v-for="option in options">
            <abc-checkbox
              :control="true"
              :value="multipleSelected.includes(option.name)"
              :checked="multipleSelected.includes(option.name)"
              @click="handleMultipleOptionClick(option)"
            >
              {{ option.name }}
            </abc-checkbox>
          </li>
        </ul>
      </abc-flex>
    </abc-select-input>
    <abc-select-input
      v-model="currentSelected"
      :is-empty="false"
      :visible-popper.sync="visiblePanel2"
      clearable
      placeholder="自定义关闭函数"
      data-cy="storybook-test-base2"
      :close-on-click-outside="handleCloseOnClickOutside"
    >
      <abc-flex :gap="10" style="height: 100px; padding: 12px">
        <div v-for="item in 2" :key="item">
          <abc-popover
            :disabled="currentClickPopoverVal !== item"
            ref="popover"
            trigger="click"
            :z-index="999999"
            theme="white"
          >
            <abc-text slot="reference" @click.native="handleClick(item)"
              >这是一个自定义的面板{{ item }}</abc-text
            >
            <abc-flex vertical>
              <abc-text class="desc-info" theme="gray"
                >可以自定义关闭函数</abc-text
              >
              <ul>
                <li class="li-item" v-for="item in 3" :key="item">
                  第{{ item }}行信息
                </li>
              </ul>
              <abc-button class="confirm-btn" @click="handleConfirmBtn"
                >确定</abc-button
              >
            </abc-flex>
          </abc-popover>
        </div>
      </abc-flex>
    </abc-select-input>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      currentSelected: '',
      visiblePanel: false,
      visiblePanel2: false,
      currentClickPopoverVal: '',
      multipleSelected: [],
      visibleMultiplePanel: false,
      options: [
        {
          name: '选项1',
        },
        {
          name: '选项2',
        },
        {
          name: '选项3',
        },
        {
          name: '选项4',
        },
        {
          name: '选项5',
        },
        {
          name: '选项6',
        },
        {
          name: '选项7',
        },
        {
          name: '选项8',
        },
        {
          name: '选项9',
        },
      ],
    }
  },
  methods: {
    handleOptionClick(item) {
      this.currentSelected = item.name
      this.visiblePanel = false
    },
    handleMultipleOptionClick(item) {
      if (this.multipleSelected.includes(item.name)) {
        this.multipleSelected = this.multipleSelected.filter(
          (option) => option !== item.name,
        )
      } else {
        this.multipleSelected.push(item.name)
      }
    },
    handleCloseOnClickOutside(e) {
      if (!this.visiblePanel2) return
      const flag = e.path.some(
        (item) =>
          item.className?.includes('desc-info') ||
          item.className?.includes('li-item'),
      )
      if (flag) {
        return false
      }
      this.currentClickPopoverVal = ''
      return true
    },
    handleClick(item) {
      this.$refs.popover.forEach((item) => item.doClose())
      if (this.currentClickPopoverVal === item) {
        this.currentClickPopoverVal = ''
        return
      }
      this.currentClickPopoverVal = item
    },
    handleConfirmBtn() {
      this.$refs.popover.forEach((item) => item.doClose())
    },
  },
}
</script>
