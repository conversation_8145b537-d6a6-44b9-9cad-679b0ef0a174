<template>
  <abc-flex vertical :inline="inline">
    <abc-space>
      <label>direction：</label>
      <abc-radio-group v-model="direction">
        <abc-radio label="horizontal"></abc-radio>
        <abc-radio label="vertical"></abc-radio>
      </abc-radio-group>
    </abc-space>
    <abc-space>
      <label>inline：</label> <abc-switch v-model="inline"></abc-switch>
    </abc-space>
    <abc-flex :vertical="direction === 'vertical'" :inline="inline">
      <div style="width: 25%; height: 54px; background: #1677ff"></div>
      <div style="width: 25%; height: 54px; background: #1677ffbf"></div>
      <div style="width: 25%; height: 54px; background: #1677ff"></div>
      <div style="width: 25%; height: 54px; background: #1677ffbf"></div>
    </abc-flex>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      direction: 'horizontal',
      inline: false,
    }
  },
}
</script>
