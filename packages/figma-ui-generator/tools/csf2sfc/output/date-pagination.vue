<template>
  <div>
    <abc-space direction="vertical" align="start">
      <abc-week-pagination
        v-model="pivotDate"
        data-cy="storybook-test-default"
        @change="handleChange"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
      <abc-week-pagination
        v-model="pivotDate"
        @change="handleChange"
        size="small"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
      <abc-week-pagination
        v-model="pivotDate"
        @change="handleChange"
        size="mini"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
    </abc-space>
    <abc-space direction="vertical" align="start">
      <abc-week-pagination
        v-model="pivotDate"
        variant="outline"
        @change="handleChange"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
      <abc-week-pagination
        v-model="pivotDate"
        variant="outline"
        @change="handleChange"
        size="small"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
      <abc-week-pagination
        v-model="pivotDate"
        variant="outline"
        @change="handleChange"
        size="mini"
        emit-change-on-init
      ></abc-week-pagination>
      <div style="margin-top: 8px">范围：{{ dateRange }}</div>
    </abc-space>
  </div>
</template>
<script>
export default {
  data() {
    return {
      pivotDate: new Date(),
      dateRange: [],
    }
  },
  methods: {
    handleChange(range) {
      this.dateRange = range
    },
  },
}
</script>
