<template>
  <abc-flex vertical :gap="16">
    <abc-form is-excel item-no-margin>
      <abc-descriptions :column="2" grid bordered :label-width="200">
        <abc-descriptions-item label="variant" content-padding="0">
          <abc-select v-model="variant" clearable>
            <abc-option label="plain" value="plain"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="theme" content-padding="0">
          <abc-select v-model="theme" clearable>
            <abc-option label="dark" value="dark"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="statisticsNumber" content-padding="0">
          <abc-input v-model="statisticsNumber" type="number"></abc-input>
        </abc-descriptions-item>
        <abc-descriptions-item label="size" content-padding="0">
          <abc-select v-model="size" clearable>
            <abc-option label="mini" value="mini"></abc-option>
            <abc-option label="normal" value="normal"></abc-option>
            <abc-option label="small" value="small"></abc-option>
            <abc-option label="large" value="large"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="disabled">
          <abc-switch v-model="disabled"></abc-switch>
        </abc-descriptions-item>
      </abc-descriptions>
    </abc-form>
    <abc-card padding-size="large">
      <abc-checkbox-button
        v-model="value"
        :variant="variant"
        :theme="theme"
        :size="size"
        :disabled="disabled"
        :statistics-number="+statisticsNumber"
      ></abc-checkbox-button>
    </abc-card>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      value: false,
      variant: 'plain',
      theme: 'dark',
      statisticsNumber: 5,
      disabled: false,
      size: 'normal',
    }
  },
}
</script>
