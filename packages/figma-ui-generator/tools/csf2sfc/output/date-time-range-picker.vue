<template>
  <div>
    <abc-date-time-range-picker
      v-model="selectDate"
      @change="handleChange"
      size="tiny"
    >
    </abc-date-time-range-picker>
    <div style="height: 8px"></div>
    <abc-date-time-range-picker
      v-model="selectDate"
      @change="handleChange"
      size="small"
    >
    </abc-date-time-range-picker>
    <div style="height: 8px"></div>
    <abc-date-time-range-picker v-model="selectDate" @change="handleChange">
    </abc-date-time-range-picker>
    <div style="height: 8px"></div>
    <abc-date-time-range-picker
      v-model="selectDate"
      @change="handleChange"
      size="large"
    >
    </abc-date-time-range-picker>
  </div>
</template>
<script>
export default {
  data() {
    return {
      selectDate: [],
    }
  },
  methods: {
    handleChange(args) {
      console.log('handleChange', args)
    },
  },
}
</script>
