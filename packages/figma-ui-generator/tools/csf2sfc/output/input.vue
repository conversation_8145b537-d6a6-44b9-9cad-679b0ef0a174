<template>
  <div>
    <abc-flex>
      <abc-space>
        <span>disabled状态</span>
        <abc-switch
          v-model="isDisabled"
          @change="handleChangeDisabled"
        ></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>readonly状态</span>
        <abc-switch
          v-model="isReadonly"
          @change="handleChangeReadonly"
        ></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>支持清除</span> <abc-switch v-model="isClearable"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>只有下边框的输入框</span>
        <abc-switch
          v-model="isOnlyBottomBorder"
          @change="handleChangeBorder"
        ></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>不同尺寸</span>
        <abc-radio-group v-model="size">
          <abc-radio label="tiny">tiny</abc-radio>
          <abc-radio label="small">small</abc-radio>
          <abc-radio label="">normal </abc-radio>
          <abc-radio label="medium">medium </abc-radio>
          <abc-radio label="large">large </abc-radio>
        </abc-radio-group>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>append 插槽</span>
        <abc-switch v-model="showAppendInput"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>appendInner 插槽</span>
        <abc-switch v-model="showAppendInner"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>appendLabel 插槽</span>
        <abc-switch v-model="showAppendLabel"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>cover 插槽</span> <abc-switch v-model="showCover"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>prepend 插槽</span>
        <abc-switch v-model="showPrepend"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>loading</span> <abc-switch v-model="loading"></abc-switch>
      </abc-space>
    </abc-flex>
    <abc-flex style="margin-top: 16px">
      <abc-space>
        <span>loading位置</span>
        <abc-radio-group v-model="loadingPosition">
          <abc-radio label="right">right</abc-radio>
          <abc-radio label="left">left</abc-radio>
        </abc-radio-group>
      </abc-space>
    </abc-flex>
    <div style="margin-top: 16px">
      <abc-input
        v-model="currentValue"
        :disabled="isDisabled"
        :readonly="isReadonly"
        :loading="loading"
        :loading-position="loadingPosition"
        :width="140"
        :only-bottom-border="isOnlyBottomBorder"
        :size="size"
        :clearable="isClearable"
        :placeholder="placeholder"
      >
        <span slot="append" v-if="showAppendInput">盒</span>
        <span slot="prepend" v-if="showPrepend">
          <abc-icon icon="s-b-scan-line"></abc-icon>
        </span>
        <span slot="appendInner" v-if="showAppendInner">单位</span>
        <span slot="appendLabel" v-if="showAppendLabel">次数</span>
      </abc-input>
      <abc-input v-if="showCover" :size="size">
        <abc-flex
          slot="cover"
          align="center"
          justify="center"
          style="height: 100%"
        >
          <abc-text
            v-abc-title.ellipsis="'使用 cover 插槽实现自定义文本效果'"
          ></abc-text>
        </abc-flex>
      </abc-input>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      currentValue: '',
      isDisabled: false,
      isReadonly: false,
      isClearable: false,
      isOnlyBottomBorder: false,
      isClear: false,
      showAppendLabel: false,
      showAppendInner: false,
      showCover: false,
      readonlyValue: '这是只读 Input',
      disableValue: '这是禁用 Input',
      placeholder: 'placeholder',
      size: '',
      showAppendInput: false,
      showPrepend: false,
      loading: false,
      loadingPosition: 'left',
    }
  },
  methods: {
    handleChangeBorder(val) {
      if (val) {
        this.placeholder = '只有下边框的 input'
      } else {
        this.placeholder = ''
      }
    },
    handleChangeReadonly(val) {
      if (val) {
        this.placeholder = '只读的 input'
      } else {
        this.placeholder = ''
      }
    },
    handleChangeDisabled(val) {
      if (val) {
        this.placeholder = 'disabled的 input'
      } else {
        this.placeholder = ''
      }
    },
  },
}
</script>
