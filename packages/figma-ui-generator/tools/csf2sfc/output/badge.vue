<template>
  <abc-flex vertical :gap="16">
    <abc-form is-excel item-no-margin>
      <abc-descriptions :column="2" grid bordered :label-width="200">
        <abc-descriptions-item label="value" content-padding="0">
          <abc-input v-model="value" type="number"></abc-input>
        </abc-descriptions-item>
        <abc-descriptions-item label="theme" content-padding="0">
          <abc-select v-model="theme">
            <abc-option label="text" value="text"></abc-option>
            <abc-option label="danger" value="danger"></abc-option>
            <abc-option label="warn" value="warn"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="variant" content-padding="0">
          <abc-select v-model="variant">
            <abc-option label="text" value="text"></abc-option>
            <abc-option label="round" value="round"></abc-option>
            <abc-option label="dot" value="dot"></abc-option>
            <abc-option label="count" value="count"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="maxNumber" content-padding="0">
          <abc-input v-model="maxNumber" type="number"></abc-input>
        </abc-descriptions-item>
      </abc-descriptions>
    </abc-form>
    <abc-card padding-size="small">
      <abc-badge
        :value="value"
        :theme="theme"
        :variant="variant"
        :max-number="+maxNumber"
        data-cy="storybook-test-default"
      >
        确定
      </abc-badge>
    </abc-card>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      value: 1,
      theme: 'danger',
      variant: 'round',
      maxNumber: 99,
    }
  },
}
</script>
