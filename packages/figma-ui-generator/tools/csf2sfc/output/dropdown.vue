<template>
  <div>
    <abc-dropdown
      data-cy="storybook-test-default"
      size="tiny"
      @change="handleChange"
    >
      <div slot="reference">
        <abc-button variant="ghost"> {{ curVal }} </abc-button>
      </div>
      <abc-dropdown-item label="门诊单" value="outpatient"></abc-dropdown-item>
      <abc-dropdown-item label="收费单" value="cashier"></abc-dropdown-item>
      <abc-dropdown-item label="发药单" value="dispense"></abc-dropdown-item>
    </abc-dropdown>
    <abc-dropdown @change="handleChange" size="small">
      <div slot="reference">
        <abc-button variant="ghost"> {{ curVal }} </abc-button>
      </div>
      <abc-dropdown-item label="门诊单" value="outpatient"></abc-dropdown-item>
      <abc-dropdown-item label="收费单" value="cashier"></abc-dropdown-item>
      <abc-dropdown-item label="发药单" value="dispense"></abc-dropdown-item>
    </abc-dropdown>
    <abc-dropdown @change="handleChange">
      <div slot="reference">
        <abc-button variant="ghost"> {{ curVal }} </abc-button>
      </div>
      <abc-dropdown-item label="门诊单" value="outpatient"></abc-dropdown-item>
      <abc-dropdown-item label="收费单" value="cashier"></abc-dropdown-item>
      <abc-dropdown-item label="发药单" value="dispense"></abc-dropdown-item>
    </abc-dropdown>
    <abc-dropdown @change="handleChange" size="large">
      <div slot="reference">
        <abc-button variant="ghost"> {{ curVal }} </abc-button>
      </div>
      <abc-dropdown-item label="门诊单" value="outpatient"></abc-dropdown-item>
      <abc-dropdown-item label="收费单" value="cashier"></abc-dropdown-item>
      <abc-dropdown-item label="发药单" value="dispense"></abc-dropdown-item>
    </abc-dropdown>
  </div>
</template>
<script>
export default {
  data() {
    return {
      curVal: 'outpatient',
    }
  },
  methods: {
    handleChange(val) {
      this.curVal = val
    },
  },
}
</script>
