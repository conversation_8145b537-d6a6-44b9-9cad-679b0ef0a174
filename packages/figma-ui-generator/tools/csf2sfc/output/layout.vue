<template>
  <abc-space direction="vertical">
    <abc-layout style="width: 600px">
      <abc-section>
        <abc-title level="1"
          >一级标题、弹窗标题、区域标题 16px 行高24px
        </abc-title>
        <abc-title>二级标题 14px 行高20px </abc-title>
        <abc-title level="3">三级标题 12px 行高16px </abc-title>
        <abc-title :bold="false" level="3"
          >标题都是加粗的，可以指定 bold=false 不加粗</abc-title
        >
      </abc-section>
    </abc-layout>
    <abc-layout style="width: 600px; margin-top: 40px">
      <abc-section>
        <abc-p>正文 14px 行号20px</abc-p>
        <abc-p gray>解释性文案、特殊情况正文 14px 行号20px</abc-p>
        <abc-p small>特殊情况tips正文</abc-p>
        <abc-p small gray>注释性文案</abc-p>
      </abc-section>
    </abc-layout>
  </abc-space>
</template>
<script>
export default {
  data() {
    return {}
  },
}
</script>
