<template>
  <div>
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="hover"
      theme="yellow"
      data-cy="storybook-test-base1"
    >
      <span slot="reference">
        <abc-button variant="ghost">hover</abc-button>
      </span>
      <div>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
    <br />
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="click"
      theme="yellow"
      control
      data-cy="storybook-test-base2"
    >
      <abc-button variant="ghost" slot="reference"
        >click（reference 可提供active 状态）</abc-button
      >
      <div>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
    <br />
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="focus"
      theme="yellow"
      data-cy="storybook-test-base3"
      control
    >
      <span slot="reference">
        <abc-input placeholder="focus 激活"></abc-input>
      </span>
      <div>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
    <br />
    <h2>normal - manual</h2>
    <br />
    <br />
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="manual"
      theme="yellow"
      v-model="visible"
      data-cy="storybook-test-base-manual"
      control
    >
      <span slot="reference">
        <abc-button
          variant="text"
          data-cy="abc-mr-望闻切诊-脉象选择"
          @click="visible = !visible"
        >
          脉象选择
        </abc-button>
      </span>
      <div>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
    <br />
    <h2>large - click</h2>
    <br />
    <br />
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="click"
      theme="yellow"
      size="large"
      control
    >
      <span slot="reference">
        <abc-button icon="save" variant="text" theme="default" size="small">
        </abc-button>
      </span>
      <div>
        <abc-button icon="save" variant="text" theme="default" size="small">
        </abc-button>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
    <br />
    <h2>huge - click</h2>
    <br />
    <br />
    <abc-popover
      width="348px"
      placement="top-start"
      trigger="click"
      theme="yellow"
      size="huge"
      control
    >
      <span slot="reference">
        <abc-button
          variant="text"
          theme="default"
          size="small"
          icon="three_dot"
        >
        </abc-button>
      </span>
      <div>
        该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店
      </div>
    </abc-popover>
  </div>
</template>
<script>
export default {
  data() {
    return {
      visible: false,
    }
  },
}
</script>
