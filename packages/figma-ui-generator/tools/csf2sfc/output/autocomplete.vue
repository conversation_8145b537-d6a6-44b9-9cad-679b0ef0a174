<template>
  <abc-flex vertical :gap="16">
    <abc-form item-no-margin is-excel>
      <abc-descriptions :column="2" grid :label-width="300">
        <abc-descriptions-item label="尺寸（size）" content-padding="0">
          <abc-select v-model="size">
            <abc-option label="tiny" value="tiny"></abc-option>
            <abc-option label="small" value="small"></abc-option>
            <abc-option label="normal" value="normal"></abc-option>
            <abc-option label="medium" value="medium"></abc-option>
            <abc-option label="large" value="large"></abc-option>
          </abc-select>
        </abc-descriptions-item>
        <abc-descriptions-item label="自适应宽度（adaptiveWidth）">
          <abc-switch v-model="adaptiveWidth"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="聚焦时显示（focusShow）">
          <abc-switch v-model="focusShow"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="可清空（clearable）">
          <abc-switch v-model="clearable"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item
          label="聚焦时占位文本（focusPlaceholder）"
          content-padding="0"
        >
          <abc-input v-model="focusPlaceholder"></abc-input>
        </abc-descriptions-item>
        <abc-descriptions-item label="禁用（disabled）">
          <abc-switch v-model="disabled"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="只读（readonly）">
          <abc-switch v-model="readonly"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item
          label="获取建议时自动聚焦第一个（autoFocusFirst）"
        >
          <abc-switch v-model="autoFocusFirst"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="常驻搜索建议（residentSugguestions）">
          <abc-switch v-model="residentSugguestions"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item label="只显示底部边框（onlyBottomBorder）">
          <abc-switch v-model="onlyBottomBorder"></abc-switch>
        </abc-descriptions-item>
        <abc-descriptions-item
          label="面板最大高度（maxHeight）"
          content-padding="0"
        >
          <abc-input v-model="maxHeight"></abc-input>
        </abc-descriptions-item>
        <abc-descriptions-item label="是否显示空状态（showEmpty）">
          <abc-switch v-model="showEmpty"></abc-switch>
        </abc-descriptions-item>
      </abc-descriptions>
    </abc-form>
    <abc-card padding-size="small">
      <abc-autocomplete
        v-model="value"
        :size="size"
        :adaptive-width="adaptiveWidth"
        :focus-show="focusShow"
        :clearable="clearable"
        :focus-placeholder="focusPlaceholder"
        :disabled="disabled"
        :readonly="readonly"
        :auto-focus-first="autoFocusFirst"
        :resident-sugguestions="residentSugguestions"
        :only-bottom-border="onlyBottomBorder"
        :max-height="maxHeight"
        :show-empty="showEmpty"
        :async-fetch="true"
        :fetch-suggestions="fetchData"
        @enterEvent="handleSelect"
        data-cy="storybook-test-default"
      >
        <template slot="suggestion-header">
          <abc-space>
            <abc-select :width="91"></abc-select>
            <abc-autocomplete :width="91"></abc-autocomplete>
          </abc-space>
          <div
            class="suggestion-title"
            style="display: flex; align-items: center; padding: 0 18px 0 8px"
          >
            <div style="flex: 1">姓名</div>
            <div style="width: 100px">年龄</div>
          </div>
        </template>
        <template slot="suggestions" slot-scope="props">
          <dt
            class="suggestions-item"
            :class="{ selected: props.currentIndex == props.index }"
            @click="handleSelect(props.suggestion)"
          >
            <div style="flex: 1">{{ props.suggestion.name }}</div>
            <div style="width: 100px">{{ props.suggestion.age }}</div>
          </dt>
        </template>
      </abc-autocomplete>
    </abc-card>
  </abc-flex>
</template>
<script>
export default {
  data() {
    return {
      size: 'normal',
      adaptiveWidth: false,
      focusShow: false,
      clearable: false,
      focusPlaceholder: '',
      disabled: false,
      readonly: false,
      autoFocusFirst: false,
      residentSugguestions: false,
      onlyBottomBorder: false,
      maxHeight: 0,
      showEmpty: false,
      options: [
        {
          name: 'bubble',
          age: 10,
        },
        {
          name: '刘喜喜喜喜喜喜喜喜',
          age: 12,
        },
        {
          name: '王富民民民民民',
          age: 13,
          disabled: true,
        },
        {
          name: '王二小',
          age: 14,
        },
        {
          name: 'aaaaaaaaaaaaaaaaaaaaaaaaa',
          age: 15,
        },
        {
          name: 'aaaaaaabbbbbbbbbb',
          age: 16,
        },
        {
          name: 'acccccccccc',
          age: 16,
        },
        {
          name: '张三三三',
          age: 22,
          disabled: true,
        },
        {
          name: '李四五六七八',
          age: 22,
          disabled: true,
        },
      ],
      value: '',
    }
  },
  methods: {
    fetchData(key, callback) {
      return callback(this.options.filter((item) => item.name.includes(key)))
    },
    handleSelect(data) {
      this.value = data.name
    },
  },
}
</script>
