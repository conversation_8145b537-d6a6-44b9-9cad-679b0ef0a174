<template>
  <abc-input-style size="small">
    <abc-space wrap>
      <abc-input
        v-model="currentValue"
        :width="120"
        placeholder="input"
      ></abc-input>
      <abc-select placeholder="select" :width="120"></abc-select>
      <abc-radio-group>
        <abc-radio-button label="1">radio-1</abc-radio-button>
        <abc-radio-button label="2">radio-2</abc-radio-button>
      </abc-radio-group>
      <abc-input-number
        :width="120"
        placeholder="input-number"
      ></abc-input-number>
      <abc-edit-div placeholder="edit-div"></abc-edit-div>
      <abc-date-picker placeholder="date-picker"></abc-date-picker>
      <abc-date-time-picker
        placeholder="date-time-picker"
      ></abc-date-time-picker>
      <abc-time-picker placeholder="time-picker"></abc-time-picker>
      <abc-time-range-picker
        placeholder="time-range-picker"
      ></abc-time-range-picker>
    </abc-space>
  </abc-input-style>
</template>
<script>
export default {
  data() {
    return {
      currentValue: '',
      currentValue1: '',
    }
  },
}
</script>
