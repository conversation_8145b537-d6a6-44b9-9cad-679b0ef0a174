{"name": "AbcDeleteIcon", "description": "简简单单，删除按钮，尺寸支持 large/huge/hugely", "usage": "<template> <abc-flex vertical :style=\"containerStyle\"> <abc-flex> <abc-space> <span>theme：</span> <abc-radio-group v-model=\"theme\"> <abc-radio label=\"default\"></abc-radio> <abc-radio label=\"dark\"></abc-radio> <abc-radio label=\"light\"></abc-radio> </abc-radio-group> </abc-space> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon :theme=\"theme\" size=\"small\" data-cy=\"e2e-delete-icon\" @delete=\"handleDelete\" ></abc-delete-icon> <abc-delete-icon :theme=\"theme\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"large\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"huge\"></abc-delete-icon> <abc-delete-icon :theme=\"theme\" size=\"hugely\"></abc-delete-icon> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"small\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"large\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"huge\" ></abc-delete-icon> <abc-delete-icon variant=\"outline-square\" :theme=\"theme\" size=\"hugely\" ></abc-delete-icon> </abc-flex> <abc-flex align=\"center\"> <abc-delete-icon variant=\"fill\" size=\"small\"></abc-delete-icon> <abc-delete-icon variant=\"fill\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"large\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"huge\"></abc-delete-icon> <abc-delete-icon variant=\"fill\" size=\"hugely\"></abc-delete-icon> </abc-flex> </abc-flex> </template> <script> export default { data() { return { theme: 'default', } }, computed: { containerStyle() { if (this.theme === 'light') { return { background: 'var(--abc-color-B2)', } } }, }, methods: { handleDelete() { alert('delete') }, }, } </script>", "props": [{"name": "transform<PERSON><PERSON>in", "default": "true"}, {"name": "placement", "default": "'right'"}, {"name": "boundariesPadding", "default": "5"}, {"name": "reference"}, {"name": "popper"}, {"name": "offset", "default": "0"}, {"name": "value"}, {"name": "visibleArrow"}, {"name": "transition"}, {"name": "arrowOffset", "default": "35"}, {"name": "appendToBody", "default": "true"}, {"name": "popperOptions", "default": "{\n    // gpuAcceleration: false,\n    eventsEnabled: false,\n    boundariesElement: 'viewport',\n    // modifiersIgnored: ['preventOverflow'],\n}"}, {"name": "variant", "default": "'outline-circle'"}, {"name": "theme", "default": "'default'"}, {"name": "size", "values": ["small", "medium", "large", "huge", "hugely"], "default": "''"}, {"name": "needConfirm", "default": "false"}, {"name": "customPopoverClass", "default": "''"}], "events": ["input", "created", "delete"]}