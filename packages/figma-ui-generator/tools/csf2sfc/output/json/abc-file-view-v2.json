{"name": "AbcFileViewV2", "description": "文件展示组件，用于展示不同类型的文件：图片、doc、pdf、ppt、xls等", "usage": "<template> <div> <div class=\"box\" style=\"display: flex\"> <span>28px</span> <div style=\"width: 44px; height: 44px\" v-for=\"(file, index) in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" size=\"mini\" :data-cy=\"!index ? 'storybook-test-default' : ''\" show-delete-icon @delete=\"onDelete\" > </abc-file-view-v2> </div> </div> <div class=\"box\" style=\"display: flex\"> <span>34px</span> <div style=\"width: 44px; height: 44px\" v-for=\"file in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" show-delete-icon size=\"small\"> </abc-file-view-v2> </div> </div> <div class=\"box\" style=\"display: flex\"> <span>44px</span> <div style=\"width: 44px; height: 44px\" v-for=\"file in fileList\" :key=\"file.url\" > <abc-file-view-v2 :file=\"file\" show-delete-icon> </abc-file-view-v2> </div> </div> </div> </template> <script> export default { data() { return { fileList: [ { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JYx79zRKxSnpBqzZVQnbZBokKNrohlJc_1625724986292.png', fileName: 'qr.png', fileSize: 5089, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/U638oEgsWkWjJ5YdtIZZodxmziMDwtLM_1625725312770.pdf', fileName: '检验报告.pdf', fileSize: 1030, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JipS1TGa5slbx4Trk45zff4sXma3KuE7_1625725875621.xlsx', fileName: '财务报表.xlsx', fileSize: 8011, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/JYKRE6coXwBmspJCbGsgkURywPa1dqzo_1625725878173.pptx', fileName: '演示文档.pptx', fileSize: 25985, }, { url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/examination/2g1Tc5KnwkyNM5b7wL8ZdY66CC36JoXW_1625725880468.docx', fileName: '毕业论文.docx', fileSize: 11234, }, ], } }, methods: { onDelete(e) { console.log('onDelete', e) }, }, } </script>", "props": [{"name": "width", "description": "文件box宽度", "default": "''"}, {"name": "height", "description": "文件box高度", "default": "''"}, {"name": "file", "description": "文件内容支持的文件类型 （图片('jpg', 'png', 'gif', 'bmp', 'jpeg', 'webp'),PPT,PDF,DOC,XLS）<br/>\n 文件内容包含的对象应该有 <br/>\n fileName: string 文件名称<br/>\n fileSize: string 文件大小<br/>\n url: string 文件路径"}, {"name": "thumb", "default": "true"}, {"name": "imgBorder", "description": "图片资源是否展示边框 默认为true", "default": "true"}, {"name": "showFileName", "description": "是否展示名称 默认为false 不展示", "default": "false"}, {"name": "showDeleteIcon", "description": "展示删除按钮", "default": "false"}, {"name": "vertical", "description": "水平or垂直", "default": "true"}, {"name": "size", "description": "支持normal和small、mini", "values": ["normal", "small", "mini"], "default": "'normal'"}, {"name": "imageConfig", "description": "可扩展abc-image的配置", "default": "{}"}], "events": ["delete"]}