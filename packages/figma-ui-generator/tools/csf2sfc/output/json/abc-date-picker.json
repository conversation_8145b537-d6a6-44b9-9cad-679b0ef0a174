{"name": "AbcDatePicker", "description": "日期选择器，用于选择日期、时间、日期范围、时间范围", "usage": "<template> <abc-flex :gap=\"24\" vertical> <abc-space> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" size=\"tiny\" @change-date-pivot=\"handleDatePivotChange\" data-cy=\"e2e-date-picker\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" format=\"YYYY-MM-DD\" size=\"small\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" size=\"large\" > </abc-date-picker> </abc-space> <abc-space> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" size=\"tiny\" :show-icon=\"false\" @change-date-pivot=\"handleDatePivotChange\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :pickerOptions=\"pickerOptions\" format=\"YYYY-MM-DD\" :show-icon=\"false\" size=\"small\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :show-icon=\"false\" :pickerOptions=\"pickerOptions\" shortcutName=\"abc\" > </abc-date-picker> <abc-date-picker v-model=\"selectDate\" :describe-list=\"describeList\" :show-icon=\"false\" :pickerOptions=\"pickerOptions\" size=\"large\" shortcutName=\"abc\" > </abc-date-picker> </abc-space> </abc-flex> </template> <script> export default { data() { return { selectDate: '2023-10-17', describeList: [ { date: '2023-10-13', describe: '预10', }, { date: '2023-10-19', describe: '预20', }, ], pickerOptions: { disabledDate(date) { return date > new Date() || date <= new Date('2023-10-07') }, shortcuts: [ { text: '今天', onClick(cb) { const start = new Date() cb(start) }, }, { text: '昨天', onClick(cb) { const start = new Date() start.setTime(start.getTime() - 24 * 60 * 60 * 1000) cb(start) }, }, ], yearRange: { begin: 1930, end: 2040, }, }, } }, methods: { handleDatePivotChange(args) { console.log('handleDatePivotChange', args) }, }, } </script>", "props": [{"name": "size", "description": "大小，支持 large/small/tiny", "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "type", "description": "可选值: date, daterange, datequick, monthDayRange", "values": ["date", "daterange", "datequick", "monthDayRange"], "default": "'date'"}, {"name": "value", "description": "type 为 date 时，类型为 [Date, String] <br/>\ntype 为 daterange 时，类型为 Array<Date|String>", "default": "null"}, {"name": "placeholder", "default": "'选择日期'"}, {"name": "pickerOptions", "description": "日期选择器特有的选项对象:<br/>\nshortcuts: Array<{text: string, onClick: (cb) => void}> 快捷选择日期方式<br/>\ndisabledDate: (date: Date) => Boolean <br/>\nyearRange: {begin: number, end: number} 年份选择范围，闭区间，默认：1990-2040 <br/>\nonPick: ({minDate: Date, maxDate: Date}) => void 选中日期后会执行的回调，`daterange` 特有<br/>\ntodayText: string 指定今天的文字，默认显示的日期 <br/>\npivotByEndDate: boolean 是否以结束日期为基准显示面板，默认为 false，`daterange` 特有", "default": "{}"}, {"name": "describeList", "description": "日期描述列表 <br/>\n文字和图标二选一，图标优先展示\nArray<{date: Date, describe: string, describeIcon: string, describeClass: string}>", "default": "[]"}, {"name": "clearable", "description": "是否显示清除按钮", "default": "true"}, {"name": "editable", "description": "是否可编辑", "default": "false"}, {"name": "disabled", "description": "是否禁用时间选择器", "default": "false"}, {"name": "valueFormat", "description": "返回值格式，默认：'YYYY-MM-DD'", "default": "'YYYY-MM-DD'"}, {"name": "format", "description": "显示格式，默认：'YYYY-MM-DD'", "default": "'YYYY-MM-DD'"}, {"name": "width", "description": "input 的宽度"}, {"name": "showWeeks", "description": "是否显示星期", "default": "true"}, {"name": "shortcutName", "description": "选中的 shortcut 的 text", "default": "''"}, {"name": "focusShowOptions", "default": "true"}, {"name": "tabindex"}, {"name": "showIcon", "description": "是否展示日期icon & delete icon", "default": "true"}, {"name": "dateIcon", "default": "'n-calendar-line'"}, {"name": "dateIconColor", "default": "''"}, {"name": "onlyBottomBorder", "description": "input只有底部边框", "default": "false"}, {"name": "placement", "description": "弹出框的位置", "default": "'bottom-start'"}, {"name": "allowDayOmission", "description": "是否允许省略日期选择，当为 true 时，日期选择不是必选项，暂时只针对 datequick 生效", "default": "false"}, {"name": "preventDirectionNavigation", "description": "是否阻止日期选择器的方向键导航", "default": "true"}], "slots": [{"name": "default", "description": "自定义 reference"}], "events": ["change-date-pivot", "input", "change", "enter", "tab", "clear", "update:shortcutName"]}