{"name": "AbcSelectInput", "description": "选择输入框组件，用于输入选择内容，支持自定义筛选面板，支持自定义筛选逻辑，支持自定义筛选面板内容，支持自定义筛选面板显示隐藏逻辑。", "usage": "<template> <abc-flex vertical style=\"width: 200px\" gap=\"large\"> <abc-select-input v-model=\"currentSelected\" :is-empty=\"false\" :visible-popper.sync=\"visiblePanel\" clearable placeholder=\"单选\" data-cy=\"storybook-test-base1\" > <abc-flex vertical style=\"padding: 12px\"> <abc-text>这是一个自定义的面板</abc-text> <abc-text theme=\"gray\">可以自定义任意样式</abc-text> <ul> <li v-for=\"option in options\" @click=\"handleOptionClick(option)\"> {{ option.name }} </li> </ul> </abc-flex> </abc-select-input> <abc-select-input v-model=\"multipleSelected\" :is-empty=\"false\" :visible-popper.sync=\"visibleMultiplePanel\" clearable placeholder=\"多选\" multiple > <abc-flex vertical style=\"padding: 12px\"> <abc-text>这是一个自定义的面板</abc-text> <abc-text theme=\"gray\">可以自定义任意样式</abc-text> <ul> <li v-for=\"option in options\"> <abc-checkbox :control=\"true\" :value=\"multipleSelected.includes(option.name)\" :checked=\"multipleSelected.includes(option.name)\" @click=\"handleMultipleOptionClick(option)\" > {{ option.name }} </abc-checkbox> </li> </ul> </abc-flex> </abc-select-input> <abc-select-input v-model=\"currentSelected\" :is-empty=\"false\" :visible-popper.sync=\"visiblePanel2\" clearable placeholder=\"自定义关闭函数\" data-cy=\"storybook-test-base2\" :close-on-click-outside=\"handleCloseOnClickOutside\" > <abc-flex :gap=\"10\" style=\"height: 100px; padding: 12px\"> <div v-for=\"item in 2\" :key=\"item\"> <abc-popover :disabled=\"currentClickPopoverVal !== item\" ref=\"popover\" trigger=\"click\" :z-index=\"999999\" theme=\"white\" > <abc-text slot=\"reference\" @click.native=\"handleClick(item)\" >这是一个自定义的面板{{ item }}</abc-text > <abc-flex vertical> <abc-text class=\"desc-info\" theme=\"gray\" >可以自定义关闭函数</abc-text > <ul> <li class=\"li-item\" v-for=\"item in 3\" :key=\"item\"> 第{{ item }}行信息 </li> </ul> <abc-button class=\"confirm-btn\" @click=\"handleConfirmBtn\" >确定</abc-button > </abc-flex> </abc-popover> </div> </abc-flex> </abc-select-input> </abc-flex> </template> <script> export default { data() { return { currentSelected: '', visiblePanel: false, visiblePanel2: false, currentClickPopoverVal: '', multipleSelected: [], visibleMultiplePanel: false, options: [ { name: '选项1', }, { name: '选项2', }, { name: '选项3', }, { name: '选项4', }, { name: '选项5', }, { name: '选项6', }, { name: '选项7', }, { name: '选项8', }, { name: '选项9', }, ], } }, methods: { handleOptionClick(item) { this.currentSelected = item.name this.visiblePanel = false }, handleMultipleOptionClick(item) { if (this.multipleSelected.includes(item.name)) { this.multipleSelected = this.multipleSelected.filter( (option) => option !== item.name, ) } else { this.multipleSelected.push(item.name) } }, handleCloseOnClickOutside(e) { if (!this.visiblePanel2) return const flag = e.path.some( (item) => item.className?.includes('desc-info') || item.className?.includes('li-item'), ) if (flag) { return false } this.currentClickPopoverVal = '' return true }, handleClick(item) { this.$refs.popover.forEach((item) => item.doClose()) if (this.currentClickPopoverVal === item) { this.currentClickPopoverVal = '' return } this.currentClickPopoverVal = item }, handleConfirmBtn() { this.$refs.popover.forEach((item) => item.doClose()) }, }, } </script>", "props": [{"name": "size", "description": "大小，支持 large/small/tiny", "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "showValue", "description": "自定义展示的文字，有该字段，始终展示该字段"}, {"name": "value"}, {"name": "type", "default": "'text'"}, {"name": "width", "default": "360"}, {"name": "inputStyle", "description": "输入框样式"}, {"name": "noIcon", "description": "不展示下拉箭头", "default": "false"}, {"name": "placeholder", "default": "''"}, {"name": "disabled", "description": "是否禁用"}, {"name": "tabindex"}, {"name": "clearable", "description": "是否可删除", "default": "false"}, {"name": "placement", "default": "'bottom-start'"}, {"name": "focusShowOptions", "description": "聚焦自动展开options", "default": "false"}, {"name": "multiple", "description": "是否支持多选", "default": "() => false"}, {"name": "maxTag", "description": "multiLabelMode 为 tag 时，最后一个tag展示为+n; 如果为 text，展示xx/xx/xx 等几项\nps: 历史原因：当为text时都展示为xx等几项，最初只考虑了tag, 所以该prop取名有歧义", "default": "() => 1000"}, {"name": "onlyBottomBorder", "description": "select只有底部边框", "default": "false"}, {"name": "popperClass", "description": "自定义下拉选项面板样式"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "下拉选项面板宽度"}, {"name": "popperMaxWidth", "description": "下拉选项最大宽度"}, {"name": "popperMaxHeight", "description": "下拉选项最大高度", "default": "308"}, {"name": "visiblePopper", "description": "是否显示下拉面板", "default": "false"}, {"name": "tagMaxWidth", "description": "多选tag限制宽度", "default": "0"}, {"name": "beforeChange", "description": "select 是否选中根据业务代码来决定 （注意： 只支持单选）", "default": "null"}, {"name": "plainPopper", "description": "是否显示朴素的面板，不带有任何样式", "default": "() => false"}, {"name": "editable", "default": "() => false"}, {"name": "closeOnClickOutside", "description": "clickOutside 事件触发是否关闭面板，返回 true 关闭，false 不关闭，默认为 true", "default": "() => true"}], "slots": ["prepend", "reference", "description", "default"], "events": ["update:visiblePopper", "input", "close", "change", "focus", "open", "enter", "up", "down", "left", "right", "blur", "clear"]}