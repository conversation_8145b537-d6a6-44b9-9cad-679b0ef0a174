{"name": "AbcTooltipInfo", "description": "由于 AbcTooltip 组件结合 info_bold 的 icon 使用场景较多，提供了一个 AbcTooltipInfo 组件，便于使用", "usage": "<template> <abc-space direction=\"vertical\"> <div class=\"box\"> <div class=\"top\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"top-start\" content=\"Top Start 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"top\" content=\"Top 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"top-end\" content=\"Top End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"left\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"left-start\" content=\"Left Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"left\" content=\"Left 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"left-end\" content=\"Left End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"right\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"right-start\" content=\"Right Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"right\" content=\"Right 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"right-end\" content=\"Right End 提示文字\"> </abc-tooltip-info> </div> </div> <div class=\"bottom\"> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom-start\" content=\"Bottom Start 提示文字\" > </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom\" content=\"Bottom 提示文字\"> </abc-tooltip-info> </div> <div class=\"box-item\"> <abc-tooltip-info placement=\"bottom-end\" content=\"Bottom End 提示文字\" > </abc-tooltip-info> </div> </div> </div> <abc-tooltip-info> <div>通过 slot 自定义内容</div> <div> 没有<span style=\"color: cadetblue; font-weight: bold\">想不到</span >，只有<span style=\"color: rosybrown; font-weight: bold\">做不到</span> </div> </abc-tooltip-info> </abc-space> </template> <script> export default {} </script>", "props": [{"name": "placement", "description": "弹出位置\ntop(-start, -end),\nright(-start, -end),\nbottom(-start, -end),\nleft(-start, -end)", "default": "'top-start'"}, {"name": "content", "description": "提示内容，仅支持文本，有自定义样式时，使用 slot", "default": "''"}, {"name": "iconSize", "description": "图标大小", "default": "13"}, {"name": "iconColor", "description": "图标颜色", "default": "'var(--abc-color-P10)'"}, {"name": "iconClass", "description": "图标类名", "default": "''"}, {"name": "iconStyle", "description": "图标样式", "default": "''"}, {"name": "max<PERSON><PERSON><PERSON>", "description": "内容最大宽度", "default": "''"}], "slots": ["default"]}