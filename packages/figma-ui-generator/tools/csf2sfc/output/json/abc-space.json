{"name": "AbcSpace", "description": "控制组件之间的间距，也可通过紧凑布局组合，实现表单组件之间的紧凑连接。", "usage": "<template> <abc-space> Space <abc-button>primary</abc-button> <abc-button type=\"blank\">blank</abc-button> <abc-input :width=\"100\"></abc-input> <abc-select :width=\"100\"></abc-select> </abc-space> </template> <script> export default {} </script>", "props": [{"name": "direction", "description": "间距方向", "default": "'horizontal'"}, {"name": "align", "description": "对齐方式", "default": "'center'"}, {"name": "size", "description": "间距大小 Array[horizontalSize, verticalSize] eg: [8,16] <br/>\nString: small | middle | large <br/>\nNumber: 8 | 16 | 24", "default": "'small'"}, {"name": "wrap", "description": "是否自动换行，仅在 horizontal 时有效", "default": "false"}, {"name": "customStyle", "description": "自定义样式", "default": "() => {}"}, {"name": "isCompact", "description": "是否是紧凑布局组合", "default": "false"}, {"name": "compactBlock", "description": "将宽度调整为父元素宽度的选项（仅用于紧凑布局）", "default": "false"}, {"name": "borderStyle", "description": "表单元素间border样式（仅用于紧凑布局）<br/>\nBoolean = true / false, false 则不显示 border <br/>\nString = 'solid | dashed | dotted'", "default": "true"}, {"name": "split", "description": "设置拆分 (仅用于间距方向为‘horizontal’)", "default": "false"}], "slots": ["default", "customSplit"]}