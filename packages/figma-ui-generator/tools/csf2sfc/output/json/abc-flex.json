{"name": "AbcFlex", "description": "Flex 布局组件，用于展示 Flex 布局内容", "usage": "<template> <abc-flex vertical :inline=\"inline\"> <abc-space> <label>direction：</label> <abc-radio-group v-model=\"direction\"> <abc-radio label=\"horizontal\"></abc-radio> <abc-radio label=\"vertical\"></abc-radio> </abc-radio-group> </abc-space> <abc-space> <label>inline：</label> <abc-switch v-model=\"inline\"></abc-switch> </abc-space> <abc-flex :vertical=\"direction === 'vertical'\" :inline=\"inline\"> <div style=\"width: 25%; height: 54px; background: #1677ff\"></div> <div style=\"width: 25%; height: 54px; background: #1677ffbf\"></div> <div style=\"width: 25%; height: 54px; background: #1677ff\"></div> <div style=\"width: 25%; height: 54px; background: #1677ffbf\"></div> </abc-flex> </abc-flex> </template> <script> export default { data() { return { direction: 'horizontal', inline: false, } }, } </script>", "props": [{"name": "vertical", "description": "flex 主轴的方向是否垂直，默认为水平方向", "default": "false"}, {"name": "wrap", "description": "参考 flex-wrap，默认 nowrap", "default": "'nowrap'"}, {"name": "gap", "description": "设置网格之间的间隙，small、middle、large", "default": "0"}, {"name": "justify", "description": "参考 justify-content，默认 normal", "default": "'normal'"}, {"name": "align", "description": "参考 align-items，默认 normal", "default": "'normal'"}, {"name": "flex", "description": "flex CSS 简写属性，默认 normal", "default": "'normal'"}, {"name": "tag", "description": "自定义元素标签，默认 div", "default": "'div'"}, {"name": "inline", "description": "是否使用 inline-flex", "default": "false"}], "slots": ["default"]}