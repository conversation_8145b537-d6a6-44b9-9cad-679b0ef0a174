{"name": "AbcSuggestionsPanel", "description": "", "usage": "<template> <div> <abc-suggestions-panel> <template slot=\"header\"> <abc-suggestions-header> <abc-suggestions-cell width=\"200\">险种类型</abc-suggestions-cell> <abc-suggestions-cell width=\"100\">状态</abc-suggestions-cell> <abc-suggestions-cell align=\"right\" width=\"100\" >余额</abc-suggestions-cell > </abc-suggestions-header> </template> <abc-suggestions-item v-for=\"option in options\" @click=\"handleOptionClick(option)\" :selected=\"currentSelected.name === option.name\" > <abc-suggestions-cell width=\"200\">{{ option.name }}</abc-suggestions-cell> <abc-suggestions-cell width=\"100\">{{ option.status }}</abc-suggestions-cell> <abc-suggestions-cell align=\"right\" width=\"100\">{{ option.balance }}</abc-suggestions-cell> </abc-suggestions-item> </abc-suggestions-panel> </div> </template> <script> export default { data() { return { currentSelected: { name: '', }, options: [ { name: '职工基本医疗保险', status: '正常参保', balance: 1200, }, { name: '生育保险', status: '暂停参保', balance: 400, }, { name: '城乡居民基本医疗保险', status: '未参保', balance: 0, }, { name: '大额医疗费用补助', status: '终止参保', balance: 0, }, ], } }, methods: { handleOptionClick(item) { this.currentSelected = { ...item, } }, }, } </script>", "props": [{"name": "width"}, {"name": "customClass"}, {"name": "maxHeight"}, {"name": "gap", "default": "8"}, {"name": "searchable", "default": "false"}, {"name": "searchConfig", "default": "{\n    placeholder: '',\n    clearable: true\n}"}, {"name": "multipleSelect", "default": "false"}, {"name": "loading", "default": "false"}], "slots": ["header", "default", "footer"], "events": ["search", "search-clear"]}