{"name": "AbcPopover", "description": "弹出层组件是其他弹窗类组件如气泡确认框实现的基础，当这些组件提供的能力不能满足定制需求时，可以在弹出层组件基础上封装。仅展示文本信息优先使用 AbcTooltip/AbcTooltipInfo", "usage": "<template> <div> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"hover\" theme=\"yellow\" data-cy=\"storybook-test-base1\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">hover</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" data-cy=\"storybook-test-base2\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">click</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"focus\" theme=\"yellow\" data-cy=\"storybook-test-base3\" > <span slot=\"reference\"> <abc-input placeholder=\"focus 激活\"></abc-input> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"manual\" theme=\"yellow\" v-model=\"visible\" data-cy=\"storybook-test-base-manual\" > <span slot=\"reference\"> <abc-button @click=\"visible = !visible\" variant=\"ghost\" >manual</abc-button > </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"large\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">large</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> <br /> <abc-popover width=\"348px\" placement=\"top-start\" trigger=\"click\" theme=\"yellow\" size=\"huge\" > <span slot=\"reference\"> <abc-button variant=\"ghost\">huge</abc-button> </span> <div> 该成员已接受你的邀请，需对他的信息进行确认并完善后，他才能正式加入门店 </div> </abc-popover> </div> </template> <script> export default { data() { return { visible: false, } }, } </script>", "props": [{"name": "transform<PERSON><PERSON>in", "default": "true"}, {"name": "placement", "default": "'bottom-start'"}, {"name": "boundariesPadding", "default": "5"}, {"name": "reference"}, {"name": "popper"}, {"name": "offset", "default": "0"}, {"name": "value"}, {"name": "visibleArrow", "description": "箭头是否可见", "default": "true"}, {"name": "transition"}, {"name": "arrowOffset", "description": "箭头偏移量", "default": "0"}, {"name": "appendToBody", "default": "true"}, {"name": "popperOptions", "default": "{\n    // gpuAcceleration: false,\n    eventsEnabled: false,\n    boundariesElement: 'viewport',\n}"}, {"name": "theme", "description": "提供 custom、yellow、white 三套主题\n默认为 custom，即需要自己定义样式", "values": ["custom", "yellow", "white"], "default": "'custom'"}, {"name": "trigger", "description": "触发方式，支持 click、focus、hover、manual\n当为 manual 时，通过 v-model 控制显示", "values": ["click", "focus", "hover", "manual"], "default": "'click'"}, {"name": "openDelay", "description": "打开延迟时间，单位 ms", "default": "0"}, {"name": "close<PERSON><PERSON><PERSON>", "description": "关闭延迟时间，单位 ms", "default": "200"}, {"name": "disabled", "description": "禁用弹层", "default": "false"}, {"name": "content", "description": "显示的内容，简单文本可用此属性传递，优先展示 slot 内容"}, {"name": "popperClass", "description": "自定义 popper 的 class", "default": "''"}, {"name": "popperStyle", "description": "自定义 popper 的 style"}, {"name": "width", "description": "自定义 popper 的宽度"}, {"name": "zIndex", "description": "控制层级，默认 1992", "default": "1992"}, {"name": "tabindex", "description": "指定 reference 的原生 tabindex，用于 tab 控制", "default": "-1"}, {"name": "disabledClose", "description": "禁止关闭"}, {"name": "residentPop<PERSON>", "description": "是否常驻 popper，为 true 时，在 mounted 中会显示", "default": "false"}, {"name": "size", "description": "popper 的大小，支持 small、large、huge", "values": ["small", "large", "huge"], "default": "'small'"}, {"name": "showOnOverflow", "description": "是否在内容溢出时显示", "default": "false"}], "slots": ["default", "reference"], "events": ["input", "created", "show", "hide"]}