{"name": "AbcAutocomplete", "description": "搜索建议组件，用于输入框的搜索建议", "usage": "<template> <div> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>不同尺寸</span> <abc-radio-group v-model=\"size\"> <abc-radio label=\"tiny\">tiny</abc-radio> <abc-radio label=\"small\">small</abc-radio> <abc-radio label=\"\">normal </abc-radio> <abc-radio label=\"medium\">medium </abc-radio> <abc-radio label=\"large\">large </abc-radio> </abc-radio-group> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>append 插槽</span> <abc-switch v-model=\"showAppend\"></abc-switch> </abc-space> </abc-flex> <abc-flex style=\"margin-top: 16px\"> <abc-space> <span>appendInner 插槽</span> <abc-switch v-model=\"showAppendInner\"></abc-switch> </abc-space> </abc-flex> <abc-autocomplete style=\"margin-top: 24px\" v-model.trim=\"value\" inner-width=\"200px\" :width=\"320\" :delay-time=\"0\" :async-fetch=\"true\" :fetch-suggestions=\"fetchData\" :max-length=\"20\" focus-show :auto-focus-first=\"false\" @enterEvent=\"handleSelect\" data-cy=\"storybook-test-default\" :size=\"size\" placeholder=\"small\" > <abc-icon slot=\"prepend\" icon=\"renminbi\"></abc-icon> <template slot=\"suggestion-header\"> <div class=\"suggestion-title\"> <span class=\"name\"> 姓名 </span> <span class=\"age\"> 年龄 </span> </div> </template> <template slot=\"suggestions\" slot-scope=\"props\"> <dt class=\"suggestions-item\" :class=\"{ selected: props.suggestion.name == value }\" @click=\"handleSelect(props.suggestion)\" > <div>{{ props.suggestion.name }}</div> <div>{{ props.suggestion.age }}</div> </dt> </template> <abc-icon slot=\"append\" v-if=\"showAppend\" icon=\"renminbi\"></abc-icon> <div slot=\"appendInner\" v-if=\"showAppendInner\">单位</div> </abc-autocomplete> </div> </template> <script> export default { data() { return { options: [ { name: 'bubble', age: 10, }, { name: '刘喜', age: 12, }, { name: '王富民', age: 13, disabled: true, }, { name: '王二小', age: 14, }, { name: 'a', age: 15, }, { name: 'b', age: 16, }, { name: 'c', age: 16, }, { name: 'd', age: 22, disabled: true, }, ], value: '', size: '', showAppend: false, showAppendInner: false, } }, methods: { fetchData(key, callback) { console.log('fetchData', key) return callback(this.options.filter((item) => item.name.includes(key))) }, handleSelect(data) { console.log('handleEnterEvent', data) this.value = data.name }, }, } </script>", "props": [{"name": "size", "description": "大小，支持 large/small/tiny", "default": "''"}, {"name": "adaptiveWidth", "description": "是否自适应宽度。默认为 false，设置为 true 时，宽度为 100%；若是下拉组件，则下拉面板的最小宽度为输入框宽度", "default": "undefined"}, {"name": "value"}, {"name": "fetchSuggestions", "description": "获取建议项", "default": "function() {\n    return () => {\n    };\n}"}, {"name": "filterSuggestions", "description": "筛选建议项", "default": "function() {\n    return () => {\n    };\n}"}, {"name": "asyncFetch", "description": "是否开启异步", "default": "false"}, {"name": "focusShow", "description": "聚焦时是否展示 建议项", "default": "false"}, {"name": "focusWhenAppendClick", "description": "点击 prepend 的icon需要focus input", "default": "false"}, {"name": "type", "default": "'text'"}, {"name": "width"}, {"name": "max<PERSON><PERSON><PERSON>", "default": "30"}, {"name": "innerWidth", "description": "建议项弹窗宽度"}, {"name": "customClass", "description": "建议项弹窗自定义Class"}, {"name": "clearable", "description": "是否支持清除搜索"}, {"name": "placeholder"}, {"name": "focusPlaceholder", "default": "''"}, {"name": "hidePlaceholder<PERSON>henFocus", "description": "focus 时隐藏 placeholder", "default": "false"}, {"name": "placement"}, {"name": "disabled"}, {"name": "readonly"}, {"name": "index"}, {"name": "tabindex"}, {"name": "delayTime", "default": "200"}, {"name": "keyboardEvent", "description": "阻止默认事件，触发方法，特殊按键直接响应"}, {"name": "autoFocusFirst", "description": "默认选中第一个suggestion", "default": "true"}, {"name": "popperOptions", "default": "{}"}, {"name": "residentSugguestions", "description": "常驻搜索建议", "default": "false"}, {"name": "onlyBottomBorder", "description": "input只有底部边框", "default": "false"}, {"name": "closeOnClickOutside", "description": "clickOutside 事件触发是否关闭建议项，返回 true 关闭，false 不关闭，默认为 true", "default": "() => true"}, {"name": "maxHeight", "description": "弹出面板滚动区域的最大高度，不包含 header 和 footer 的高度，作用在 class: abc-scrollbar-wrapper", "default": "''"}, {"name": "distinguishHalfAngleLength", "description": "是否区分全交半角的长度，与 maxLength 搭配使用，半角占 1 位，全角占 2 位", "default": "false"}, {"name": "inputCustomStyle", "description": "自定义input样式"}, {"name": "onlySelect", "description": "支持选中取值", "default": "false"}, {"name": "showEmpty", "description": "固定面板，建议项为空时，展示空状态", "default": "false"}], "slots": ["prepend", "suggestion-header", "suggestion-item", "suggestions", "suggestion-footer", "suggestion-fixed-footer", "empty", "append", "appendInner"], "events": ["input", "change", "blur", "blurEvent", "focus", "enterEvent", "enter", "select", "clear", "right", "left"]}