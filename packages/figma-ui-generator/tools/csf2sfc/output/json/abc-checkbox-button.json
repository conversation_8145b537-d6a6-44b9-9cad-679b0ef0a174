{"name": "AbcCheckboxButton", "description": "多选框的按钮形态，用于选择多个选项", "usage": "<template> <abc-descriptions :column=\"1\"> <abc-descriptions-item label=\"variant\"> <abc-space> <abc-checkbox-button v-model=\"variantValue\" type=\"number\"> 默认 </abc-checkbox-button> <abc-checkbox-button data-cy=\"storybook-test-default\" v-model=\"variantValue\" type=\"number\" statistics-number=\"2\" > 勿动该例子,用于测试 </abc-checkbox-button> <abc-checkbox-button v-model=\"variantValue\" type=\"number\" statistics-number=\"0\" > 默认 </abc-checkbox-button> <abc-checkbox-button v-model=\"variantValue2\" type=\"number\" variant=\"plain\" > 加工 </abc-checkbox-button> </abc-space> </abc-descriptions-item> <abc-descriptions-item label=\"theme\"> <abc-space> <abc-checkbox-button v-model=\"themeValue\" type=\"number\"> 默认 </abc-checkbox-button> <abc-checkbox-button v-model=\"themeValue\" type=\"number\" statistics-number=\"2\" > 默认 </abc-checkbox-button> <abc-checkbox-button theme=\"dark\" v-model=\"themeValue2\" type=\"number\" statistics-number=\"2\" > dark </abc-checkbox-button> </abc-space> </abc-descriptions-item> </abc-descriptions> </template> <script> export default { data() { return { variantValue: 0, variantValue2: 0, themeValue: 0, themeValue2: 0, } }, } </script>", "props": [{"name": "value", "default": "false"}, {"name": "checked"}, {"name": "disabled", "default": "false"}, {"name": "control", "description": "是否受控，受控时，只能通过父组件的value属性控制，自身点击不会更改状态", "default": "false"}, {"name": "type", "description": "绑定值的类型 boolean 或者 number"}, {"name": "customerStyle", "default": "{}"}, {"name": "label"}, {"name": "variant", "description": "变体，支持 plain，选中图标无边框", "values": ["plain"], "default": "''"}, {"name": "theme", "description": "主题，支持默认和 dark", "values": ["dark"], "default": "''"}, {"name": "size", "description": "支持normal和small两种大小", "values": ["mini", "small", "normal", "large"], "default": "'normal'"}, {"name": "statisticsNumber", "description": "统计数字", "default": "''"}], "slots": ["default"], "events": ["click", "input", "change"]}