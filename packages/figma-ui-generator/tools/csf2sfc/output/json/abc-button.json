{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "按钮用于开启一个闭环的操作任务，如“收费”订单、“购买”商品等", "usage": "<template> <abc-flex vertical> <abc-descriptions :column=\"1\" :bordered=\"false\"> <abc-descriptions-item label=\"variant\"> <abc-radio-group v-model=\"variant\"> <abc-radio label=\"fill\"></abc-radio> <abc-radio label=\"outline\"></abc-radio> <abc-radio label=\"ghost\"></abc-radio> <abc-radio label=\"text\"></abc-radio> </abc-radio-group> </abc-descriptions-item> <abc-descriptions-item label=\"theme\"> <abc-radio-group v-model=\"theme\"> <abc-radio label=\"default\"></abc-radio> <abc-radio label=\"primary\"></abc-radio> <abc-radio label=\"success\"></abc-radio> <abc-radio label=\"warning\"></abc-radio> <abc-radio label=\"danger\"></abc-radio> </abc-radio-group> </abc-descriptions-item> <abc-descriptions-item label=\"disabled\"> <abc-switch v-model=\"disabled\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"loading\"> <abc-switch v-model=\"loading\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"图标\"> <abc-switch v-model=\"icon\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"图标位置\"> <abc-radio-group v-model=\"iconPosition\"> <abc-radio label=\"left\"></abc-radio> <abc-radio label=\"right\"></abc-radio> </abc-radio-group> </abc-descriptions-item> <abc-descriptions-item label=\"size\"> <abc-radio-group v-model=\"size\"> <abc-radio label=\"small\"></abc-radio> <abc-radio label=\"normal\"></abc-radio> <abc-radio label=\"large\"></abc-radio> </abc-radio-group> </abc-descriptions-item> <abc-descriptions-item label=\"shape\"> <abc-radio-group v-model=\"shape\"> <abc-radio label=\"square\"></abc-radio> <abc-radio label=\"round\"></abc-radio> </abc-radio-group> </abc-descriptions-item> <abc-descriptions-item label=\"noBorderRadius\"> <abc-switch v-model=\"noBorderRadius\"></abc-switch> </abc-descriptions-item> <abc-descriptions-item label=\"count\"> <abc-input-number size=\"small\" v-model=\"count\" :width=\"100\" ></abc-input-number> </abc-descriptions-item> <abc-descriptions-item label=\"width\"> <abc-input-number size=\"small\" v-model=\"width\" :width=\"100\" ></abc-input-number> </abc-descriptions-item> </abc-descriptions> <div style=\"padding-left: 12px\"> <abc-button :icon-position=\"iconPosition\" :icon=\"iconName\" :size=\"size\" :shape=\"shape\" :disabled=\"disabled\" :loading=\"loading\" :variant=\"variant\" :theme=\"theme\" :no-border-radius=\"noBorderRadius\" :count=\"count\" :width=\"width\" > {{ variant }} - {{ theme }} </abc-button> </div> </abc-flex> </template> <script> export default { data() { return { icon: false, size: 'normal', shape: 'square', disabled: false, loading: false, iconPosition: 'left', variant: 'fill', theme: 'default', noBorderRadius: false, count: 5, width: 120, } }, computed: { iconName() { return this.icon ? 'n-setting-line' : '' }, }, } </script>", "props": [{"name": "loading"}, {"name": "disabled"}, {"name": "needLoadedClick"}, {"name": "variant", "description": "按钮变体", "values": ["fill", "outline", "ghost", "text"], "default": "'fill'"}, {"name": "theme", "description": "按钮主题", "values": ["default", "primary", "warning", "danger", "success"], "default": "'primary'"}, {"name": "shape", "description": "形状", "default": "'square'"}, {"name": "type", "values": ["ghost", "blank", "danger", "text", "success", "important", "importantp", "successp", "warning", "payment", "paymentp", "primary"], "default": "'primary'"}, {"name": "size", "values": ["small", "large", "normal"], "default": "'normal'"}, {"name": "icon", "description": "指定图标", "default": "''"}, {"name": "iconColor", "description": "图标颜色，默认跟随主题色", "default": "''"}, {"name": "iconPosition", "description": "图标位置，默认左侧", "values": ["left", "right"], "default": "'left'"}, {"name": "noBorderRadius", "description": "禁用边框圆角\n传 boolean 时，true 会禁用全部圆角\n传 string 时，left、right 会禁用左、右圆角", "default": "false"}, {"name": "count", "description": "数字，当指定数字时，会显示·数字", "default": "''"}, {"name": "width", "description": "自定义宽度", "default": "''"}, {"name": "min<PERSON><PERSON><PERSON>", "description": "自定义最小宽度", "default": "''"}], "slots": [{"name": "default", "description": "text slot"}], "events": ["click"]}