{"name": "AbcPreview", "description": "图片预览组件", "usage": "<template> <div style=\"display: flex\" data-cy=\"storybook-test-default\"> <abc-file-view v-for=\"(file, index) in imgList\" :key=\"file.url\" :file=\"file\" @click.native=\"handleFileClick(file, index)\" > </abc-file-view> <abc-preview v-if=\"showPreview\" v-model=\"showPreview\" :index=\"previewIndex\" enable-compress :lists=\"imgList\" ></abc-preview> </div> </template> <script> export default { data() { return { showPreview: false, previewIndex: 0, imgList: [ { uuid: '70d77dcf61744af8a99735b548c1b3b1', // eslint-disable-next-line max-len url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/medical-record/mXZ4i3UWmXldRnDKXalcu8qvrSokLRT9_1625729124120.png', fileName: 'maple_2.png', fileSize: 438576, sort: 0, }, { uuid: '759089f7758841bbbf314d05f53af5d4', // eslint-disable-next-line max-len url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/medical-record/A1KjVldkKfUoG1fzXZYqAC7B1MaYAiik_1625729124131.png', fileName: 'maple.png', fileSize: 1940720, sort: 1, }, { uuid: 'e79589834a494aa4bb4097dbbb7645e6', // eslint-disable-next-line max-len url: 'https://cd-cis-static-assets-dev.oss-cn-chengdu.aliyuncs.com/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/clinic-usage/fff730ccc5ee45d783d82a85b8a0e52d/medical-record/tkO8EU7suyW5LJwDRoEb5VVxzg5lgaKl_1625729124930.png', fileName: 'sunset.png', fileSize: 205340, sort: 2, }, { uuid: 'e79589834a4aa4bb4097dbbb7645e6', // eslint-disable-next-line max-len url: '123', fileName: 'sunset.png', fileSize: 205340, sort: 2, }, ], } }, methods: { handleFileClick(file, index) { this.showPreview = true this.previewIndex = index }, }, } </script>", "props": [{"name": "value"}, {"name": "lists", "description": "图片列表 Array<{url: string}>", "default": "[]"}, {"name": "index", "description": "当前展示的索引", "default": "0"}, {"name": "appendToBody", "description": "是否插在 body 下", "default": "true"}, {"name": "enableCompress", "description": "是否启用压缩", "default": "true"}], "events": ["input"]}