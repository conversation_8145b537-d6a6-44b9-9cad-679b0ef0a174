{"name": "AbcBadge", "description": "徽标，用于展示未读消息数量等", "usage": "<template> <div> <abc-space> <abc-badge :value=\"value\" theme=\"danger\"></abc-badge> <abc-badge :value=\"value\" theme=\"danger\" variant=\"dot\"></abc-badge> <abc-badge :value=\"value\" theme=\"danger\" variant=\"round\"></abc-badge> <abc-badge data-cy=\"storybook-test-default\" :value=\"value\" theme=\"danger\" variant=\"count\" ></abc-badge> </abc-space> </div> </template> <script> export default { data() { return { value: 1, } }, } </script>", "props": [{"name": "value", "default": "0"}, {"name": "variant", "description": "类型 text 文字徽标 round 圆形徽标 dot 点状徽标 count文字汇总徽标\ntext 不设置theme文本颜色默认继承父元素颜色", "default": "'text'"}, {"name": "theme", "description": "主题 danger 危险; warn 警告; text 文本;\n属性为round和dot时 仅支持danger warn 属性 不传默认为danger", "default": "'text'"}, {"name": "maxNumber", "description": "是否需要限制最大位数 默认为99 超出设置范围展示xx+", "default": "99"}]}