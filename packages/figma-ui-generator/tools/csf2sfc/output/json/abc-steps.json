{"name": "AbcSteps", "description": "步骤条组件，用于引导用户按照流程完成任务，提供了水平、垂直、点状等多种展示方式，与AbcStep搭配使用", "usage": "<template> <div> <AbcSteps :active=\"active\" @step-click=\"handleClick\" data-cy=\"storybook-test-default\" > <AbcStep :index=\"0\" icon=\"n-vip-1-line\" data-cy=\"abc-step-completed\" >下载并填写项目模板</AbcStep > <AbcStep :index=\"1\" icon=\"n-link-line\" data-cy=\"abc-step-in-progress\" >上传已填项目模板表</AbcStep > <AbcStep :index=\"2\" icon=\"n-card-line\" data-cy=\"abc-step-unfinished\" >导入完成</AbcStep > </AbcSteps> </div> </template> <script> export default { data() { return { active: 1, } }, methods: { handleClick(index) { alert(index) }, }, } </script>", "props": [{"name": "direction", "description": "steps 方向 horizontal / vertical", "default": "() => 'horizontal'"}, {"name": "active", "description": "进行到第几步", "default": "() => 0"}, {"name": "activeColor", "description": "完成了的颜色", "default": "() => 'var(--abc-color-theme2)'"}, {"name": "separator", "description": "分割线 line / dashed， 默认垂直方向下为虚线, 水平方向下为实线"}, {"name": "theme", "description": "主题 default / dot  默认水平为default， 垂直为dot"}, {"name": "titleSize", "description": "title的font-size 默认水平方向下为 14px， 垂直方向下为 12px"}], "slots": ["default"]}