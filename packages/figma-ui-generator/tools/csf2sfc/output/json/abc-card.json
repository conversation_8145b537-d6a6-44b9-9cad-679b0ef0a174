{"name": "AbcCard", "description": "卡片，用于展示一些信息", "usage": "<template> <AbcCard> <abc-layout> <abc-layout-content> <abc-section> <abc-title level=\"1\">优惠券功能介绍</abc-title> <abc-title :bold=\"false\">为什么使用优惠券？</abc-title> <abc-p gray>发放优惠券，可提升顾客复购率</abc-p> <abc-p gray>设置免费领取的优惠券，可吸引新顾客</abc-p> <abc-p gray>设置优惠券使用门槛，可提升客单价</abc-p> </abc-section> <abc-section> <abc-title>优惠券如何发放？</abc-title> <abc-p >设置为可免费领取的优惠券，顾客可在 [微诊所] - [我的优惠券] 中自行领取</abc-p > <abc-p >可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</abc-p > <abc-p >可结合满赠活动，消费满足一定金额赠送优惠券，提升顾客复购率</abc-p > </abc-section> <abc-section> <abc-title level=\"1\">为什么使用优惠券？</abc-title> <abc-p>发放优惠券，可提升顾客复购率</abc-p> </abc-section> <abc-section> <abc-title level=\"3\">为什么使用优惠券？</abc-title> <abc-p gray small>发放优惠券，可提升顾客复购率</abc-p> </abc-section> </abc-layout-content> </abc-layout> </AbcCard> </template> <script> export default { data() { return {} }, } </script>", "props": [{"name": "radiusSize", "description": "卡片圆角，mini、small、large", "values": ["mini", "small", "large"], "default": "'large'"}, {"name": "paddingSize", "description": "内置卡片padding，\n'' 0;\n'mini' 8px;\n'small': 12;\n'medium': 16;\n'large': 24;\n'huge': 32;\n'hugely': 40;", "values": ["mini", "small", "medium", "large", "huge", "hugely"], "default": "''"}, {"name": "background", "description": "背景色默认白色，可以指定\ngray 灰底", "values": ["gray"], "default": "''"}, {"name": "border", "description": "card 是否有边框", "default": "true"}, {"name": "shadow", "description": "card 是否有阴影", "default": "false"}], "slots": ["title", "default"]}