{"name": "AbcMenu", "description": "菜单组件，用于导航菜单的展示，可与AbcMenuItem和AbcSubMenu，AbcMenuGroup组件配合使用", "usage": "<template>\n  <div style=\"width: 100%; padding: 20px 0\">\n    <h5>常规用法</h5>\n    <div style=\"width: 260px; height: 400px; padding: 10px 10px\">\n      <abc-menu v-model=\"value\" @click=\"selectItem\">\n        <abc-menu-item icon=\"n-settings-line\" index=\"1\">菜单1</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"2\">菜单2</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"3\">菜单3</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"4\" :count=\"13\"\n          >菜单4</abc-menu-item\n        >\n        <abc-menu-item icon=\"n-settings-line\" index=\"5\">菜单5</abc-menu-item>\n        <abc-menu-item icon=\"n-settings-line\" index=\"6\">菜单6</abc-menu-item>\n        <abc-sub-menu :index=\"7\" icon=\"n-settings-line\" value=\"菜单7\">\n          <abc-menu-item index=\"7-1\">菜单7-1</abc-menu-item>\n          <abc-menu-item index=\"7-2\">菜单7-2</abc-menu-item>\n        </abc-sub-menu>\n        <abc-menu-item icon=\"n-settings-line\" index=\"8\">菜单8</abc-menu-item>\n      </abc-menu>\n    </div>\n    <h5>分组用法</h5>\n    <div style=\"width: 260px;height: 400px;padding: 10px 10px;\">\n      <abc-menu badge-variant=\"dot\" v-model=\"value\" @click=\"selectItem\">\n        <abc-menu-group>\n          <abc-menu-item icon=\"n-settings-line\" :count=\"10\" index=\"1\">菜单1</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"2\">菜单2</abc-menu-item>\n        </abc-menu-group>\n        <abc-menu-group>\n          <abc-menu-item icon=\"n-settings-line\" index=\"3\">菜单3</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"4\">菜单4</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"5\">菜单5</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"6\">菜单6</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"7\">菜单7</abc-menu-item>\n        </abc-menu-group>\n        <abc-menu-group>\n          <abc-menu-item icon=\"n-settings-line\" index=\"8\">菜单8</abc-menu-item>\n          <abc-menu-item icon=\"n-settings-line\" index=\"9\">菜单9</abc-menu-item>\n        </abc-menu-group>\n      </abc-menu>\n    </div>\n    <h5>点击收起用法</h5>\n    <div style=\"width: 260px;height: 400px;padding: 10px 0 10px 10px;overflow-x: hidden;overflow-y: scroll;\">\n      <abc-menu badge-variant=\"dot\" :clickNeedCollapseOtherMenu=\"false\" v-model=\"value\" @click=\"selectItem\">\n        <abc-sub-menu index=\"1\" icon=\"n-settings-line\" value=\"菜单1\">\n          <abc-menu-item :count=\"12\" index=\"1-1\">菜单1-1</abc-menu-item>\n          <abc-menu-item index=\"1-2\">菜单1-2</abc-menu-item>\n        </abc-sub-menu>\n        <abc-sub-menu index=\"2\" icon=\"n-settings-line\" :clickNeedCollapseOtherMenu=\"true\" value=\"菜单2\">\n          <abc-menu-item index=\"2-1\">菜单2-1</abc-menu-item>\n          <abc-menu-item index=\"2-2\">菜单2-2</abc-menu-item>\n        </abc-sub-menu>\n        <abc-sub-menu index=\"3\" icon=\"n-settings-line\" :clickNeedCollapseOtherMenu=\"true\" value=\"菜单3\">\n          <abc-menu-item index=\"3-1\">菜单3-1</abc-menu-item>\n          <abc-menu-item index=\"3-2\">菜单3-2</abc-menu-item>\n        </abc-sub-menu>\n      </abc-menu>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      value: '1',\n    }\n  },\n  methods: {\n    selectItem(item) {\n      console.log('item', item)\n    },\n  },\n}\n</script>", "props": [{"name": "value", "default": ""}, {"name": "badgeVariant", "description": "徽标变体 text 文字徽标 round 圆形徽标 dot 点状徽标 count文字汇总徽标\ntext 不设置theme文本颜色默认继承父元素颜色", "default": "'round'"}, {"name": "variant", "description": "变体 default 普通左侧菜单; clinic 诊所菜单(诊所菜单无二级菜单)", "default": "'default'"}, {"name": "theme", "description": "主题 light 普通主题 dark 暗色主题", "default": "'light'"}, {"name": "size", "description": "尺寸 large 大尺寸; small 小尺寸", "default": "'large'"}, {"name": "bottomSafeHeight", "description": "底部安全高度", "default": "0"}, {"name": "clickNeedCollapseOtherMenu", "description": "是否自动关闭其他menu菜单", "default": "false"}], "slots": ["default"], "events": ["click", "input", "change"]}