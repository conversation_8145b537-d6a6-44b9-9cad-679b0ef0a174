{"name": "AbcModal", "description": "模态框组件，常勇于展示提示信息、确认信息、警告信息等，内置了 alert、confirm、message、step 四种预设类型，且支持函数式调用", "usage": "<template> <div> <abc-button @click=\"handleShowModal\" data-cy=\"storybook-test-modal-open-btn\" >显示 info alert modal</abc-button > </div> </template> <script> export default { methods: { handleShowModal() { this.message = this.$modal({ type: 'warn', preset: 'alert', title: '删除提示', content: '科室中已有成员，需要将成员移除科室才可删除！', }) }, }, } </script>", "props": [{"name": "value", "description": "组件形式调用是否展示modal", "default": "false"}, {"name": "type", "description": "类型影响icon显示\n<br/>目前支持 alert confirm", "values": ["success", "warn", "info", "loading"], "default": "'info'"}, {"name": "size", "description": "同dialog size属性\n<br/>目前支持 alert confirm", "default": "'default'"}, {"name": "responsive", "description": "是否响应式，响应式规则：\nwidth: 80vw，当 size 指定为 hugely 时，宽度固定为 1200px\nheight: 90vh 当 size 指定为 hugely 时，宽度固定为 76vh\nmin-width: 1200px\n弹窗中心点，距离顶部 50%", "default": "false"}, {"name": "preset", "description": "预设值影响modal交互方式和按钮布局", "values": ["alert", "confirm", "message", "step"], "default": "''"}, {"name": "title", "description": "标题", "default": "''"}, {"name": "content", "description": "内容区域 支持数组，字符串，vNode"}, {"name": "footerPrepend", "description": "footerPrepend区域 支持数组，字符串，vNode"}, {"name": "showIcon", "description": "是否展示icon", "default": "false"}, {"name": "customClass", "description": "给弹窗的自定义class，会设置到dialog上", "default": "''"}, {"name": "dialogContentStyles", "description": "给 modal 的 dialog 设置自定义样式", "default": "''"}, {"name": "contentStyles", "description": "给modal-content-wrapper 设置自定义样式", "default": "''"}, {"name": "confirmText", "description": "确认按钮文案", "default": "''"}, {"name": "confirmButtonType", "description": "确认按钮类型", "default": "'primary'"}, {"name": "cancelText", "description": "取消按钮文案", "default": "''"}, {"name": "closeAfterConfirm", "description": "confirm 后是否关闭modal", "default": "true"}, {"name": "needHighLevel", "description": "兼容字段，目前废弃", "default": "true"}, {"name": "showConfirm", "description": "是否展示 确定 按钮", "default": "true"}, {"name": "showCancel", "description": "是否展示 取消 按钮", "default": "true"}, {"name": "showClose", "description": "是否展示 右上角 关闭x", "default": "true"}, {"name": "disabledKeyboard", "description": "禁用弹窗键盘事件", "default": "false"}, {"name": "showFooter", "description": "是否展示底部区域", "default": "true"}, {"name": "onConfirm", "description": "确认按钮被点击"}, {"name": "confirmLoading", "description": "确认按钮loading", "default": "false"}, {"name": "onCancel", "description": "取消按钮被点击"}, {"name": "onClose", "description": "关闭弹窗回调"}, {"name": "noDialogAnimation", "default": "false"}, {"name": "dialogType", "default": "'normal'"}, {"name": "successAnimation", "default": "false"}, {"name": "appendToBody", "description": "是否挂载到 Body 下", "default": "false"}, {"name": "autoFocus", "description": "对于内容有input可设置首个input 自动focus", "default": "false"}, {"name": "beforeClose", "description": "关闭前回调", "default": "null"}, {"name": "scrollbarPaddingSize", "description": "none 0 default 24px; small: 16px; tiny: 10px", "default": "'default'"}], "slots": ["top-extend", "step", "default", "footerPrepend"], "events": ["open", "input"]}