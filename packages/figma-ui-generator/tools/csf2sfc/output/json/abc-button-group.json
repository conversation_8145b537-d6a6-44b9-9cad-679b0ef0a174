{"name": "AbcButtonGroup", "description": "", "usage": "<template> <abc-flex vertical gap=\"large\"> <abc-button-group data-cy=\"storybook-test-button-group\"> <abc-button>按钮 1</abc-button> <abc-button>按钮 2</abc-button> <abc-button>按钮 3</abc-button> <abc-dropdown placement=\"bottom-end\"> <abc-button slot=\"reference\" icon=\"n-down-line-medium\" width=\"24\" min-width=\"24\" ></abc-button> <abc-dropdown-item>操作一</abc-dropdown-item> <abc-dropdown-item>操作二</abc-dropdown-item> <abc-dropdown-item>操作三</abc-dropdown-item> </abc-dropdown> </abc-button-group> <abc-button-group> <abc-button theme=\"success\">按钮 1</abc-button> <abc-button theme=\"success\">按钮 2</abc-button> <abc-button theme=\"success\">按钮 3</abc-button> <abc-dropdown placement=\"bottom-end\"> <abc-button slot=\"reference\" theme=\"success\" icon=\"n-down-line-medium\" width=\"24\" min-width=\"24\" ></abc-button> <abc-dropdown-item>操作一</abc-dropdown-item> <abc-dropdown-item>操作二</abc-dropdown-item> <abc-dropdown-item>操作三</abc-dropdown-item> </abc-dropdown> </abc-button-group> <abc-button-group> <abc-button variant=\"outline\">按钮 1</abc-button> <abc-button variant=\"outline\">按钮 2</abc-button> <abc-button variant=\"outline\">按钮 3</abc-button> <abc-dropdown placement=\"bottom-end\"> <abc-button slot=\"reference\" variant=\"outline\" icon=\"n-down-line-medium\" width=\"24\" min-width=\"24\" ></abc-button> <abc-dropdown-item>操作一</abc-dropdown-item> <abc-dropdown-item>操作二</abc-dropdown-item> <abc-dropdown-item>操作三</abc-dropdown-item> </abc-dropdown> </abc-button-group> <abc-button-group> <abc-button variant=\"ghost\">按钮 1</abc-button> <abc-button variant=\"ghost\">按钮 2</abc-button> <abc-button variant=\"ghost\">按钮 3</abc-button> <abc-dropdown placement=\"bottom-end\"> <abc-button slot=\"reference\" variant=\"ghost\" icon=\"n-down-line-medium\" width=\"24\" min-width=\"24\" ></abc-button> <abc-dropdown-item>操作一</abc-dropdown-item> <abc-dropdown-item>操作二</abc-dropdown-item> <abc-dropdown-item>操作三</abc-dropdown-item> </abc-dropdown> </abc-button-group> <abc-button-group> <abc-button variant=\"ghost\" theme=\"default\">按钮 1</abc-button> <abc-button variant=\"ghost\" theme=\"default\">按钮 2</abc-button> <abc-button variant=\"ghost\" theme=\"default\">按钮 3</abc-button> <abc-dropdown placement=\"bottom-end\"> <abc-button slot=\"reference\" variant=\"ghost\" theme=\"default\" icon=\"n-down-line-medium\" width=\"24\" min-width=\"24\" ></abc-button> <abc-dropdown-item>操作一</abc-dropdown-item> <abc-dropdown-item>操作二</abc-dropdown-item> <abc-dropdown-item>操作三</abc-dropdown-item> </abc-dropdown> </abc-button-group> <abc-button-group> <abc-button variant=\"ghost\" theme=\"default\" icon=\"n-add-line-medium\" ></abc-button> <abc-button variant=\"ghost\" theme=\"default\" icon=\"n-reduce-line-medium\" ></abc-button> </abc-button-group> <abc-button-group> <abc-button variant=\"ghost\" theme=\"default\" icon=\"n-reduce-line-medium\" ></abc-button> </abc-button-group> </abc-flex> </template> <script> export default {} </script>", "slots": ["default"]}