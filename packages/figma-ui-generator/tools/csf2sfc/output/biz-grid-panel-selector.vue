<template>
  <abc-space direction="vertical" align="left">
    <abc-checkbox v-model="editable">可编辑</abc-checkbox>
    <abc-checkbox v-model="disabled">禁用</abc-checkbox>
    <biz-grid-panel-selector
      v-model="selectValue"
      placeholder="用法"
      :width="206"
      label-field="label"
      value-field="value"
      :data-source="options"
      :editable="editable"
      :disabled="disabled"
    ></biz-grid-panel-selector>
    <abc-button @click="submit">确定</abc-button>
  </abc-space>
</template>
<script>
export default {
  data() {
    return {
      selectValue: '测一',
      editable: false,
      disabled: false,
      options: [
        {
          title: '',
          // 非必须
          list: [
            {
              label: '测一',
              value: '测一',
            },
            {
              label: '测二',
              value: '测二',
            },
            {
              label: '测三',
              value: '测三',
            },
            {
              label: '测四',
              value: '测四',
            },
            {
              label: '测五',
              value: '测五',
            },
            {
              label: '测六',
              value: '测六',
            },
            {
              label: '测七',
              value: '测七',
            },
          ],
        },
      ],
    }
  },
  methods: {
    submit() {
      console.log(this.selectValue)
    },
  },
}
</script>
