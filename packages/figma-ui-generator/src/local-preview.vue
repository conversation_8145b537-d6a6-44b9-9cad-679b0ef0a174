<template>
  <AbcDialog v-model="showDialog" size="xlarge" showHeaderBorderBottom title="长护登记">
    <template #default>
      <AbcFlex vertical justify="flex-start" align="flex-start" gap="large" style="padding: 24px 24px 24px 24px">
        <AbcFlex vertical justify="flex-start" align="flex-start" gap="large">
          <AbcFlex vertical justify="flex-start" align="flex-start" gap="middle">
            <biz-patient-selector v-model="patient" states="default" size="default"></biz-patient-selector>
            <AbcDescriptions bordered borderStyle="solid" labelWidth="42" labelStyle contentStyle contentPadding="0">
              <!-- Descriptions content would go here -->
            </AbcDescriptions>
          </AbcFlex>
          <AbcTabsV2 v-model="activeTab" :options="tabOptions" size="middle"></AbcTabsV2>
          <AbcForm labelPosition="top" labelAlign="left" marginSize="large" itemBlock>
            <AbcFormItemGroup grid gridColumnCount="3" isExcel>
              <AbcFormItem label="状态" type="input" gridColumn="span 1" help>
                <AbcInput placeholder="未登记" clearable width="228"></AbcInput>
              </AbcFormItem>
              <AbcFormItem label="申请类型" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="服务类型" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="结算类型" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="气管切开" type="radioButton" gridColumn="span 1" help>
                <AbcRadioGroup>
                  <AbcFlex style="width: 100%" justify="center" align="center" gap="10" padding="0px 12px 0px 12px">
                    <AbcText>否</AbcText>
                  </AbcFlex>
                  <AbcFlex style="width: 100%" justify="center" align="center" gap="10" padding="0px 12px 0px 12px">
                    <AbcText>是</AbcText>
                  </AbcFlex>
                </AbcRadioGroup>
              </AbcFormItem>
              <AbcFormItem label="评估等级" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="业绩医生" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择业绩医生" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="责任医生" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择责任医生" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="责任护士" type="select" gridColumn="span 1" help>
                <AbcSelect placeholder="请选择责任护士" states="default" width="228">
                  <abc-option v-for="i in 5" :label="'选项' + i" :value="'选项' + i"></abc-option>
                </AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="责任护理员" type="select" gridColumn="span 1" help>
                <AbcSelect></AbcSelect>
              </AbcFormItem>
              <AbcFormItem label="诊断" type="input" gridColumn="span 2" help>
                <AbcInput placeholder="请输入" clearable width="480"></AbcInput>
              </AbcFormItem>
            </AbcFormItemGroup>
          </AbcForm>
        </AbcFlex>
      </AbcFlex>
    </template>
    <template #footer>
      <AbcFlex justify="flex-start" align="center" gap="12" style="width: 100%; padding: 12px 24px 12px 24px">
        <AbcFlex justify="flex-start" align="center" gap="middle" style="width: 100%"></AbcFlex>
        <AbcFlex justify="flex-end" align="center" gap="middle" style="width: 100%">
          <AbcSpace direction="horizontal" size="small">
            <AbcButton shape="square" variant="fill" theme="primary" size="normal" disabled="false">登记</AbcButton>
            <AbcButton shape="square" variant="ghost" theme="primary" size="normal" disabled="false">取消</AbcButton>
          </AbcSpace>
        </AbcFlex>
      </AbcFlex>
    </template>
  </AbcDialog>
</template>
<script>

export default {
  data() {
    return {
      showDialog: true,
      patient: {
        id: "",
        name: "",
        sex: "",
        age: { year: null, month: null, day: null },
        birthday: "",
        created: "",
        mobile: "",
        countryCode: "",
        idCard: "",
        idCardType: "身份证",
        sn: "",
        shebaoCardInfo: null
      },
      activeTab: 'tab1',
      tabOptions: [
        { label: '标签1', value: 'tab1' },
        { label: '标签2', value: 'tab2' },
        { label: '标签3', value: 'tab3' }
      ]
    };
  }
};
</script>