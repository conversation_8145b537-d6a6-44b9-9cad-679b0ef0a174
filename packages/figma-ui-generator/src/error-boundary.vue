<template>
  <div v-if="error" :style="{
                color: '#f56c6c',
                padding: '20px',
                background: '#fef0f0',
                borderRadius: '4px',
                border: '1px solid #fde2e2',
                margin: '10px',
                maxWidth: '100%',
                overflow: 'auto'
              }">
    <h3 style="margin: 0 0 10px 0;">组件渲染错误</h3>
    <p style="margin: 5px 0; font-weight: bold;">{{ error.toString() }}</p>
    <pre
        style="max-height: 200px; overflow: auto; background: #f5f5f5; padding: 10px; border-radius: 4px; margin: 10px 0; white-space: pre-wrap;">{{
        error.stack || '无堆栈信息'
      }}</pre>
  </div>
  <div v-else>
    <slot></slot>
  </div>
</template>
<script>
export default {
  name: "error-boundary",
  data: () => ({
    error: null,
    errorInfo: null
  }),
  errorCaptured(err, vm, info) {
    this.error = err;
    this.errorInfo = info;
    console.log('wrappedComponent errorCaptured', this.error, this.errorInfo);
    return false; // 阻止错误继续向上传播
  },
}
</script>