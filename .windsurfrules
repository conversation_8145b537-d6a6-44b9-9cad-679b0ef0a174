# 介绍
这是一个 figma plugin 工程，用于选中设计稿，将设计稿中的元素，转换为私有组件库 ABC UI 的描述信息。转换后的信息，将用于给 LLM 生成代码。
该工程是一个 pnpm monorepo，包含如下子工程：
- @abc-figma/figma-ui-generator: 转换工具，位于 packages/figma-ui-generator
- @abc-figma/vue-playground: vue2.7 的演示项目，位于 packages/vue-playground

## ABC UI
ABC UI 是公司内部封装的基于 Vue2 的前端组件库，用于快速构建 UI，目前的组件有：
- AbcButton: 一个按钮组件，支持多种变体和图标。
- AbcBadge: 一个徽章组件，用于显示数字和红点。
- AbcAutocomplete: 一个自动完成组件，支持异步数据获取和自定义建议列表。
- AbcButtonGroup: 一个按钮组组件，用于将多个按钮组合在一起。
- AbcButtonPagination: 一个按钮分页组件，用于分页显示数据。
- AbcCard: 一个卡片组件，用于展示内容和操作。
- AbcCascader: 一个级联选择器组件，用于选择多级数据。
- AbcCheckbox: 一个复选框组件，用于多选操作。
- AbcCheckboxButton: 一个复选按钮组件，用于多选操作。
- AbcContentEmpty: 一个内容为空的占位组件，用于在数据为空时展示。
- AbcWeekPagination: 用于周区间选择。
- AbcDatePicker: 日期选择器。
- AbcTimePicker: 时间选择器。
- AbcTimeRangePicker: 时间范围选择器。
- AbcDateTimePicker: 日期时间选择器。
- AbcDescriptions: 一个描述列表组件，用于展示成组的数据。
- AbcDialog: 一个对话框组件，用于展示弹出式内容。
- AbcDivider: 一个分割线组件，用于分隔内容。
- AbcDropdown: 一个下拉菜单组件，用于展示可选择的选项列表。
- AbcEditDiv: 一个可编辑的文本区域组件，支持多种配置选项。
- AbcFlex: Flex 布局组件，通常用于在一行或者一列排列子元素，默认横向布局，使用vertical可纵向布局，通过gap可设置间距
- AbcForm: 一个表单组件，支持表单验证和提交。
- AbcGrid: 栅格系统基于 24 栅格，将页面水平等分为 24 格，使用 AbcRow 和 AbcCol 结合，可以有效的保证页面的一致性、逻辑性。
- AbcIcon: 一个图标组件，支持自定义颜色和大小。
- AbcImage: 一个图片组件，支持设置默认图片、加载中状态、加载失败状态以及图片填充方式。
- AbcInput: 一个基础的输入框组件。
- AbcInputMobile: 一个手机号码输入框，左侧带国家区号选择
- AbcInputNumber: 一个数字输入框组件。
- AbcLayout: 一组布局容器组件，包含AbcLayout, AbcLayoutHeader, AbcLayoutContent, AbcLayoutFooter，AbcLayoutSidebar
- AbcLink: 超链接，跳转到目标地址。
- AbcList: 列表组件，用于展示结构化的数据。
- AbcLoading: 加载状态组件，用于覆盖页面或页面中的一部分，提供加载状态的反馈。
- AbcModal: 模态框组件，用于显示重要的提示信息、确认操作或进行内容输入。
- AbcPagination: 分页组件，用于将大量数据进行分页展示。
- AbcPopover: 弹出框组件，用于在鼠标悬停或点击时显示隐藏的内容。
- AbcRadio: 单选框组件，用于在多个选项中选择一个。
- AbcRadioButton: 单选按钮组件，用于在多个选项中选择一个，常以卡片形式展示。
- AbcSelect: 下拉选择器，用于从下拉列表中选择一个或多个选项。
- AbcSpace: 用于在一系列组件之间添加水平或垂直间距的布局组件，通过size设置间距：small,middle,large，或者自定义数值
- AbcStatistic: 统计数值的容器组件，大部分场景需要使用 variant="outline"，呈现边框。
- AbcSteps: 步骤条组件，用于引导用户完成一系列步骤。
- AbcSwitch: 开关选择器。
- AbcTable: 用于展示数据列表的表格组件。当表格带筛选栏时，需要配合 AbcLayout 使用，并将 AbcTable 放在 AbcLayoutContent 中，将筛选控件放在 AbcLayoutHeader 中
- AbcTabsV2: 标签页组件，用于在一个容器中展示多个内容面板，每个面板对应一个标签。
- AbcTagV2: 一个标签组件，用于显示状态或标签等。主题包含：'default', 'primary', 'warning', 'danger', 'success'
- AbcText: 用于显示文本的组件。
- AbcTextarea: 多行文本输入框组件。
- AbcTips: 全局展示操作反馈信息。
- AbcTipsCardV2: 卡片形式的提示信息。
- AbcTooltip: 简单的提示工具。
- AbcTooltipInfo: 默认带 info 图标的提示工具。
- AbcTransferV2: 穿梭框，可进行单项或双向选择。
- AbcTreeV2: 树形控件。