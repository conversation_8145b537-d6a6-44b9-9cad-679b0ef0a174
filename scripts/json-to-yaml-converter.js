#!/usr/bin/env node

/**
 * JSON转YAML转换脚本
 * 
 * 此脚本将abc-ui目录下的所有JSON文件转换为YAML格式，并保存到yaml-output目录中
 * 使用方法: node json-to-yaml-converter.js
 */

const fs = require('fs');
const path = require('path');
const yaml = require('js-yaml');

// 预处理对象，修复正则表达式中的转义问题
function preprocessObject(obj) {
  if (typeof obj === 'string') {
    // 对于包含正则表达式的字符串，将 \d, \w, \s 等转义序列转换为 \\d, \\w, \\s
    return obj.replace(/\\([dwsWDSbBnrtfv])/g, '\\\\$1');
  } else if (Array.isArray(obj)) {
    return obj.map(item => preprocessObject(item));
  } else if (obj && typeof obj === 'object') {
    const result = {};
    for (const [key, value] of Object.entries(obj)) {
      result[key] = preprocessObject(value);
    }
    return result;
  }
  return obj;
}

// 使用 js-yaml 库进行 JSON 到 YAML 的转换
function jsonToYaml(obj) {
  try {
    // 预处理对象，修复正则表达式转义问题
    const processedObj = preprocessObject(obj);
    
    // 使用 js-yaml 库进行转换，配置选项确保正确处理特殊字符
    return yaml.dump(processedObj, {
      indent: 2,
      lineWidth: -1, // 不限制行宽，避免长字符串被截断
      noRefs: true,  // 不使用引用，确保输出简洁
      quotingType: '"', // 使用双引号
      forceQuotes: false, // 只在必要时使用引号
      flowLevel: -1, // 使用块级样式而不是流式样式
    });
  } catch (error) {
    console.error('YAML 转换失败:', error);
    return '';
  }
}

// 将组件名从 kebab-case 转换为 PascalCase
function kebabToPascalCase(str) {
  return str
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('');
}

// 主函数
async function main() {
  // 定义源目录和目标目录
  const sourceDir = path.resolve(__dirname, '../references/abc-ui');
  const targetDir = path.resolve(__dirname, '../references/yaml-output');
  
  console.log('开始转换JSON到YAML...');
  console.log(`源目录: ${sourceDir}`);
  console.log(`目标目录: ${targetDir}`);
  
  // 确保目标目录存在
  if (!fs.existsSync(targetDir)) {
    console.log(`创建目标目录: ${targetDir}`);
    fs.mkdirSync(targetDir, { recursive: true });
  }
  
  // 读取源目录中的所有文件
  const files = fs.readdirSync(sourceDir);
  
  // 过滤出JSON文件
  const jsonFiles = files.filter(file => file.endsWith('.json'));
  console.log(`找到 ${jsonFiles.length} 个JSON文件需要转换`);
  
  // 处理每个JSON文件
  for (const jsonFile of jsonFiles) {
    try {
      const filePath = path.join(sourceDir, jsonFile);
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const jsonData = JSON.parse(fileContent);
      
      // 转换为YAML格式
      const yamlContent = jsonToYaml(jsonData);
      
      // 生成目标文件名（将.json替换为.yaml）
      const targetFileName = jsonFile.replace('.json', '.yaml');
      const targetFilePath = path.join(targetDir, targetFileName);
      
      // 获取组件名和Pascal格式名称（用于日志）
      const componentName = jsonFile.replace('.json', '');
      const pascalName = kebabToPascalCase(componentName);
      
      // 写入YAML文件
      fs.writeFileSync(targetFilePath, yamlContent, 'utf8');
      
      console.log(`✅ 已转换: ${componentName} -> ${pascalName} (${targetFileName})`);
    } catch (error) {
      console.error(`❌ 转换失败: ${jsonFile}`, error.message);
    }
  }
  
  console.log('转换完成!');
  console.log(`YAML文件已保存到: ${targetDir}`);
}

// 执行主函数
main().catch(error => {
  console.error('脚本执行失败:', error);
  process.exit(1);
});
